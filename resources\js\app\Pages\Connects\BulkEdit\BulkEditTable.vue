<template>
  <div class="flex px-5 py-3 border-b border-grayCust-160 sticky top-[59px] bg-white z-[5]">
    <CommonTab
      v-model:selected-tab="isSelectedTab"
      :tab-list="tabList"
      btn-type="gray"
      @update:selected-tab="onHandleSelectTab"
    />
  </div>
  <CommonTable
    :fields="tableHeaderData"
    :items="{ data: tabData }"
    :responsive="true"
    :busy="initialLoading"
    row-bordered
    row-height="lg:h-[40px]"
    header-height="lg:h-10"
  >
    <template #cell_items="{ item }">
      <div class="text-left text-sm font-medium text-grayCust-800 flex items-center gap-2">
        <!-- <img v-if="isSelectedTab == 2" :src="cdn(`images/BulkEdit/${item.itemIconName}`)" class="w-6 h-6 ring-4 ring-offset-1 ring-grayCust-75 rounded-full" alt="" > -->
        <ConnectPluginImage
          v-if="isSelectedTab == 3"
          class="size-6"
          :slug="item.slug"
        />
        <h4
          v-if="isSelectedTab == 2"
          class="max-w-[150px] truncate sm:max-w-[200px] sm2:max-w-[280px]"
        >
          {{ item.slug }}
        </h4>
        <h4
          v-if="isSelectedTab == 3"
          class="max-w-[150px] truncate sm:max-w-[200px] sm2:max-w-[280px]"
        >
          {{ item.display_name }}
        </h4>
      </div>
    </template>
    <template #cell_sites_updates="{ item }">
      <div class="text-start text-sm font-normal text-grayCust-800 flex items-center gap-2">
        <p class="mr-1 block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
          {{ $t('updates') }}:
        </p>
        <div class="space-y-1">
          <div class="flex cursor-pointer items-center gap-1">
            <h4 class="max-w-[120px] truncate text-sm text-grayCust-800">
              {{ item.update_count }} {{ $t('require_updates') }}
            </h4>
            <!-- <SyncIcon class="w-4 h-4 text-grayCust-500 animate-spin" v-if="item.updating"/> -->
            <!-- <sync-icon
              v-if="item.updating"
              class="animate-spin text-grayCust-500"
            /> -->
            <OutlineRefreshIcon
              v-if="item.updating"
              class="w-5 h-5 text-grayCust-500 animate-spin"
            />
          </div>
          <p class="text-[10px] text-grayCust-500">
            {{ $t('out_of_num_sites', { num: item.item_count }) }}
          </p>
        </div>
      </div>
    </template>
    <template #cell_actions="{ item }">
      <CommonActionButtonGroup>
        <CButton
          :disabled="item.updating || item.update_count == 0"
          icon-name="OutlineRefreshIcon"
          btn-type="gray-outline-btn"
          :btn-title="$t('update_all')"
          icon-color="text-grayCust-980"
          @click="bulkUpdate(item, 'update')"
        />
        <CPopover button-class="!p-0 rounded-lg overflow-hidden">
          <template #popoverButton>
            <IconButton
              v-tooltip="$t('more_act')"
              icon-name="OutlineDotsVerticalIcon"
              btn-type="gray-outline-btn"
              icon-color="text-grayCust-980"
              extra-class="!rounded-lg"
            />
          </template>
          <template #popoverOptions>
            <CommonMenuItem
              :label="$t('delete') + ' ' + item.type + 's'"
              icon="OutlineTrashIcon"
              @click="bulkUpdate(item, 'delete')"
            />
          </template>
        </CPopover>
      </CommonActionButtonGroup>
    </template>
    <!-- <template #extra>
        <div class="flex items-center justify-center gap-2 text-left whitespace-nowrap text-sm text-grayCust-800">
          <p class="block lg:hidden text-sm font-medium mr-1 text-grayCust-800 uppercase">
            {{ $t('type') }}:
          </p>
          <p>{{ item.typeTitle }}</p>
          <p>wp.org</p>
        </div>
      </template> -->
  </CommonTable>
  <div
    v-if="isSelectedTab == 2 || isSelectedTab == 3"
    class="flex justify-end py-4"
  >
    <CommonPagination
      v-if="paginationData.total > 0 && paginationData.lastPage > 1"
      v-model:current-page="paginationData.currentPage"
      v-model:per-page="perPageValue"
      :resource-name="paginationData.resourceName"
      :total="paginationData.total"
      @page-change="(page) => getThemePluginList(page, paginationData.type)"
      @per-page-change="handlePerPageChange"
    />
  </div>
</template>

<script>
import CommonMenuItem from '@/app/Common/ActionDropdown/CommonMenuItem.vue';
import CommonPagination from "@/app/Common/CommonPagination.vue";
import CommonTab from "@/app/Common/CommonTab.vue";
import CommonTable from "@/app/Common/CommonTable.vue";
import ConnectPluginImage from "@/app/Pages/Connects/BulkEdit/ConnectPluginImage.vue";
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { wTrans } from "laravel-vue-i18n";
import { mapState, mapStores } from 'pinia';

export default {
  name: 'ConnectBulkEdit',
  components: {
    CommonTab,
    CommonTable,
    CommonPagination,
    ConnectPluginImage,
    CommonMenuItem
  },
  data() {
    return {
      initialLoading: true,
      isSelectedTab: 2,
      tabList: [
        // {
        //     id: 1,
        //     iconName: 'CoreIcon',
        //     title: 'Core'
        // },
        {
          id: 2,
          iconName: 'OutlinePuzzleIcon',
          title: 'Themes'
        },
        {
          id: 3,
          iconName: 'OutlineCogIcon',
          title: 'Plugins'
        }
      ],
      tableHeaderData: [
        {
          key: 'items',
          label: wTrans('items'),
          headerClass: 'w-1/2'
        },
        {
          key: 'sites_updates',
          label: wTrans('sites_updates')
        },
        {
          key: 'actions',
          label: wTrans('actions')
        },
      ],
      tableDataList: [
        // {
        //     id: 1,
        //     itemIconName: 'site-icon.svg',
        //     itemTitle: 'Wp Forms',
        //     typeTitle: 'wp.org'
        // },
        // {
        //     id: 2,
        //     itemIconName: 'wg-icon.svg',
        //     itemTitle: 'Contact Forms 7',
        //     typeTitle: 'custom'
        // },
        // {
        //     id: 3,
        //     itemIconName: 'site-icon.svg',
        //     itemTitle: 'Wp Forms',
        //     typeTitle: 'wp.org'
        // },
        // {
        //     id: 4,
        //     itemIconName: 'site-icon.svg',
        //     itemTitle: 'Wp Forms',
        //     typeTitle: 'custom'
        // },
        // {
        //     id: 5,
        //     itemIconName: 'site-icon.svg',
        //     itemTitle: 'Wp Forms',
        //     typeTitle: 'custom'
        // },
        // {
        //     id: 6,
        //     itemIconName: 'site-icon.svg',
        //     itemTitle: 'Wp Forms',
        //     typeTitle: 'wp.org'
        // },
      ],
      connectThemePluginDetails: [],
      perPageValue: '10'
    }
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ["user"]),
    tabData() {
      const tabDataMap = {
        1: [],
        2: this.connectThemePluginDetails?.themes?.data || [],
        3: this.connectThemePluginDetails?.plugins?.data || []
      };

      return tabDataMap[this.isSelectedTab] || [];
    },
    paginationData() {
      const dataMap = {
        2: {
          resourceName: this.$t('themes'),
          currentPage: this.connectThemePluginDetails?.themes?.current_page,
          lastPage: this.connectThemePluginDetails?.themes?.last_page,
          total: this.connectThemePluginDetails?.themes?.total,
          type: 'theme'
        },
        3: {
          resourceName: this.$t('plugins'),
          currentPage: this.connectThemePluginDetails?.plugins?.current_page,
          lastPage: this.connectThemePluginDetails?.plugins?.last_page,
          total: this.connectThemePluginDetails?.plugins?.total,
          type: 'plugin'
        }
      };

      return dataMap[this.isSelectedTab] || {};
    }
  },
  mounted() {
    this.getThemePluginList()
    window.Echo.channel(`connect.theme_plugin.updated.${this.user.current_team_id}`)
      .listen('ConnectThemePluginUpdated', (e) => {
        if (this.appStore.selectAllConnectBulkEdit == false) {
          if (this.appStore.connectBulkSelection.includes(e.connectId) == false) {
            return false
          }
        }

        if (e.error) {
          const notification = {
            heading: this.$t('error'),
            subHeading: e.error,
            type: "error",
          }
          this.appStore.setNotification(notification);
        }

        const type = e.type + 's'
        const index = this.connectThemePluginDetails[type].data.findIndex(i => {
          if (e.type == 'theme') {
            return i.type == e.type && i.slug == e.slug
          } else {
            return i.type == e.type && i.name == e.slug
          }
        })
        if (index != -1) {
          if (this.connectThemePluginDetails[type].data[index].update_count > 0) {
            this.connectThemePluginDetails[type].data[index].update_count--;
          }
          if (this.connectThemePluginDetails[type].data[index].update_count == 0) {
            this.connectThemePluginDetails[type].data[index].updating = false
          }
        }
      })
  },
  unmounted() {
    window.Echo.leave(`connect.theme_plugin.updated.${this.user.current_team_id}`);
  },
  methods: {
    onHandleSelectTab(id) {
      this.isSelectedTab = id
    },
    getThemePluginList(page = 1, type = null) {
      let payload = {}
      if (this.appStore.selectAllConnectBulkEdit) {
        payload = { select_all: true }
      } else {
        payload = { connect_ids: this.appStore.connectBulkSelection }
      }
      payload.page = page
      payload.per_page = this.perPageValue
      axios.post('/api/v2/connects/get-bulk-update-data', payload)
        .then(response => {
          if (type == 'theme') {
            this.connectThemePluginDetails.themes = response.data.data.themes
          } else if (type == 'plugin') {
            this.connectThemePluginDetails.plugins = response.data.data.plugins
          } else {
            this.connectThemePluginDetails = response.data.data
          }
        })
        .catch(error => {
          console.log(error)
        })
        .finally(() => {
          this.initialLoading = false
        })
    },
    bulkUpdate(connectThemePluginDetails, action) {
      connectThemePluginDetails.updating = true
      let payload = {}
      if (this.appStore.selectAllConnectBulkEdit) {
        payload = { select_all: true }
      } else {
        payload = { connect_ids: this.appStore.connectBulkSelection }
      }

      if (connectThemePluginDetails.type == 'theme') {
        payload.type = 'theme'
        payload.slug = connectThemePluginDetails.slug
      } else {
        payload.type = 'plugin'
        payload.slug = connectThemePluginDetails.name
      }
      payload.action = action
      axios.post('/api/v2/connects/bulk-update-connects', payload)
        .then(response => {
          const notification = {
            heading: this.$t('success'),
            subHeading: response.data.message,
            type: "success",
          };
          this.appStore.setNotification(notification);
        })
        .catch(() => {

        })
    },
    setTabType(tabType) {
      this.tabType = tabType;
    },
    handlePerPageChange(value) {
      this.perPageValue = value;
      this.getThemePluginList(1, this.paginationData.type);
    },
  }
};
</script>