<template>
  <div>
    <Head>
      <title>Setup - {{ setting.brandShortName }}</title>
    </Head>
    <TemplateLayout
      :template="templateData"
      page="setup"
      title="Setup"
      sub-title="Add details, convert to instant template, or add post-creation commands"
    >
      <div class="card-main overflow-y-auto overflow-x-hidden rounded-md bg-white">
        <div class="p-4">
          <div class="custom-form-width space-y-4">
            <div class="template-wrapper">
              <label
                for="email"
                class="form-label"
              >
                {{ $t('template_name') }}
              </label>
              <div class="mt-2">
                <CInput
                  v-model="form.name"
                  :placeholder="$t('enter_name')"
                  extra-class="w-full"
                  @keyup="validateName"
                />
              </div>
              <div
                v-for="(nameError, index) in serverErrors?.name"
                :key="index"
                class="mt-2 text-xs text-red-700"
              >
                {{ nameError
                }}
              </div>
            </div>

            <div>
              <label
                for="Description"
                class="form-label"
              >
                {{ $t('description') }}
              </label>
              <div class="mt-1">
                <textarea
                  id="comment"
                  v-model="form.description"
                  rows="4"
                  name="comment"
                  class="form-control w-full"
                />
              </div>
              <div
                v-for="(descriptionError, index) in serverErrors?.description"
                :key="index"
                class="mt-2 text-xs text-red-700"
              >
                {{
                  descriptionError }}
              </div>
            </div>

            <div class="mt-6 flex flex-col">
              <div
                v-if="!teamCan?.instant_template"
                class="mb-2 flex justify-end"
              >
                <div
                  class="float-right flex cursor-pointer pb-1 text-left text-xs font-medium text-gray-900"
                  @click="showUpgradeWarning('instant_template')"
                >
                  <span>{{ $t('upgrade_unlock') }}</span>
                  <solidLockClosedIcon class="ml-1 size-4 text-gray-700" />
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="">
                  <span class="mr-5 form-label">
                    {{ $t('instant_template') }}
                  </span>
                </span>
                <CommonSwitch
                  id="template_setup_instant_switch"
                  v-model="form.instant_template"
                  :disabled="!teamCan?.instant_template"
                  size="md"
                />
              </div>
            </div>
            <div class="mt-4">
              <div class="flex justify-between">
                <label
                  for="post_creation_command"
                  class="form-label mb-2"
                >Post-Creation Commands</label>
                <div
                  v-if="!teamCan?.advance_template_options"
                  class="mb-2 flex justify-end"
                >
                  <div
                    class="float-right flex cursor-pointer pb-1 text-left text-xs font-medium text-gray-900"
                    @click="showUpgradeWarning('advance_template_options')"
                  >
                    <span>{{ $t('upgrade_unlock') }}</span>
                    <solidLockClosedIcon class="ml-1 size-4 text-gray-700" />
                  </div>
                </div>
              </div>
              <div class="mt-1 text-left sm:col-span-2 sm:mt-0">
                <textarea
                  id="post_creation_command"
                  v-model="form.post_creation_command"
                  :disabled="!teamCan?.advance_template_options"
                  rows="5"
                  placeholder="Enter Commands"
                  :maxlength="max_char"
                  name="post_creation_command"
                  class="mb-3 block w-full rounded-md border-gray-300 bg-black text-white shadow-sm focus:border-transparent focus:ring-transparent sm:text-sm"
                />
                <label
                  for="post_creation_command"
                  class="mb-2 block text-right form-label sm:mt-px"
                >Characters
                  limit <span v-text="form.post_creation_command.length" /> / {{
                    max_char }}</label>
              </div>
            </div>
            <div
              v-if="form.instant_template && user.can_set_template_pool_number"
              class="template-wrapper"
            >
              <label
                for="max_instant_pool_count"
                class="block form-label"
              >
                {{ $t('max_instant_pool_count') }}
              </label>
              <div class="mt-1">
                <input
                  id="max_instant_pool_count"
                  v-model="max_instant_pool_count"
                  min="1"
                  type="number"
                  autocomplete="off"
                  class="mt-2 block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus:border-primary-900 focus:outline-none  focus:ring-1 focus:ring-primary-900"
                >
              </div>
              <div
                v-for="(error, index) in serverErrors?.max_instant_pool_count"
                :key="index"
                class="mt-2 text-xs text-red-700"
              >
                {{ error }}
              </div>
            </div>
            <div
              v-if="!form.is_monetized"
              class="mt-6 flex items-center justify-between"
            >
              <span class="">
                <label class="mr-5 form-label">
                  {{ $t('select_design') }}
                </label>
              </span>
              <div class="flex justify-center pt-4 text-center">
                <div class="mr-2 flex items-center">
                  <input
                    id="v1"
                    v-model="form.launch_version"
                    :disabled="form.is_monetized"
                    type="radio"
                    value="v1"
                    class="ml-1 size-4 border-primary-900 font-medium text-primary-900 focus:ring-primary-900"
                  >
                  <label
                    for="v1"
                    class="ml-2 block cursor-pointer form-label sm:text-sm"
                  >{{
                    $t('v1_legacy') }}</label>
                </div>
                <div class="flex items-center">
                  <input
                    id="v2"
                    v-model="form.launch_version"
                    type="radio"
                    value="v2"
                    class="ml-1 size-4 border-primary-900 font-medium text-primary-900 focus:ring-primary-900"
                  >
                  <label
                    for="v2"
                    class="ml-2 block cursor-pointer form-label sm:text-sm"
                  >{{
                    $t('v2_beta') }}</label>
                </div>
              </div>
            </div>
            <div class="mt-6 flex items-center justify-between">
              <label class="mr-5 form-label">
                {{ $t('language') }}
              </label>
              <div class="flex justify-center pt-4 text-center">
                <div class="flex items-center">
                  <CommonListbox
                    v-model:value="selectedLanguage"
                    :options="FormattedLanguages"
                    :placeholder="$t('select')"
                    label-key="label"
                    value-key="value"
                    position="top"
                    button-class="w-60"
                    options-class="w-60"
                  />
                </div>
              </div>
            </div>
          </div>
          <TemplateFooter
            :disabled="disableSubmit"
            :title="$t('save')"
            @click-event="Save()"
          />
        </div>
      </div>
    </TemplateLayout>
  </div>
</template>

<script>
import CommonSwitch from '@/app/Common/CommonSwitch.vue';
import TemplateFooter from "@/app/Pages/Templates/Components/Footer.vue";
import { editTemplateFormData } from "@/helpers";
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from "axios";
import { mapState, mapStores } from 'pinia';
import TemplateLayout from './TemplateLayout.vue';


export default {
  name: "TemplateSetup",
  components: {
    TemplateFooter,
    CommonSwitch,
    TemplateLayout
  },
  props: {
    template: {
      required: true,
      type: Object
    },
    languages: {
      required: true,
      type: Object
    },
  },
  data() {
    return {
      setting: new Setting(),
      selectedLanguage: this.template.language,
      form: {
        id: this.template.id,
        name: this.template.name,
        slug: this.template.slug,
        launch_version: this.template.is_monetized ? 'v2' : this.template.launch_version,
        description: this.template.display_text,
        instant_template: this.template.instant_template ? true : false,
        is_monetized: this.template.is_monetized,
        language: this.template.language,
        post_creation_command: this.template.post_creation_command ? this.template.post_creation_command : '',
      },
      toggle: false,
      serverErrors: [],
      processing: false,
      templateData: this.template,
      max_instant_pool_count: this.template.max_instant_pool_count !== null ? this.template.max_instant_pool_count : '',
      max_char: 2500,
    };
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ["user", "teamAllow", "teamCan", "teamUsed", "featureAvailableFrom"]),
    disableSubmit() {
      if (!this.form.name || (Object.keys(this.serverErrors).length && this.serverErrors.name) || this.processing) {
        return true
      } else {
        return false
      }
    },
    FormattedLanguages() {
      return Object.keys(this.languages).map(key => ({
        text: this.languages[key].name,
        label: this.languages[key].name,
        value: key,
        image_url: this.languages[key].icon
      }));
    }
  },
  watch: {
    selectedLanguage(newValue) {
      this.form.language = newValue.value;
    }
  },
  methods: {
    validateName: function () {
      const self = this;
      self.processing = true
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(function () {
        self.serverErrors = [];
        const postData = {
          name: self.form.name,
          template_id: self.form.id
        }
        axios
          .post("/api/v2/templates/validate-name", postData)
          .then(() => {

          }).catch(function () {
            //self.serverErrors = error.response.data.errors;
          })
          .finally(() => {
            self.processing = false
          });
      }, 500);
    },
    showUpgradeWarning(feature) {
      const msg = {
        subHeading: '',
        planMessage: this.featureAvailableFrom[feature] ? this.$tChoice('this_feature_is_available_in_plan_and_above', this.featureAvailableFrom[feature]) : null,
        feature,
        triggerRef: `template_edit_setup_page_${feature}`
      }
      this.appStore.setUpgradeWarning(msg);
    },
    async Save() {
      const that = this;
      that.processing = true;
      that.serverErrors = [];
      const formData = editTemplateFormData(that.templateData);
      formData.set('name', that.form.name);
      formData.set('launch_version', that.form.launch_version);
      formData.set('instant_template', that.form.instant_template);
      formData.set('description', that.form.description ? that.form.description : '');
      formData.set('language', that.form.language ? that.form.language : 'en');
      formData.set('post_creation_command', that.form.post_creation_command);
      if (that.form.instant_template && that.user.can_set_template_pool_number) {
        formData.set('max_instant_pool_count', that.max_instant_pool_count);
      }
      await axios({
        method: "post",
        url: `/api/v2/templates/${that.templateData.id}?_method=PUT`,
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
      }).then(function (response) {
        that.templateData = response.data.data;
        const notification = {
          heading: that.$t('success'),
          subHeading: response.data.message,
          type: "success",
        };
        that.appStore.setNotification(notification);
      }).catch(function (error) {
        let errorMsg = null;
        if (error.response.status == 422) {
          that.serverErrors = error.response.data.errors;
        } else {
          errorMsg = error.response?.message
          const message = {
            heading: that.$t('error'),
            subHeading: errorMsg,
            type: "error",
          };
          this.appStore.setNotification(message);
        }
      }).finally(() => {
        that.processing = false
      });
    }
  },
};
</script>
<style scoped>
.fav-width {
  width: 900px;
}

@media screen and (max-width:991px) {
  .fav-width {
    width: 100%;
  }
}
</style>