<template>
  <Head>
    <title>Disconnected - {{ setting.brandShortName }}</title>
  </Head>
  <ConnectLayout
    :connect="$props.connect"
  >
    <div class="flex items-center justify-center card-main py-10">
      <div>
        <disconnect-icon
          :fill-color="setting.secondaryColor"
          class="mx-auto"
        />
        <p class="my-2 text-center text-sm font-medium text-grayCust-700">
          {{ $t('are_you_sure_you_want_to_disconnect_this_site') }}
        </p>
        <p class="my-2 text-center text-sm text-grayCust-500">
          {{ $t('this_will_automatically_reset_the_instawp_connect_plugin_from_the_site') }}
        </p>
        <p class="mt-2 text-center text-sm text-grayCust-500">
          {{ $t('this_will_not_delete_any_staging_sites_created_from_this_connected_site') }}
        </p>
        <div class="mt-6 flex justify-center">
          <CButton
            :btn-title="$t('disconnect')"
            btn-type="danger"
            @click="disconnect"
          />
        </div>
      </div>
    </div>
  </ConnectLayout>
  <!-- Original Dialog modal has been replaced with CommonModal -->

  <CommonModal
    v-model="showDeleteConfirm"
    size="lg"
    extra-footer-class="bg-grayCust-50"
    @close="showDeleteConfirm = false"
  >
    <template #content>
      <div class="flex items-center">
        <div class="mr-3 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
          <ExclamationIcon
            aria-hidden="true"
            class="h-6 w-6 text-red-600"
          />
        </div>
        <h3 class="text-lg font-medium leading-6 text-gray-600">
          {{ $t('sure_delete_connection') }}
        </h3>
      </div>
    </template>

    <template #footer>
      <div class="sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
        <CButton
          :btn-title="$t('cancel')"
          btn-type="gray-outline-btn"
          @click="showDeleteConfirm = false"
        />
        <CButton
          :btn-title="$t('disconnect')"
          btn-type="danger"
          @click="deleteConnection()"
        />
      </div>
    </template>
  </CommonModal>
</template>
<script>
import CommonModal from "@/app/Common/CommonModal.vue";
import ConnectLayout from "@/app/Pages/Connects/ConnectLayout.vue";
import DisconnectIcon from '@/app/Pages/Connects/Components/DisconnectIcon.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import { ExclamationIcon } from '@heroicons/vue/outline';
import axios from 'axios';
import { trans } from 'laravel-vue-i18n';
import { mapStores } from 'pinia';
import { route } from "ziggy-js";

export default {
  name: "ConnectDisconnect",
  components: {
    ConnectLayout,
    ExclamationIcon,
    DisconnectIcon,
    CommonModal
  },
  props: {
    connect: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeTabName: "DisconnectTab",
      showDeleteConfirm: false,
      setting: new Setting()
    };
  },
  computed: {
    ...mapStores(useAppStore)
  },
  methods: {
    disconnect() {
      this.showDeleteConfirm = true;
    },
    deleteConnection() {
      axios
        .post("/api/v1/disconnect-site/" + this.$props.connect.id)
        .then((response) => {
          if (response.data.status) {
            const message = {
              heading: trans('success'),
              subHeading: 'Site Disconnected',
              type: "success",
            };
            this.appStore.setNotification(message);
            this.$inertia.get(route('connects', { connect: this.$props.connect.id }))
          }

          if (!response.data.status) {
            const message = {
              heading: trans('failed'),
              subHeading: 'Something went wrong!',
              type: "error",
            };
            this.appStore.setNotification(message);
          }
        })
        .catch((error) => {
          const message = {
            heading: trans('failed'),
            subHeading: error.response || error.message,
            type: "error",
          };
          this.appStore.setNotification(message);
        })
        .finally(() => {
          this.btnLoading = false;
          this.showDeleteConfirm = false;
        });
    },
  }
};
</script>