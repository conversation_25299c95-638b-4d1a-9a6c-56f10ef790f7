<template>
  <div
    v-for="option in optionList"
    :key="option"
    class="space-y-3 p-4 relative card-main"
  >
    <div
      v-if="!option.isEnabled"
      class="absolute left-0 top-0 flex size-full items-center justify-center rounded-lg bg-gray-200 z-[2]"
    >
      <inertia-link
        :href="option.link"
      >
        <button
          type="button"
          class="inline-flex items-center justify-center gap-2 rounded-lg border focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 h-9 px-3.5 py-2 text-sm font-medium bg-secondary-800 transition-colors duration-200 ease-linear hover:bg-primary-310 focus:shadow-secondary focus:bg-primary-900 text-white border-transparent"
        >
          <component
            :is="option.iconName"
            v-if="option.iconName"
            class="size-6 text-white"
            aria-hidden="true"
            :height="20" 
            :width="20"
          /><span class="text-nowrap">{{ option.subTitle }}</span>
        </button>
      </inertia-link>
    </div>
    <div class="flex w-full flex-wrap items-end justify-between gap-2 relative">
      <div class="space-y-1">
        <div
          v-if="option.isLoading"
          class="h-8 w-16 bg-gray-300 animate-pulse rounded"
        />
        <h1
          v-else
          class="text-2xl font-semibold text-grayCust-980"
        >
          {{ option.title }}
        </h1>
        <p class="text-grayCust-910">
          {{ option.subTitle }}
        </p>
      </div>
      <div>
        <component
          :is="option.iconName"
          v-if="option.iconName"
          class="size-10 text-grayCust-460"
          aria-hidden="true"
          :height="40" 
          :width="40"
        />
      </div>
    </div>
    <div class="h-px w-full bg-grayCust-160" />
    <div
      v-if="option.isLoading"
      class="w-full space-y-1 animate-pulse"
    >
      <div class="h-8 bg-gray-200 rounded" />
      <div class="flex flex-wrap items-center justify-between gap-2">
        <div class="h-2 w-12 bg-gray-200 rounded" />
        <div class="h-2 w-12 bg-gray-200 rounded" />
      </div>
    </div>
    <div
      v-else
      class="w-full space-y-1"
    >
      <Vue3ChartJs
        :id="option.id"
        :type="graphData.type"
        :data="option.data"
        :options="graphData.options"
        style="max-height: 29px"
      />
      <div class="flex flex-wrap items-center justify-between gap-2">
        <span class="text-[10px] text-grayCust-910">{{ option.pastDays }}</span>
        <span class="text-[10px] text-grayCust-910">{{ option.currentDay }}</span>
      </div>
    </div>
  </div>
</template>

<script>

import RocketIcon from '@/app/Pages/Connects/Components/RocketIcon.vue';
import VulnerabilityIcon from '@/app/Pages/Connects/Components/VulnerabilityIcon.vue';
import { ArrowCircleUpIcon, ShieldCheckIcon } from '@heroicons/vue/solid';
import Vue3ChartJs from '@j-t-mcc/vue3-chartjs';
export default {
    name: 'GraphCard',
    components: {
        Vue3ChartJs,
        ArrowCircleUpIcon,
        ShieldCheckIcon,
        RocketIcon,
        VulnerabilityIcon,
    },
    
    props:{
        graphData:{
            type: [Array, Object],
            required: false,
            default: () => ({})
        },
        optionList:{
            type: Array,
            required: false,
            default: () => []
        }
    }
}
</script>