<template>
  <div>
    <Head>
      <title>{{ template.name }} - {{ setting.brandShortName }}</title>
    </Head>
    <TemplateLayout 
      :template="template" 
    > 
      <div class="card-main p-4 card-shadow overflow-y-auto overflow-x-hidden rounded-md bg-white">
        <div class="space-y-4">
          <div
            v-if="!teamCan?.advance_template_options"
            class="flex items-center  justify-end text-sm"
          >
            <div
              class="flex cursor-pointer text-left text-xs font-medium text-gray-900"
              @click="showUpgradeWarning('advance_template_options')"
            >
              <span>{{ $t('upgrade_unlock') }}</span>
              <solidLockClosedIcon class="ml-1 size-4 text-gray-700" />
            </div>
          </div>

          <div
            v-for="repository in repositories"
            :key="repository.id"
            class=""
          >
            <div class="relative gap-3 flex items-center border-b border-grayCust-900 pb-5">
              <div class="mr-3 mt-2 flex h-5 items-center">
                <input
                  :id="`ch_repo_${repository.id}`"
                  v-model="selectedUpdateRepositoryIds"
                  :aria-describedby="repository.repository_url"
                  name="selected_repo"
                  :value="repository.id"
                  type="checkbox"
                  class="text-insta-primary size-4 rounded-full focus:ring-secondary-800"
                >
              </div>
              <div class="min-w-0 flex-1 text-sm">
                <label
                  :for="`ch_repo_${repository.id}`"
                  class="font-medium text-gray-700"
                >{{
                  repository.repository_url }}</label>
                <p class="text-xs text-gray-500">
                  {{ $t('branch') }}: {{ repository.branch }}, {{
                    $t('destination') }}: {{ repository.destination_folder }}
                </p>
              </div>
              <div v-if="selectedUpdateRepositoryIds.indexOf(repository.id) != -1">
                <CommonButton
                  :btn-title="link_copied && selectedCopyRepoId == repository.id ? $t('copied') : $t('actions_file')"
                  :icon-name="link_copied && selectedCopyRepoId == repository.id ? 'OutlineCheckIcon' : 'OutlineClipboardCopyIcon'"
                  btn-type="secondary"
                  @click="copyYmlToClipboard(repository.id, selectedTemplateUpdateRepositorySlug); selectedCopyRepoId = repository.id"
                />
              </div>
            </div>
          </div>
          <textarea
            id="repo-yml"
            ref="repoYml"
            v-model="selectedYml"
            readonly
            class="absolute top-0 h-1 cursor-pointer p-0 opacity-0"
          />
          <div class="mt-6">
            <div class="flex rounded-md">
              <div class="relative flex grow items-stretch focus-within:z-10">
                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <!-- <webhook-gray-icon
                    class="text-grayCust-550"
                    aria-hidden="true"
                  /> -->
                  <img
                    :src="cdn('/images/webhook.svg')"
                    alt=""
                  >
                </div>
                <input
                  id="email"
                  ref="WebhookURL"
                  type="text"
                  name="email"
                  :value="webhookURL"
                  class="form-control w-full !pl-10 !rounded-r-none"
                  placeholder="https://noritake.us4.instawp.xyz/wp-admin"
                >
              </div>
              <CommonCopyButton
                :text="webhookURL"
                :lable-text="copyText"
                extra-class="border h-[34px] !rounded-l-none min-w-10 flex justify-center items-center"
                @text-copied="webhook_link_copied = true; setTimeout(() => { webhook_link_copied = false; copyText = $t('copy'); }, 2000)"
              />
              <CommonButton
                :btn-title="$t('deploy')"
                btn-type="gray-outline-btn"
                class="rounded-l-none"
                @click="deployGitWebhook()"
              />
            </div>
          </div>
        </div>
        <TemplateFooter
          :disabled="disableSubmit"
          extra-class="rounded-md py-2 px-4 bg-primary-900 text-white text-sm font-medium disabled:opacity-80 focus:ring-2 focus:ring-primary-900 focus:ring-offset-2"
          :title="$t('save')"
          @click-event="updateGitRepositories()"
        />
      </div>
    </TemplateLayout>
  </div>
</template>

<script>
import CommonButton from "@/app/Common/CommonButton.vue";
import CommonCopyButton from "@/app/Common/CommonCopyButton.vue";
import TemplateFooter from "@/app/Pages/Templates/Components/Footer.vue";
// import WebhookGrayIcon from '@/components/UpdatedDesignVersion/ImageComponents/WebhookGrayIcon.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import TemplateLayout from './TemplateLayout.vue';

export default {
   components: {
      // WebhookGrayIcon,
      TemplateFooter,
      TemplateLayout,
      CommonButton,
      CommonCopyButton
   },

   props: {
      template: {
         required: true,
         type: Object
      },
      repositories: {
         type: Array,
         required: true,
      }
    },
    emits: ['refreshTable'],
   data() {
      return {
         setting: new Setting(),
         activeTabName: "deployments",
         isImageShow: false,
         serverErrors: [],
         processing: false,
         form: [],
         isPublic: this.template.mark_as_public,
         updateRepositoryProcess: false,
         selectedUpdateRepositoryIds: [],
         selectedTemplateUpdateRepositorySlug: '',
         selectedCopyRepoId: '',
         selectedYml: '',
         webhook_link_copied: false,
         copyText: '',
         link_copied: false
      };
   },
   computed: {
      ...mapStores(useAppStore),
      ...mapState(useAppStore, ["user", "teamAllow", "teamCan", "teamUsed", "featureAvailableFrom"]),
      disableSubmit() {
         if (this.updateRepositoryProcess) {
            return true
         } else {
            return false
         }
      },
   },
   created() {
      
      if (this.template) {
         this.selectedUpdateRepositoryIds = this.template.gitrepositories_ids;
         this.selectedTemplateUpdateRepositorySlug = this.template.slug;
         this.webhookURL = this.template.webhook_url;
         this.copyText = trans('copy')
      }
   },
   methods: {
      showUpgradeWarning(feature) {
         const msg = {
            subHeading: trans('template_advance_config_not_allow'),
            planMessage: this.featureAvailableFrom[feature] ? trans('this_feature_is_available_in_plan_and_above', { planName: this.featureAvailableFrom[feature] }) : null,
            feature,
            triggerRef: `template_edit_deployments_page_${feature}`
         }
         this.appStore.setUpgradeWarning(msg);
      },
      changeStatus(status) {
         this.isPublic = status;
         this.$inertia.visit(this.$route("template.edit.setup", this.template));
      },
      updateGitRepositories() {
         const that = this;
         that.updateRepositoryProcess = true
         axios
            .post('/api/v1/template/update-git-repositories/' + that.template.id, { repository_ids: that.selectedUpdateRepositoryIds })
            .then(response => {
               const message = {
                  heading: that.$t('success'),
                  subHeading: response.data.message,
                  type: "success",
               };
               this.appStore.setNotification(message);
            })
            .then(() => {
                this.$emit("refreshTable");
            })
            .catch(() => {
            })
            .finally(() => {
               this.closeModal()
            });
      },
      copyYmlToClipboard(repo_id, template_slug) {
         const that = this;

         that.selectedYml = 'name: InstaWP WordPress Testing\n\non:\n  pull_request:\n    types: [opened]\n\njobs:\n  create-wp-for-testing:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: instawp/wordpress-testing-automation@main\n        with:\n          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}\n          INSTAWP_TOKEN: ${{secrets.INSTAWP_TOKEN}}\n          INSTAWP_TEMPLATE_SLUG: ' + template_slug + '\n          REPO_ID: ' + repo_id + '\n          INSTAWP_ACTION: create-site-template'
         this.copyYmlToClipboardTimeOut = setTimeout(function () {
            const ele = that.$refs.repoYml;

            if (ele) {
               ele.select();
               ele.setSelectionRange(0, 99999); /* For mobile devices */

               /* Copy the text inside the text field */
               document.execCommand("copy");
               that.link_copied = true;
               that.link_copy_timer = setTimeout(function () {
                  that.link_copied = false;
               }, 1000);
            }
         }, 200);

      },
      deployGitWebhook() {
         const that = this;
         that.processing = true
         axios
            .post("/git-deployments/templates/" + that.template.id, {})
            .then((response) => {
               if (response.status == 200) {

                  const message = {
                     heading: that.$t('success'),
                     subHeading: that.$t('git_deployment'),
                     type: "success",
                  };
                  that.appStore.setNotification(message);
               }
            })
            .catch((error) => {

               const message = {
                  heading: that.$t('error'),
                  subHeading: error.message,
                  type: "error",
               };
               that.appStore.setNotification(message);

            })
            .finally(() => {
               that.processing = false
            });
      },
      copyWebhookToClipboard(link) {
         const that = this;
         that.selectedWebhookURL = link;
         that.copyText = that.$t('copied');
         this.copyWebhookToClipboardTimeOut = setTimeout(function () {
            const ele = that.$refs.WebhookURL;

            if (ele) {
               ele.select();
               ele.setSelectionRange(0, 99999); /* For mobile devices */

               /* Copy the text inside the text field */
               document.execCommand("copy");
               that.webhook_link_copied = true;
               that.link_copy_timer = setTimeout(function () {
                  that.webhook_link_copied = false;
                  that.selectedWebhookURL = '';
                  that.copyText = that.$t('copy');
               }, 2000);
            }
         }, 100);
      },
   }
};
</script>

<style scoped>
.fav-width {
   width: 900px;
}

@media screen and (max-width:991px) {
   .fav-width {
      width: 100%;
   }

}
</style>