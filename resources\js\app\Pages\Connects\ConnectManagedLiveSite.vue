<template>
  <div>
    <transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="connectLiveSite"
        class="mb-8 inline-block min-w-full py-2 align-middle transition duration-300 ease-in-out md:px-6 lg:px-8"
      >
        <div class="relative overflow-hidden rounded-xl border border-primary-260 bg-primary-110 p-3 lg:px-6 lg:py-5">
          <div class="relative z-10 flex flex-wrap items-center justify-between gap-6 pb-4">
            <div>
              <h6
                class="text-base font-semibold text-black md:text-xl"
                style="line-height: normal;"
              >
                Staging Sites
              </h6>
              <p class="mt-1.5 max-w-[750px] text-xs text-black md:text-sm">
                From creating a test site to building sites for a client and migrating that to a hosted platform.
              </p>
            </div>
            <div class="ml-auto flex items-center gap-x-4">
              <!-- <button class="rounded-[10px] border border-grayCust-180 bg-white p-[11px] focus:ring-2 focus:ring-primary-900 focus:ring-offset-2">
                <img
                  :src="cdn('images/DashboardDesign/EmptyState/support-icon.svg')"
                  alt=""
                >
              </button> -->
              <IconButton
                btn-type="gray-outline-btn"
                size="btn-md"
                image-path="images/DashboardDesign/EmptyState/support-icon.svg"
              />
              <!-- <button class="flex items-center gap-1 rounded-lg border border-primary-900 bg-primary-900 px-3.5 py-2 text-base font-semibold text-white shadow-sm hover:bg-primary-700 focus:ring-2 focus:ring-primary-900 focus:ring-offset-2">
                <img
                  :src="cdn('images/DashboardDesign/EmptyState/plus-sm.svg')"
                  alt=""
                  srcset=""
                >
                Create Your First Staging Site
              </button> -->

              <CButton
                btn-title="Create Your First Staging Site"
                btn-type="secondary"
                size="btn-md"
                icon-name="OutlinePlusIcon"
              />
              <div
                class="w-6 h-6"
                @click="closeLiveSite"
              >
                <img
                  class="cursor-pointer w-6 h-6"
                  :src="cdn('images/chevron-down.svg')"
                  alt=""
                >
              </div>
            </div>
          </div>
          <div class="relative z-10 mt-6 flex flex-wrap items-center lg:flex-nowrap">
            <div class="w-full lg:w-auto">
              <div style="">
                <div
                  class="relative w-full overflow-hidden p-2 lg:w-[482px] lg:p-[18px]"
                  style="border-radius: 11.334px; border: 0.708px solid #E4E4E7;"
                >
                  <div class="connect-left-background absolute" />
                  <div class="connect-site-box absolute bottom-0 right-0" />
                  <!-- youtube video -->
                  <!-- <iframe class="lg:w-[440] w-full lg:h-[246] h-full relative" style="border-radius: 10px;" width="497" height="246" src="https://www.youtube.com/embed/JJgZ97AY2_8" title="Introducing InstaWP / 1-Click WordPress Sandbox Sites" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe> -->
                                    
                  <!-- <video class="lg:w-[440] w-full lg:h-[246] h-full relative" :controls="isControls" :autoplay="isControls" muted style="border-radius: 10px;">
                                        <source src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4" type="video/mp4">
                                    </video> -->
                  <div
                    class="relative rounded-lg p-3"
                    style="background-color: rgba(255, 255, 255, 0.50); border: 0.708px solid #fff;"
                  >
                    <img
                      :src="cdn('images/connect-manage-live-site.png')"
                      class="relative size-full lg:h-[269px] lg:w-[422px]"
                      style="border-radius: 10px;"
                      alt=""
                      srcset=""
                    >
                    <div
                      class="absolute z-50 cursor-pointer"
                      style="left: 50%; transform: translate(-40%, -50%); top: 50%;"
                      @click="VideoModal"
                    >
                      <img
                        :src="cdn('images/play-arrow-rounded.svg')"
                        alt=""
                        srcset=""
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 flex w-full flex-col gap-y-3 lg:ml-8 lg:mt-0 xl:gap-y-[22px]">
              <div
                v-for="item in stagingSiteList"
                :key="item"
                class="flex items-center gap-3"
              >
                <div>
                  <img
                    :src="cdn('images/DashboardDesign/EmptyState/check-icon.svg')"
                    class="h-5 min-w-[20px]"
                    alt=""
                    srcset=""
                  >
                </div>
                <p class="text-xs text-grayCust-620 md:text-sm lg:text-base xl:text-lg">
                  {{ item.title }}
                </p>
              </div>
              <!-- <div class="mt-6 gap-3 flex flex-wrap lg:flex-nowrap items-center">
                                <button
                                    class="w-full rounded-lg bg-secondary-800 py-2 text-sm font-medium text-white shadow-sm focus:ring-2 focus:ring-offset-2 focus:ring-secondary-800">Learn
                                    More</button>
                                <button
                                    class="w-full rounded-lg border border-grayCust-180 bg-white py-2 text-sm font-medium text-grayCust-1520 shadow-sm focus:ring-2 focus:ring-offset-2 focus:ring-secondary-800">View
                                    Documentation</button>
                            </div> -->
            </div>
          </div>
          <div class="z-9 absolute right-0 top-0 hidden lg:block">
            <img
              :src="cdn('images/DashboardDesign/EmptyState/right-side-bg.svg')"
              alt=""
            >
          </div>
        </div>
      </div>
    </transition>

    <!-- Using CommonModal instead of custom transition modal -->
    <CommonModal
      :model-value="isVideoModal"
      size="6xl"
      :allow-padding="false"
      @close="videoModalStop"
    >
      <template #content>
        <div class="relative">
          <div
            class="absolute -right-3.5 -top-3.5 z-50 flex size-10 cursor-pointer items-center justify-center rounded-full bg-white"
            @click="videoModalStop"
          >
            <img
              :src="cdn('images/Command/CloseGray.svg')"
              alt=""
              srcset=""
            >
          </div>
          <div class="overflow-hidden">
            <iframe
              class="relative size-full sm2:h-[250px] sm2:w-[450px] lg:h-[500px] lg:w-[800px]"
              style="border-radius: 10px;"
              width="497"
              height="246"
              :src="videoUrl"
              title="Introducing InstaWP / 1-Click WordPress Sandbox Sites"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowfullscreen
            />
          </div>
        </div>
      </template>
    </CommonModal>

    <!-- Old Custom Transition Modal - Commented Out -->
    <!--
    <transition leave-active-class="duration-200">
      <div
        v-show="isVideoModal"
        class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto px-4 py-6"
        scroll-region
      >
        <transition
          enter-active-class="ease-out duration-300"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="ease-in duration-200"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <div
            v-show="isVideoModal"
            class="fixed inset-0 transform transition-all"
            @click="videoModalStop"
          >
            <div class="absolute inset-0 bg-gray-500 opacity-75" />
          </div>
        </transition>

        <transition
          enter-active-class="ease-out duration-300"
          enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to-class="opacity-100 translate-y-0 sm:scale-100"
          leave-active-class="ease-in duration-200"
          leave-from-class="opacity-100 translate-y-0 sm:scale-100"
          leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div
            v-show="isVideoModal"
            class="rounded-lg bg-whiSte shadow-xl transform transition-all sm2:mx-auto"
          >
            <div
              class="absolute -right-3.5 -top-3.5 z-50 flex size-10 cursor-pointer items-center justify-center rounded-full bg-white"
              @click="videoModalStop"
            >
              <img
                :src="cdn('images/Command/CloseGray.svg')"
                alt=""
                srcset=""
              >
            </div>
            <div class="overflow-hidden">
              <iframe
                class="relative size-full sm2:h-[250px] sm2:w-[450px] lg:h-[500px] lg:w-[800px]"
                style="border-radius: 10px;"
                width="497"
                height="246"
                :src="videoUrl"
                title="Introducing InstaWP / 1-Click WordPress Sandbox Sites"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen
              />
            </div>
          </div>
        </transition>
      </div>
    </transition>
    -->
  </div>
</template>

<script>
import CommonModal from '@/Common/CommonModal.vue';
// import CButton from '@/components/New/Blocks/Buttons/CommonButton.vue';
// import IconButton from '@/components/New/Blocks/Buttons/IconButton.vue';

export default {
    name: 'ConnectManagedLiveSite',
    components: {
        // IconButton,
        // CButton,
        CommonModal
    },

    props: {
        connectLiveSite: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isVideoModal: false,
            videoUrl: 'https://www.youtube.com/embed/JJgZ97AY2_8?autoplay=0&rel=0',
            stagingSiteList:[
                {
                    id: 1,
                    title: 'Use it to build test websites.'
                },
                {
                    id: 2,
                    title: 'Build client sites and migrate them to a hosted site when you are done.'
                },
                {
                    id: 3,
                    title: 'Staging sites are created on InstaWP servers.'
                },
                {
                    id: 4,
                    title: 'They can be either temporary or permanent, based on your need.'
                },
                {
                    id: 5,
                    title: 'Free plan only allows for temporary sites.'
                },
                {
                    id: 6,
                    title: 'You can sync changes from staging to live using 2 way sync or full push.'
                },
            ]
        }
    },
    methods: {
        videoModalStop() {
            this.isVideoModal = false
            this.videoUrl = '';
        },
        VideoModal() {
            this.videoUrl = 'https://www.youtube.com/embed/JJgZ97AY2_8?autoplay=0&rel=0';
            this.isVideoModal = true
        },
        closeLiveSite() {
            this.$emit('closeLiveSite');
        },
    },
}
</script>

<style>
.connect-left-background {
    width: 123.252px;
    height: 123.252px;
    flex-shrink: 0;
    opacity: 0.9;
    background: var(--Brand-Tertiary, #F3E98D);
    filter: blur(76.14727020263672px);
}

.connect-site-box {
    width: 123.252px;
    height: 123.252px;
    flex-shrink: 0;
    opacity: 0.9;
    background: #B7ECDA;
    filter: blur(76.14727020263672px);
}

@media screen and (max-width: 991px) {
    .connect-left-background {
        width: 90px;
        height: 100px;
        flex-shrink: 0;
        opacity: 0.9;
        background: #11BF85;
        filter: blur(107.5px);
    }

    .connect-site-box {
        width: 150px;
        height: 90px;
        flex-shrink: 0;
        border: 1.5px solid var(--pending-03, #FEEFC6);
    }
}</style>