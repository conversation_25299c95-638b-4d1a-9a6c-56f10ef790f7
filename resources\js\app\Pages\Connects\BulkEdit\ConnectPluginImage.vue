<template>
  <img
    v-if="path"
    :src="path"
    class="w-6 h-6 rounded-full"
  >
</template>
<script>
export default {
    props: {
        slug: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            // path: 'https://ps.w.org/' + this.$props.slug + '/assets/icon-128x128.png',
            path: '',
            isSvgChecked: false
        }
    },
    mounted() {
        this.fetchIcon()
    },
    methods: {
        // onError() {
        //     if (this.isSvgChecked) {
        //         this.path = 'https://ps.w.org/' + this.$props.slug + '/assets/icon-128x128.jpg'
        //     } else {
        //         this.path = 'https://ps.w.org/' + this.$props.slug + '/icon.svg'
        //         this.isSvgChecked = true
        //     }
        // }
        fetchIcon() {
            fetch(`https://api.wordpress.org/plugins/info/1.1/?action=plugin_information&request[slug]=${this.$props.slug}&request[fields]=icons`)
            .then(response => response.json())
            .then(res => {
                if (!res) {
                    return this.path = '/images/wordpress-logo.svg'
                }
                if (!res.icons) {
                    return this.path = '/images/wordpress-logo.svg'
                }
                if (res.icons.default) {
                    this.path = res.icons['default']
                } else if(res.icons['1x']) {
                    this.path = res.icons['1x']
                } else if (res.icons['2x']) {
                    this.path = res.icons['2x']
                } else {
                    this.path = '/images/wordpress-logo.svg'
                }
            })
        }
    }

}
</script>