<template>
  <div class="bg-grayCust-50 px-6 py-5 flex items-center justify-between gap-4 border-t border-grayCust-180">
    <CButton
      :btn-title="$t('close')"
      btn-type="gray-outline-btn"
      @click="closeModal"
    />
    <CButton
      :icon-name="isAdvanced ? null : 'LockClosedIcon'"
      :btn-title="$t('save')"
      btn-type="secondary"
      @click="saveSettings"
    />
  </div>
</template>

<script>

export default {
  name: "UptimeSettingModelFooter",
  components: {
  },
  props: {
    isAdvanced: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeModal', 'saveSettings'],
  methods: {
    closeModal() {
      this.$emit('closeModal');
    },
    saveSettings() {
      this.$emit('saveSettings');
    }
  }
}
</script>

<style></style>