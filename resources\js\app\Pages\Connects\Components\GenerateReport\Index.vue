<template>
  <CommonModal
    :model-value="isReportModal"
    size="3xl"
    :allow-padding="false"
    @close="closeModal"
  >
    <template #content>
      <div class="p-6 space-y-6">
        <div>
          <h2 class="text-xl font-semibold text-grayCust-430 pb-5 border-b border-grayCust-180">
            {{ $t('generate_report') }}
          </h2>
        </div>
        <div
          v-if="connectedSitesList.length > 0"
          class="space-y-1.5"
        >
          <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('list_sites') }}</label>
          <multiselect-list-box
            :btn-placeholder="$t('select_sites')"
            :option-list="connectedSitesList"
            :selected-options="modelValue"
            @on-handle-change="onHandleChange"
          >
            <template #extra-content="{ item }">
              <span
                v-if="item.advanced && item.plan_features.client_reporting"
                class="inline-flex gap-1 h-6 items-center rounded px-2 py-1 text-xs font-medium bg-green-100 text-green-800"
              >
                {{ $t('advanced') }}
              </span>
              <span
                v-else-if="item.advanced"
                class="inline-flex gap-1 h-6 items-center rounded px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600"
              >
                {{ $t('advanced') }} ({{ $t('not_in_plan') }})
              </span>
              <span
                v-else
                class="inline-flex gap-1 h-6 items-center rounded px-2 py-1 text-xs font-medium bg-slate-100 text-slate-600"
              >
                {{ $t('basic') }}
              </span>
            </template>
          </multiselect-list-box>
        </div>
        <div class="space-y-1.5">
          <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('reporting_period') }}</label>
          <CommonListbox
            :options="reportingPeriodList"
            :value="selectedReportingPeriod"
            label-key="name"
            value-key="id"
            return-key="value"
            placeholder="Select reporting period"
            button-class="w-full"
            options-class="w-full"
            @select="onHandlePeriodChange"
          />
        </div>
        <div class="space-y-1.5">
          <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('inclusions') }}</label>
          <div class="flex items-center gap-4 flex-wrap">
            <div
              v-for="inclusion in inclusionList"
              :key="inclusion.id"
            >
              <CheckboxButton
                :id="inclusion.id"
                v-model:is-checked="inclusion.isChecked"
                class="w-fit"
                :title="inclusion.title"
              />
            </div>
          </div>
        </div>
        <div class="space-y-1.5 p-4 rounded-lg bg-grayCust-380 border border-grayCust-160">
          <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('send_copy_of_the_report_to') + ':' }}</label>
          <CommonMultiTagInput
            v-model="customerEmails"
            :placeholder="$t('add_comma_separated_email_addresses')"
          />
        </div>
      </div>
      <GenerateReportFooter
        :is-generate-report-processing="isGenerateReportProcessing"
        @close-modal="closeModal"
        @generate-report="generateReport"
      />
    </template>
  </CommonModal>
</template>

<script>
import CheckboxButton from '@/app/Common/CheckboxButton.vue';
import CommonListbox from "@/app/Common/CommonListbox.vue";
import CommonModal from "@/app/Common/CommonModal.vue";
import CommonMultiTagInput from "@/app/Common/CommonMultiTagInput.vue";
import GenerateReportFooter from '@/app/Pages/Connects/Components/GenerateReport/Footer.vue';
import MultiselectListBox from '@/components/New/Blocks/Dropdowns/ListBox/MultiselectListBox.vue';
import { wTrans } from "laravel-vue-i18n";

export default {
  name: "ReportModal",
  components: {
    CommonModal,
    CommonListbox,
    CommonMultiTagInput,
    GenerateReportFooter,
    CheckboxButton,
    MultiselectListBox,
  },
  props: {
    isReportModal: {
      type: Boolean,
      required: true,
    },
    connectedSitesList: {
      type: Array,
      required: false,
      default: () => [],
    },
    modelValue: {
      type: Array,
      required: false,
      default: () => [],
    },
    isGenerateReportProcessing: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['closeModal', 'generate-report', 'update:modelValue'],
  data() {
    return {
      selectedTab: 1,
      selectedReportingPeriod: {},
      sendToCustomer: false,
      customerEmails: [],
      reportingPeriodList: [
        {
          id: 1,
          value: 'this_week',
          name: wTrans('this_week'),
        },
        {
          id: 2,
          value: 'last_week',
          name: wTrans('last_week'),
        },
        {
          id: 3,
          value: 'this_month',
          name: wTrans('this_month'),
        },
        {
          id: 4,
          value: 'last_month',
          name: wTrans('last_month'),
        },
        {
          id: 5,
          value: 'this_year',
          name: wTrans('this_year'),
        },
        {
          id: 6,
          value: 'last_year',
          name: wTrans('last_year'),
        }
      ],
      inclusionList: [
        {
          id: 'summary',
          title: wTrans('summary'),
          isChecked: true,
        },
        {
          id: 'performance',
          title: wTrans('performance'),
          isChecked: true,
        },
        {
          id: 'uptime_history',
          title: wTrans('uptime_history'),
          isChecked: true,
        },
        {
          id: 'security_scan',
          title: wTrans('security_scan'),
          isChecked: true,
        },
        {
          id: 'activity_logs',
          title: wTrans('activity_logs'),
          isChecked: true,
        },
      ],
    };
  },
  created() {
    this.selectedReportingPeriod = this.reportingPeriodList[0];
  },
  methods: {
    closeModal() {
      this.$emit('closeModal');
    },
    onHandlePeriodChange(optionValue) {
      this.selectedReportingPeriod = optionValue;
    },
    onHandleChange(selectedSites) {
      this.$emit('update:modelValue', selectedSites);
    },
    generateReport() {
      this.$emit('generate-report', {
        inclusions: this.inclusionList.filter(inclusion => inclusion.isChecked).map(inclusion => inclusion.id),
        customerEmails: this.customerEmails,
        reportingPeriod: this.selectedReportingPeriod.value,
      })
    },
  },
}
</script>
<style>
@media (max-height: 600px) {
  .report-model {
    align-items: start !important;
  }
}
</style>
