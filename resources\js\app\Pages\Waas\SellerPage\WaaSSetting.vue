<template>
  <div>
    <div class="p-4 pt-0 md:p-8">
      <div class="card-content card-content--mod rounded-xl border p-4 md:p-11">
        <div class="text-center">
          <div class="mb-2 text-2xl font-semibold text-grayCust-1600 sm2:mb-6">
            {{ template?.name }}
          </div>
          <div class="text-sm text-grayCust-550">
            {{ template?.display_text }}
          </div>
        </div>

        <div
          v-if="getLaunchSiteStatus && getLaunchSiteStatus == 'waiting'"
          class="my-6 flex w-full flex-wrap items-center rounded-lg border border-grayCust-900 bg-grayCust-270 p-2 sm2:p-4 md:flex-nowrap"
        >
          <div
            class="mb-6 flex w-full items-center rounded-lg bg-white p-6"
            :class="getLaunchSiteDetail?.site_creating_mode == 'waas' ? '' : 'site-create'"
          >
            <img
              style="width:54px ; height: 54px"
              :src="cdn('images/site_creating.gif')"
            >
            <div>
              <div class="flex items-center justify-between">
                <p class="text-lg font-medium text-grayCust-1550">
                  {{ trans('starting_installation') }}
                </p>
              </div>
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-grayCust-1550">
                  {{
                    trans('please_wait_for_start_installation') }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="getLaunchSiteStatus && getLaunchSiteStatus == 'failed'"
          class="my-6 flex w-full flex-wrap items-center rounded-lg border border-grayCust-900 bg-grayCust-270 p-2 sm2:p-4 md:flex-nowrap"
        >
          <div
            class="mb-6 flex w-full items-center rounded-lg bg-white p-6"
            :class="getLaunchSiteDetail?.site_creating_mode == 'waas' ? '' : 'site-create'"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="mr-3 size-7 text-red-700"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>

            <div>
              <div class="flex items-center justify-between">
                <p
                  v-if="!getLaunchSiteMessage"
                  class="text-lg font-medium text-grayCust-1550"
                >
                  {{
                    trans('installation_failed') }}
                </p>
                <p
                  v-else
                  class="text-lg font-medium text-grayCust-1550"
                >
                  {{ getLaunchSiteMessage.heading }}
                </p>
              </div>
              <div class="flex items-center justify-between">
                <p
                  v-if="getLaunchSiteMessage"
                  class="text-sm font-medium text-grayCust-1550"
                >
                  {{
                    getLaunchSiteMessage.subHeading }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="getLaunchSiteStatus && getLaunchSiteStatus == 'processing'"
          class="my-6 flex w-full flex-wrap items-center rounded-lg border border-grayCust-900 bg-grayCust-270 p-2 sm2:p-4 md:flex-nowrap"
        >
          <img
            style="width:45px ; height: 45px"
            class="mb-2 rounded-lg border border-grayCust-900 sm2:mb-0"
            :src="cdn('images/site_creating.gif')"
          >
          <div class="w-full sm2:ml-4">
            <div class="mb-2 flex items-center justify-between sm2:mb-0 ">
              <p class="text-lg font-medium text-grayCust-1550">
                {{
                  trans('installation_in_progress') }}
              </p>
              <span class="si-progress ml-2 text-sm font-semibold">{{ getLaunchSiteProgress }}%</span>
            </div>
            <div class="mt-0.5 h-1.5 rounded-xl bg-grayCust-250">
              <div
                class="si-progress-bar h-1.5 w-4 rounded-xl"
                :style="{ width: getLaunchSiteProgress + '%' }"
              />
            </div>
          </div>
        </div>

        <div
          v-if="getLaunchSiteStatus && getLaunchSiteStatus == 'completed'"
          class="mt-6 w-full rounded-xl border border-grayCust-900 bg-grayCust-270 text-black"
        >
          <div class="flex items-start justify-center border-b p-6">
            <div class="flex flex-wrap  items-center md:flex-nowrap">
              <div
                class="flex items-center justify-center"
                style="width: 34px; height: 34px;"
              >
                <img
                  src="/images/site_created_img.png"
                  class="h-8 w-10"
                >
              </div>
              <div class="ml-1.5">
                <h4 class="text-sm font-medium text-grayCust-1550 md:text-xl">
                  {{ trans('your_site_is_ready') }}
                </h4>
              </div>
            </div>
          </div>
          <div class="p-6">
            <div class="flex flex-wrap items-center md:flex-nowrap">
              <h4 class="w-20 text-base font-semibold text-grayCust-800 md:text-sm">
                {{ trans('url') }}
              </h4>
              <p
                class="border-b border-dashed border-primary-900 text-base text-primary-900 sm2:ml-2 md:text-sm"
              >
                {{ getLaunchSiteDetail.wp_url }}
              </p>
              <div class="ml-1.5 cursor-pointer">
                <a
                  :href="getLaunchSiteDetail.wp_url"
                  target="_blank"
                >
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_4974_20341)">
                      <path
                        d="M11.4544 0H7.09078C6.78953 0 6.54531 0.244219 6.54531 0.545461C6.54531 0.846703 6.78953 1.09092 7.09078 1.09092H10.1376L4.52329 6.70523C4.31027 6.91826 4.31027 7.26359 4.52329 7.47661C4.62977 7.58311 4.76936 7.63636 4.90896 7.63636C5.04855 7.63636 5.18817 7.58313 5.29467 7.47659L10.909 1.8623V4.9091C10.909 5.21034 11.1532 5.45456 11.4544 5.45456C11.7557 5.45456 11.9999 5.21034 11.9999 4.9091V0.545461C11.9999 0.244219 11.7557 0 11.4544 0Z"
                        fill="#2e6ce6"
                      />
                      <path
                        d="M9.27272 5.45448C8.97148 5.45448 8.72726 5.6987 8.72726 5.99994V10.909H1.0909V3.27266H6C6.30124 3.27266 6.54546 3.02844 6.54546 2.7272C6.54546 2.42596 6.30124 2.18176 6 2.18176H0.545461C0.244219 2.18176 0 2.42598 0 2.72722V11.4545C0 11.7557 0.244219 11.9999 0.545461 11.9999H9.27274C9.57398 11.9999 9.8182 11.7557 9.8182 11.4545V5.99994C9.81818 5.6987 9.57396 5.45448 9.27272 5.45448Z"
                        fill="#2e6ce6"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_4974_20341">
                        <rect
                          width="12"
                          height="12"
                          fill="white"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </a>
              </div>
              <div class="ml-1">
                ({{ trans('expires_in') }} {{ totalDays }})
              </div>
            </div>
            <div class="mt-4 flex flex-wrap items-start md:flex-nowrap">
              <h4 class="w-20 text-base font-semibold text-grayCust-800 md:text-sm">
                {{ trans('username') }}
              </h4>
              <input
                ref="username"
                type="text"
                class="absolute p-0 opacity-0"
                :value="getLaunchSiteDetail.wp_username"
              >
              <p
                class="username-width ml-2 flex items-center break-all text-base font-semibold text-grayCust-800 md:text-sm"
              >
                {{ getLaunchSiteDetail.wp_username }}

                <button
                  type="button"
                  class="ml-2 cursor-pointer"
                  @click="copyToClipboard('username')"
                >
                  <Tooltip :tooltip-text="waassiteCopy.username ? trans('copied') : trans('copy')">
                    <CheckIcon
                      v-if="waassiteCopy.username"
                      class="size-4 text-grayCust-800"
                    />
                    <CopyGrayIcon
                      v-else
                      class="size-4 text-primary-900"
                    />
                  </Tooltip>
                </button>
              </p>
            </div>
            <div class="mt-4 flex flex-wrap items-start md:flex-nowrap">
              <h4 class="ext-base w-20 font-semibold text-grayCust-800 md:text-sm">
                {{ trans('password') }}
              </h4>
              <input
                ref="password"
                type="text"
                class="absolute p-0 opacity-0"
                :value="getLaunchSiteDetail.wp_password"
              >
              <p
                class="username-width ml-2 flex items-center break-all text-base font-semibold text-grayCust-800 md:text-sm"
              >
                {{ getLaunchSiteDetail.wp_password }}

                <button
                  type="button"
                  class="ml-2 cursor-pointer"
                  @click="copyToClipboard('password')"
                >
                  <Tooltip :tooltip-text="waassiteCopy.password ? trans('copied') : trans('copy')">
                    <CheckIcon
                      v-if="waassiteCopy.password"
                      class="size-4 text-grayCust-800"
                    />
                    <CopyGrayIcon
                      v-else
                      class="size-4 text-primary-900"
                    />
                  </Tooltip>
                </button>
              </p>
            </div>
          </div>
        </div>
        <div
          v-if="getLaunchSiteStatus && getLaunchSiteStatus == 'completed'"
          class="mt-6"
        >
          <p
            class="flex items-center rounded-xl border border-purpleCust-370 bg-blueCust-300 p-3 text-sm font-medium text-blue-800"
          >
            <span class="mr-2"><svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M18 10.001C18 14.4193 14.4183 18.001 10 18.001C5.58172 18.001 2 14.4193 2 10.001C2 5.5827 5.58172 2.00098 10 2.00098C14.4183 2.00098 18 5.5827 18 10.001ZM11 6.00098C11 6.55326 10.5523 7.00098 10 7.00098C9.44772 7.00098 9 6.55326 9 6.00098C9 5.44869 9.44772 5.00098 10 5.00098C10.5523 5.00098 11 5.44869 11 6.00098ZM9 9.00098C8.44772 9.00098 8 9.44869 8 10.001C8 10.5533 8.44772 11.001 9 11.001V14.001C9 14.5533 9.44772 15.001 10 15.001H11C11.5523 15.001 12 14.5533 12 14.001C12 13.4487 11.5523 13.001 11 13.001V10.001C11 9.44869 10.5523 9.00098 10 9.00098H9Z"
                fill="#60A5FA"
              />
            </svg></span> {{ trans('waas_click_go_live_inside_wordpress') }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="flex flex-wrap items-center justify-end rounded-xl rounded-t-none bg-grayCust-50 p-4 sm2:flex-nowrap"
    >
      <div>
        <div class="flex items-center justify-center">
          <!-- <div v-if="site_installing_status == 'completed' || getLaunchSiteStatus == 'completed' && waas.is_pre_payment == 1 && is_progress_page == 0">
                        <a v-if="wizardPurchaseDetailsRoute != '' && wizardPurchaseDetailsRoute != undefined"
                            target="_blank" type="button" :href="wizardPurchaseDetailsRoute"
                            class="md:ml-4  disabled:opacity-50 md:w-auto inline-flex justify-center items-center rounded-md border shadow-sm px-4 py-2 text-base font-medium ring-primary-900 sm:col-start-2 sm:text-base w-full text-grayCust-700 border-grayCust-350">
                            Manage Site
                            <span class="ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                    fill="none">
                                    <path
                                        d="M8.33325 5.00065H4.99992C4.07944 5.00065 3.33325 5.74684 3.33325 6.66732V15.0007C3.33325 15.9211 4.07944 16.6673 4.99992 16.6673H13.3333C14.2537 16.6673 14.9999 15.9211 14.9999 15.0007V11.6673M11.6666 3.33398H16.6666M16.6666 3.33398V8.33398M16.6666 3.33398L8.33325 11.6673"
                                        stroke="#2e6ce6" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </span>
                        </a>
                    </div> -->
          <div
            v-if="getLaunchSiteStatus == 'completed'"
            class="hidden items-center md:flex"
          >
            <a
              ref="siteHashLinkRef"
              target="_blank"
              :href="`${route('autologin')}?site=${getLaunchSiteDetail.s_hash}${(getLaunchSiteCustomRedirect) ? '&redir=' + getLaunchSiteCustomRedirect : ''}`"
              class="magic-login site-created-shadow card-success-button card-success-button--mod ml-4 flex cursor-pointer items-center rounded-md bg-primary-900 px-4 py-2.5 hover:bg-primary-900"
              style="; color: rgb(255, 255, 255);"
            >
              <loading
                v-if="magic_loading"
                :width="16"
                :height="16"
                :display-in-line="true"
              />
                            &nbsp;
              <img
                src="/images/SiteEdit/SiteEditHeader/site-magic-login.svg"
                alt=""
                class="!h-4 !w-4"
              >
                            &nbsp;
              <p
                class="ml-2.5 text-sm font-medium"
                style="color: rgb(255, 255, 255);"
              > {{
                trans('magic_login') }}</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import WizardSiteCreated from "@/app/Pages/Waas/SellerPage/WizardSiteCreated.vue";
import CreateSite from "@/components/CreateSite.vue";
import Tooltip from '@/components/New/Blocks/Tooltip/Tooltip.vue';
import SiteInstallationStatus from "@/components/SiteCreate/SiteInstallationStatus.vue";
import HostingNewSite from "@/components/UpdatedDesignVersion/ConnectSite/HostingNewSite.vue";
import Loading from "@/components/UpdatedDesignVersion/ImageComponents/ConnectSite/Loading.vue";
import CopyGrayIcon from '@/components/UpdatedDesignVersion/ImageComponents/CopyGrayIcon.vue';
import { showRemainingDays } from "@/helpers.js";
import { useAppStore } from '@/store/index';
import { CheckIcon } from "@heroicons/vue/outline";
import { trans } from "laravel-vue-i18n";
import { mapState, mapStores } from 'pinia';

export default {
    name: 'WaasSetting',

    components: {
        CreateSite,
        SiteInstallationStatus,
        HostingNewSite,
        WizardSiteCreated,
        Loading,
        Tooltip,
        CopyGrayIcon,
        CheckIcon
    },
    props: [
        "template",
        "serverErrors",
        "siteId",
        "waas",
        "wizardPurchaseDetailsRoute",
        "selectedHostingPackage",
        "site_installing_status",
        "site_progress_message",
        "site_progress",
        "createdsitedetails",
        "waasPrimaryColor",
        "waasSecondaryColor",
        "is_progress_page"
    ],
    computed: {
        ...mapStores(useAppStore),
        ...mapState(useAppStore, [
            "getLaunchSiteStatus",
            "getLaunchSiteCustomRedirect",
            "getLaunchSiteDetail",
            "updateLaunchSiteStatus",
            "getLaunchSiteProgress",
            "getLaunchSiteMessage"
        ]),
        totalDays() {
            return this.getLaunchSiteDetail ? showRemainingDays(this.getLaunchSiteDetail.remaining_site_minutes) : "0 days";
        }
    },
    watch: {
        getLaunchSiteStatus(value) {
            const that = this;
            //
            if (value == "completed") {
                //
                //
                let hosting_connection_id = '';
                if (that.waas.hosting_type == 'my_hosting') {
                    if (that.selectedHostingPackage.wizard_server?.length > 0 && that.selectedHostingPackage.wizard_server[0]?.hosting_connection_id) {
                        hosting_connection_id = that.selectedHostingPackage?.wizard_server[0]?.hosting_connection_id;
                    }
                    // if (this.createdsitedetails?.plugin_install != false) {
                        /*that.wizardSiteInstallPluginAndConfig({
                            site_id: that.siteId,
                            hosting_type: that.waas.hosting_type,
                            hosting_connection_id: hosting_connection_id
                        }, that.waas.slug);*/
                    // }

                }
            }

            if (value == "failed") {

                this.$emit('catchlunchSite', that.waas.id);
            }
        }
    },
    unmounted() {
        clearTimeout(this.copyToClipboardTimeOut)
    },
    methods: {
      trans: trans,
        wizardSiteInstallPluginAndConfig(data, slug) {
            const that = this;
            //
            //
            axios
                .post("/api/v2/wizard/" + slug + "/site/installplugin-and-config", data)
                .then((response) => {
                }).catch((error) => {


                }).finally(() => {

                });
        },
        closeHostingNewSite() {
            this.site_installing_status = 'pending';
        },

        copyToClipboard(type) {
            const that = this;
            that.waassiteCopy[type] = true;
            const ele = this.$refs[type];
            if (ele) {
                ele.select();
                ele.setSelectionRange(0, 99999);
                document.execCommand('copy');

                that.waassiteCopyTimeout = setTimeout(() => {
                that.waassiteCopy[type] = false;
                }, 2000);
            }
        },
    },
    data() {
        return {
            backgroundColor: this.waas.primary_color,
            btnLoading: false,
            magic_loading: false,
            copyText: trans('copy'),
            copyToClipboardTimeOut: null,
            waassiteCopy: {
                username: null,
                password: null
            },
            waassiteCopyTimeout:null
        };
    },
}
</script>

<style scoped>
.bg-primary-900 {
    background-color: v-bind(waasPrimaryColor);
}

.ring-primary-900:focus {
    border-color: v-bind(waasPrimaryColor);
}
</style>
