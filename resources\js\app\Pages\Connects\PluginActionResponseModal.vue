<template>
  <TransitionRoot
    as="template"
    :show="open"
  >
    <Dialog
      as="div"
      class="relative z-10"
    >
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transform transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div>
                <div
                  class="mx-auto flex size-12 items-center justify-center rounded-full"
                  :class="[isLoading || isSuccess ? 'bg-green-100' : 'bg-red-100']"
                >
                  <div v-if="isLoading">
                    <img
                      class="refresh-animation"
                      :src="cdn(`images/HostingManager/refresh.svg`)"
                      alt="loading"
                    >
                  </div>
                  <div v-else-if="!isLoading && isSuccess">
                    <CheckIcon
                      class="size-6 text-green-600"
                      aria-hidden="true"
                    />
                  </div>
                  <div v-else>
                    <XCircleIcon
                      class="size-6 text-red-600"
                      aria-hidden="true"
                    />
                  </div>
                </div>
                <div class="mt-3 text-center sm:mt-5">
                  <DialogTitle
                    as="h1"
                    class="text-2xl font-semibold leading-6 text-gray-900"
                  >
                    {{ actionStatus }}
                  </DialogTitle>
                  <div
                    v-if="plugins.length && !isLoading"
                    class="mt-4 max-h-56 overflow-auto rounded-lg border"
                  >
                    <div
                      v-for="(plugin, index) in plugins"
                      :key="plugin.slug"
                      :class="[index !== Object.keys(plugins).length - 1 ? 'border-b' : '', 'items-center, flex justify-between p-3']"
                    >
                      <div class="inline-flex shrink-0 items-center">
                        <span
                          v-if="plugin.icon"
                          class="rounded-full bg-gray-400 p-1"
                        >
                          <img
                            class="size-10 rounded-full"
                            :src="plugin.icon"
                            alt=""
                          >
                        </span>
                        <span
                          v-else
                          class="flex size-10 items-center justify-center rounded-full bg-gray-400"
                        >
                          <CogIcon class="size-6 text-white" />
                        </span>
                        <div>
                          <p class="ml-2 text-sm font-medium text-gray-900">
                            {{ plugin.name ?? plugin.slug }}
                          </p>
                        </div>
                      </div>
                      <div class="flex min-w-0 items-center">
                        <span
                          class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset"
                          :class="[plugin.success ? 'bg-green-50  text-green-700 ring-green-600/20' : 'bg-red-50  text-red-700 ring-red-600/20']"
                        >{{ plugin.success ? 'Success' : 'Failed' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mt-5 flex justify-around sm:mt-6">
                <button
                  type="button"
                  class="mt-3 inline-flex w-56 justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                  @click="close"
                >
                  Close
                </button>
                <button
                  v-if="!isLoading"
                  type="button"
                  class="inline-flex w-56 justify-center rounded-md bg-primary-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-900 sm:col-start-2"
                  @click="purgeCache"
                >
                  Purge Again
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script>
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { CheckIcon, CogIcon, XCircleIcon } from '@heroicons/vue/outline'

export default {

  components: {
    CheckIcon,
    CogIcon,
    XCircleIcon,
    Dialog,
    DialogPanel,
    DialogTitle,
    TransitionRoot,
    TransitionChild,
  },
  props: {
    plugins: {
      type: Array,
      required: true
    },
    actionStatus: {
      type: String,
      default: 'Loading'
    },
    open: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: true
    },
    connectId: {
      type: Number
    },
    isSuccess: {
      type: Boolean
    }
  },

  methods: {
    close() {
      this.$emit('closeModal')
    },

    purgeCache() {
      this.$emit('purgeCache', this.connectId)
    }
  },
  
}

</script>
<style scoped>
.refresh-animation{
  animation: refresh-animation 1s linear infinite;
}

@keyframes refresh-animation {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style>