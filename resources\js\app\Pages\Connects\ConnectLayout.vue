<template>
  <PageHeader
    :title="$t('manage')"
    :route-name="route('connects')"
    :is-inner-page="true"
  >
    <template #left>
      <div class="flex items-center gap-3">
        <!-- <span class="cursor-pointer break-all text-lg font-medium text-warning-1300">
          {{ $props.connect?.url?.replace(/^(https?:\/\/)/, '') }}
        </span> -->
        <a
          :href="$props.connect?.url"
          target="_blank"
          class="self-center text-lg font-semibold text-warning-1300 md:max-w-[350px] sm2:max-w-[300px] sm:max-w-[250px] truncate"
          @click="addSiteOpenEvent($props.connect.id)"
        >
          {{ $props.connect?.url?.replace(/^(https?:\/\/)/, '') }}
          <!-- <OutlineExternalLinkIcon class="size-4 text-blueCust-500" /> -->
        </a>
        <div
          v-if="!isEditingSiteLabel"
          v-tooltip="$t('edit_site_label')"
          class="md:flex hidden items-center cursor-pointer gap-1.5"
          @click="isEditingSiteLabel=true"
        >
          <CommonIcon
            class="flex items-center cursor-pointer border-l pl-3 border-dashed border-warning-1020"
            :image-path="'images/StagingDashboard/pencil-alt-icon.svg'"
          />
          <div
            v-show="!isEditingSiteLabel"
            class="text-sm text-grayCust-600"
          >
            {{ siteLabel ? siteLabel : $t('edit_site_label') }}
          </div>
        </div>
        <div
          v-show="isEditingSiteLabel"
          class="text-primary flex items-center text-sm focus-visible:outline-none"
        >
          <input
            ref="inputSiteLabel"
            v-model="siteLabel"
            :maxlength="30"
            autocomplete="off"
            class="form-control"
            @keyup.esc="isEditingSiteLabel = false; siteLabel = $props.connect.label"
            @keypress="validateName($event)"
          >
          <button
            class="disabled:opacity-60"
            type="button"
            :disabled="processing"
            @click="saveLabel"
          >
            <img
              v-lazy="cdn('images/save-icon.svg')"
              class="ml-2 cursor-pointer"
              alt=""
              srcset=""
            >
          </button>
        </div>
      </div>
    </template>
    <template #right>
      <div class="flex items-center gap-3 justify-between">
        <CButton
          icon-name="OutlineCubeIcon"
          btn-title="Create Staging"
          btn-type="gray-outline-btn"
          @click="handleButtonClick"
        />
        <CButton
          icon-name="OutlineDownloadIcon"
          :btn-title="$t('magic_login')"
          btn-type="secondary"
          icon-size="rotate-[270deg]"
          @click="autoLoginConnect"
        />
        <!-- <IconButton
            icon-name="OutlineDotsHorizontalIcon"
            btn-type="gray-outline-btn"
          /> -->
        <CommonPopover>
          <template #popoverButton>
            <IconButton
              icon-name="OutlineDotsHorizontalIcon"
              btn-type="gray-outline-btn"
            />
          </template>
          <template #popoverOptions>
            <CommonMenuItem
              :label="$t('db_editor')"
              icon="OutlineDatabaseIcon"
              @click="databaseManager"
            />
            <CommonMenuItem
              :label="$t('view_log')"
              image="/images/view-logs.svg"
              @click="debugLog"
            />
            <CommonMenuItem
              :label="$t('purge_cache')"
              image="/images/brush.svg"
              @click="cacheClear"
            />
            <CommonMenuItem
              :label="$t('generate_report')"
              icon="OutlineDocumentReportIcon"
              @click="goToReports"
            /> 
            <CommonMenuItem
              :label="$t('scan_for_updates')"
              icon="OutlineRefreshIcon"
              @click="fetchHeartBeat"
            />
          </template>
        </CommonPopover>
      </div>
    </template>
    <template #bottom>
      <div class="flex items-center space-x-4 py-2 px-4 border-b border-grayCust-160  bg-white">
        <span class="text-gray-800 flex text-xs font-medium items-center">
          <CommonIcon
            :image-path="'/images/clock-icon.svg'"
          />
          <span class="ml-1 text-sm font-medium text-grayCust-500 truncate">Created {{
            dateTimeHumanize($props.connect.created_at) }} <span v-if="site?.user?.name">, by {{ site.user.name
          }}</span>
          </span>
        </span>
        <div class="h-5 w-[1px] bg-grayCust-160" />
        <span class="flex items-center text-gray-600 gap-1.5">
          <CommonIcon
            :image-path="'/images/ConnectSiteUpdate/wordpress.svg'"
          />
          <span class="text-sm"> {{ $props.connect.wp_version }}</span>
        </span>
        <div class="h-5 w-[1px] bg-grayCust-160" />

        <span class="flex items-center gap-1.5 text-gray-600">
          <CommonIcon
            :image-path="'/images/php-icon.svg'"
          />
          <span class="text-sm">  {{ $props.connect.php_version }}</span>
        </span>
        <div class="h-5 w-[1px] bg-grayCust-160" />
        <SolidFireIcon
          v-tooltip="$props.connect.advance_connect_plan_id ? $t('advanced_plan') : $t('basic_plan')"
          class="w-4 h-4 text-grayCust-630 "
          :class="$props.connect.advance_connect_plan_id ? 'text-warning-1110' : 'text-grayCust-630'"
        />
      </div>
      <div class="lg:hidden w-full px-4 py-4 border-b border-grayCust-160">
        <CommonListbox
          v-model:value="selectedMenuItem"
          :options-extra-class="option => option.isChild ? 'pl-6' : ''"
          :options="flattenedMenuItems"
          :check-icon="true"
          :label-key="'title'"
          :value-key="'path'"
          :button-class="'w-full'"
          :options-class="'w-full'"
          @update:value="handleMenuItemSelect"
        />
      </div>
    </template>
  </PageHeader>
  
  <SiteLogs
    :debug-logs="debugLogs"
    :is-debug-log-open="isDebugLogOpen"
  />
  <PluginActionResponseModal
    :action-status="purgeCacheStatus"
    :connect-id="$props.connect?.id"
    :is-loading="btnLoading"
    :is-success="isPurgeCacheSuccess"
    :open="isPurgeCacheOpen"
    :plugins="purgePlugins"
    @close-modal="closePurgeCacheModal"
    @purge-cache="cacheClear"
  />
  

  <div class="flex-1 flex overflow-hidden bg-grayCust-50">
    <!-- Desktop Sidebar -->
    <div
      class="hidden lg:flex md:flex-none absolute bottom-0 bg-white overflow-hidden top-[96px]"
    >
      <CommonSidebar
        :menu-items="menuItems"
        :is-collapsed="isCollapsed"
        :route-params="{ connect: $props.connect ? $props.connect.id : null }"
        :bg-color="'bg-white'"
        class="h-full overflow-hidden"
      />
    </div>
    <div 
      class="flex-1 lg:ml-[240px] overflow-hidden w-full bg-grayCust-50 h-full"
    >
      <div class="p-4 space-y-4">
        <div
          v-if="$props.title"
          class="flex flex-wrap items-center justify-between gap-4"
        >
          <div>
            <h3 class="card-title">
              {{ $props.title }}
            </h3>
            <p
              v-if="$props.subTitle"
              class="card-sub-title"
            >
              {{ $props.subTitle }}
            </p>
          </div>
          <slot name="right" />
        </div>
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
import CommonMenuItem from '@/app/Common/ActionDropdown/CommonMenuItem.vue';
import CButton from '@/app/Common/CommonButton.vue';
import CommonIcon from '@/app/Common/CommonIcon.vue';
import IconButton from '@/app/Common/CommonIconButton.vue';
import CommonListbox from '@/app/Common/CommonListbox.vue';
import CommonPopover from '@/app/Common/CommonPopover.vue';
import CommonSidebar from "@/app/Common/Sidebar/CommonSidebar.vue";
import PageHeader from "@/app/Components/PageHeader.vue";
import RocketIcon from '@/app/Icons/RocketIcon.vue';
import VulnerabilityIcon from '@/app/Icons/VulnerabilityIcon.vue';
import SiteLogs from "@/Common/SiteLogs.vue";
import { dateTimeHumanize } from '@/helpers.js';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import { defineAsyncComponent } from 'vue';
import { route } from 'ziggy-js';

const PluginActionResponseModal = defineAsyncComponent(() => import("@/app/Pages/Connects/PluginActionResponseModal.vue"));

export default {
  name: 'StagingDashboard',
  components: {
    CommonSidebar,
    PageHeader,
    CButton,
    IconButton,
    CommonPopover,
    CommonMenuItem,
    PluginActionResponseModal,
    SiteLogs,
    CommonIcon,
    CommonListbox,
  },
  props: {
    connect: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    subTitle: {
      type: String,
      required: false,
      default: null
    },
  },
  data() {
    return {
      processing: false,
      isCollapsed: false,
      timeOut: null,
      showCodeEditorModal: false,
      confirmingReserveSite: false,
      is_reserved: this.site?.is_reserved,
      activeTabName: "StagingDashboard",
      setting: new Setting(),
      btnLoading: false,
      debugLogs: [],
      isDebugLogOpen: false,
      purgeCacheStatus: '',
      isPurgeCacheSuccess: false,
      isPurgeCacheOpen: false,
      purgePlugins: [],
      plugins: [],
      isEditingSiteLabel: false,
      siteLabel: this.$props.connect?.label,
      menuItems: [
        {
          title: 'Manage Site',
        },
        {
          title: 'Dashboard',
          icon: 'OutlineViewGridIcon',
          path: 'connects.edit.dashboard',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.dashboard'],
        },
        {
          title: 'Health',
          icon: 'OutlinePresentationChartLineIcon',
          path: 'connects.edit.health',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.health'],
          hide: !this.$page.props.TESTING_ENV
        },
        {
          title: 'Plugins',
          icon: 'OutlineCogIcon',
          path: 'connects.edit.plugin',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.plugin'],
        },
        {
          title: 'Themes',
          icon: 'OutlinePuzzleIcon',
          path: 'connects.edit.theme',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.theme'],
        },
        {
          title: 'Reports',
          icon: 'OutlineDocumentReportIcon',
          path: 'connects.edit.reports',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.reports'],
        },
        {
          title: 'Manage',
          image: '/images/manage.svg',
          children: [
            {
              title: 'Users',
              path: 'connects.edit.user',
              params: { connect: this.connect ? this.connect.id : null },
              routename: ['connects.edit.user'],
            },
          ]
        },
        {
          title: 'Staging Sites',
          icon: 'OutlineCubeIcon',
          path: 'connects.edit.sites',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.sites'],
        },
        {
          title: 'Uptime',
          icon: 'OutlineArrowCircleUpIcon',
          path: 'connects.edit.uptime',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.uptime'],
        },
        {
          title: 'Advanced Options'
        },
        {
          title: ' Vulnerability Scanner ',
          icon: VulnerabilityIcon,
          path: 'connects.edit.scanner',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.scanner'],
        },
        {
          title: 'Performance Scanner',
          icon: RocketIcon,
          path: 'connects.edit.performance',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.performance'],
        },
        {
          title: 'Activity Logs',
          icon: 'OutlinePresentationChartLineIcon',
          children: [
            { 
              title: 'View Logs', 
              path: 'connects.edit.view-logs', 
              params: { connect: this.connect ? this.connect.id : null }, 
              routename: ['connects.edit.view-logs']
            },
            {
              title: 'Alert Rules', 
              path: 'connects.edit.alert-rules', 
              params: { connect: this.connect ? this.connect.id : null }, 
              routename: ['connects.edit.alert-rules']
            },
          ]
        },
        {
          title: 'Config Manager',
          icon: 'OutlineAdjustmentsIcon',
          path: 'connects.edit.config.manager',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.config.manager'],
        },
        {
          title: 'Disconnect',
          icon: 'OutlineLinkIcon',
          path: 'connects.edit.disconnect',
          params: { connect: this.connect ? this.connect.id : null },
          routename: ['connects.edit.disconnect'],
        }
      ],
      selectedMenuItem: null,
    }
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ["teamCan", "user", "featureAvailableFrom"]),
    flattenedMenuItems() {
      return this.menuItems.reduce((acc, item) => {
        if (item.children) {
          // Add the parent item first
          const parentItem = {
            ...item,
            icon: item.icon,
            image_url: item.image,
            isParent: true
          };
          // Add children without inheriting parent's icon
          const children = item.children.map(child => ({
            ...child,
            icon: child.icon,
            image_url: child.image,
            isChild: true
          }));
          return [...acc, parentItem, ...children];
        }
        if (item.path) {
          return [...acc, {
            ...item,
            icon: item.icon,
            image_url: item.image
          }];
        }
        return acc;
      }, []);
    }
  },
  mounted() {
    const params = route().params
    if (params?.mark_reserved) {
      // let heading = null;
      let subHeading = null;
      if (!this.teamCan?.reserve_sites && !this.is_reserved) {
        if (this.user.is_team_owner) {
          // heading = trans('upgrade_unlock');
          subHeading =
            this.user.active_plan == null ||
              this.user.active_plan.type == "free"
              ? trans('reserve_site_confirm_active_plan')
              : trans('reserve_site_please_upgrade_plan_register');
        } else {
          // heading = trans('team_quota_exceed');
          subHeading = trans('reserve_feature_not_available_contact_team_owner');
        }

        const msg = {
          subHeading: subHeading,
          planMessage: this.featureAvailableFrom['reserve_sites'] ? trans('this_feature_is_available_in_plan_and_above', { planName: this.featureAvailableFrom['reserve_sites'] }) : null,
          feature: 'reserve_sites',
          triggerRef: "site_edit_deep_link_reserve_site"
        }
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      if ((params.mark_reserved == 'true' && !this.is_reserved) || (params.mark_reserved == 'false' && this.is_reserved)) {
        this.reserveSite()
      }
    }

    // Initialize selected menu item based on current route
    const currentPath = route().current();
    const matchingItem = this.flattenedMenuItems.find(item => 
      item.routename && item.routename.includes(currentPath)
    );
    if (matchingItem) {
      this.selectedMenuItem = matchingItem;
    }
  },
  unmounted() {
    clearTimeout(this.timeOut);
  },
  created() {
    this.appStore.fetchStackCategories().then(() => {
      this.appStore.stackcategoryList.forEach(item => {
        this.plugins.push(...item.products)
      })
    })
  },
  methods: {
    validateName(evt) {
      evt = (evt) ? evt : window.event;
      const charCode = (evt.which) ? evt.which : evt.keyCode;
      if (charCode === 13) {
        if (this.siteLabel) {
          this.saveLabel()
        }
        return true;
      }
      if (evt.key === "Escape") {
        this.isEditingSiteLabel = false
        return true;
      }
      const pattern = /^[\p{Letter}\d\-_\s]*$/u;
      if (!pattern.test(evt.key)) {
        evt.preventDefault();
      }
    },
    saveLabel() {
      this.isEditingSiteLabel = false
      axios.patch(`/api/v2/connects/${this.$props.connect.id}/update-label`, {label: this.siteLabel})
        .then(() => {
          const message = {
            heading: this.$t('success'),
            subHeading: this.$t('site_label_updated'),
            type: "success",
          };
          this.appStore.setNotification(message);
        })
    },
    addSiteOpenEvent(connectId) {
      this.appStore.addSiteOpenedEvent(connectId, window.location.href, "connect")
    },
    handleButtonClick() {
      // Navigate to the desired URL
      const url = this.$props.connect.url + '/wp-admin/tools.php?page=instawp';
      window.open(url, '_blank');
    },
    autoLoginConnect() {
      this.btnLoading = true;
      axios
        .post("/api/v1/connects-autologin", { id: this.connect.id })
        .then((response) => {
          if (response.data.status) {
            window.open(response.data.data.login_url);
          }
          if (!response.data.status) {
            const message = {
              heading: trans('failed'),
              subHeading: response.data.data.message,
              type: "error",
            };
            this.appStore.setNotification(message);
          }
        })
        .catch(() => {
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    databaseManager() {
      // this.isClearCache = true;
      axios
        .get("/api/v2/connect/" + this.$props.connect.id + "/database-manager")
        .then((response) => {
          if (response.data?.login_url) {
            window.open(response.data.login_url, '_blank');
          } else {
            const message = {
              heading: trans('failed'),
              subHeading: trans('database_manager_failed'),
              type: "error",
            };
            this.appStore.setNotification(message);
          }
        })
        .catch(() => {
          // let message = {
          //     heading: trans('failed'),
          //     subHeading: (error.data && error.data.data.message) || error.response.data.message || error.data.message || trans('database_manager_failed'),
          //     type: "error",
          // };
          // this.appStore.setNotification(message);
        })
        .finally(() => {
          // this.isClearCache = false;
        });
    },
    debugLog() {
      axios
        .get("/api/v2/connect/" + this.$props.connect.id + "/debug-log")
        .then((response) => {
          if (response.data?.success === false) {
            const message = {
              heading: trans('failed'),
              subHeading: response.data.message,
              type: "error",
            };
            this.appStore.setNotification(message);
          } else {
            this.debugLogs = response.data
            this.isDebugLogOpen = true
          }
        })
        .catch((error) => {
          const message = {
            heading: trans('failed'),
            subHeading: error.data.data.message || error.data.message || trans('debug_log_failed'),
            type: "error",
          };
          this.appStore.setNotification(message);
        })
        .finally(() => {

        });
    },
    goToReports() {
      localStorage.setItem('showReports', 'true');
      this.$inertia.visit(route('connects.edit.reports', this.$props.connect.id))
    },
    cacheClear() {
      this.btnLoading = true;
      this.purgePlugins = []
      this.purgeCacheStatus = 'Purge Cache in progress.'
      this.isPurgeCacheOpen = true
      axios
        .get("/api/v1/connect/" + this.$props.connect.id + "/cache-clear")
        .then((response) => {
          if (response.data.statusCode == 200) {
            this.purgeCacheStatus = 'Cache Purged'
            this.isPurgeCacheSuccess = true
            this.purgePlugins = response.data.response
            if (this.purgePlugins.length) {
              this.purgePlugins = this.purgePlugins.map(item => {
                const plugin = this.plugins.find(plugin => item.slug === plugin.source_location)
                if (plugin) {
                  item.name = plugin.name
                  item.icon = plugin.icon_url
                }
                return item
              });
            }
          } else {
            this.purgeCacheStatus = 'Purge Cache Failed.'
            this.isPurgeCacheSuccess = false
            const message = {
              heading: trans('failed'),
              subHeading: trans('cache_clear_failed'),
              type: "error",
            };
            this.appStore.setNotification(message);
          }
        })
        .catch((error) => {
          this.isPurgeCacheSuccess = false
          this.purgeCacheStatus = 'Purge Cache Failed.'
          console.log(error)
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    fetchHeartBeat() {
      axios
        .get("/api/v2/connects/" + this.$props.connect.id + "/fetch-heartbeat")
        .then((response) => {
          const message = {
            heading: trans('success'),
            subHeading: response.data.message,
            type: "success",
          };
          this.appStore.setNotification(message);
          this.$inertia.reload();
        }).catch(() => {

        })
    },
    closePurgeCacheModal() {
      this.isPurgeCacheOpen = false
      this.clearCacheTimeout = setTimeout(() => {
        this.isPurgeCacheSuccess = false
        this.cache_item_id = null;
        this.purgePlugins = []
      }, 500);
    },
    dateTimeHumanize: dateTimeHumanize,
    openDb(siteId) {
      const that = this;
      that.opening_db = true;
      that.appStore.getDbEditor(siteId)
        .then(() => {
          that.processing = false;
        })
    },
    fetchCodeEditor(siteId) {
      const that = this;
      that.appStore.getCodeEditor(siteId).then(() => {
        that.processing = false;
      })
    },
    viewLog() {
      const child = this.$refs.siteLogModalEdit;
      child.toggle();
    },
    showCodeEditorDialog() {
      this.showCodeEditorModal = true
    },
    hideCodeEditorDialog() {
      this.showCodeEditorModal = false;
    },
    handleMenuItemSelect(menuItem) {
      if (menuItem && menuItem.path) {
        // Check if we're already on this route to prevent duplicate navigation
        const currentPath = route().current();
        if (menuItem.routename && menuItem.routename.includes(currentPath)) {
          return;
        }

        // Update selected item
        this.selectedMenuItem = menuItem;
        
        // Use Inertia's visit method with preserveState to prevent full page refresh
        this.$inertia.visit(
          route(menuItem.path, { connect: this.$props.connect.id }), 
          { 
            preserveState: true,
            preserveScroll: true,
            onSuccess: () => {
              // Keep the menu open after navigation
              this.$nextTick(() => {
                // Re-initialize the selected item after navigation
                const newPath = route().current();
                const matchingItem = this.flattenedMenuItems.find(item => 
                  item.routename && item.routename.includes(newPath)
                );
                if (matchingItem) {
                  this.selectedMenuItem = matchingItem;
                }
              });
            }
          }
        );
      }
    },
  }
};
</script>

