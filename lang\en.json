{"2_months_free": "2 Months Free", "30_days_ago": "30 days ago", "403_forbidden": "403: Forbidden", "404_page_not_found": "404: Page Not Found", "419_page_expired": "419: <PERSON> Expired", "500_server_error": "500: Server Error", "503_service_unavailable": "503: Service Unavailable", "_zip_url": ".zip URL", "a_new_verification": "A new verification link has been sent to the email address you provided during registration.", "ability_to_reserve_sites": "Ability to Reserve Sites", "abort_transfer": "Abort Transfer", "aborted": "Aborted", "about": "About", "ac_suspend_pending_invoice": "Your account has been suspended due to non-payment for your subscription.", "ac_suspend_pending_invoice_team": "Your account has been suspended due to non-payment for subscription.", "accent": "Accent", "accept": "Accept", "access_key_retrived": "Access Key retrived.", "access_now": "Access now", "access_token": "Access Token", "access_website_sftp_ssh": "Access website though SFTP or SSH.", "account": "Account", "account_applied_info": "Your account is applied to use this suffix domain, Please wait for approval.", "account_created_successfully": "Account created successfully", "account_id": "Account Id", "account_list_fetched_successfully": "Account List Fetched Successfully", "account_not_approve_info": "Your account is not approved to use this suffix domain, Please fill the form to get verified.", "account_rejected_info": "Your account is rejected to use this suffix domain, Please contact to support team if you have any queries.", "account_upgraded": "🎉  Account Upgraded", "accounts": "Accounts", "action": "action", "action_type": "Action Type", "actions": "Actions", "actions_file": "Actions yml File", "activate": "Activate", "activated": "Activated", "active": "Active", "active_site_content": "Any website which is shown in the \"Sites\" screen is counted as active site, it can be unexpired site, or expired site or reserved website. If you delete a site, its removed from the \"active\" site count.", "active_site_title": "What is an Active Site?", "active_sites": "Active Sites", "active_theme": "Active Theme", "activity_log_stored_successfully": "Activity log stored successfully", "activity_logs": "Activity Logs", "activity_logs_disabled": "Activity logs is disabled.", "activity_logs_enabled": "Activity logs is enabled.", "add": "Add", "add_a_card": "Add a Card", "add_additional": "Add additional security to your account using two factor authentication.", "add_chrome": "Add to Chrome", "add_comma_separated_email_addresses": "Add comma separated email addresses", "add_command": "Add Command", "add_folder": "Add Folder", "add_hosting": "Add Hosting", "add_label": "Add Label", "add_manually": "Add Manually", "add_new": "Add New", "add_new_card": "Add New Card", "add_new_commands": "Add New Commands", "add_new_config": "Add New Configuration", "add_new_repository": "Add New Repository", "add_new_ssh_key": "Add SSH/SFTP User", "add_new_team_member": "Add a new team member to your team, allowing them to collaborate with you.", "add_on": "add on", "add_payment_method": "Add Payment Method", "add_plugin": "Add plugin slug from wp.org ", "add_product": "Add Product", "add_provider": "Add Provider", "add_referral_badge": "Add a referral badge", "add_referral_badge_subtitle": "Embed a visual badge on your website or content to link", "add_site": "Add Site", "add_slack": "Add to Slack", "add_ssh_key": "Add your public SSH Key", "add_ssh_sftp_user": "Add SSH/SFTP User", "add_tag": "Add Tag", "add_team_member": "Add Team Member", "add_template": "Add Template", "add_snapshots": "Add Snapshot", "add_to_folder": "Add to Folder", "add_to_wordpress": "Add to WordPress", "add_user": "Add User", "added": "Added.", "added_cards": "Added Cards", "added_on": "Added on", "addon": "<PERSON><PERSON>", "addons": "Addons", "addons_customizer": "Addons Customizer", "address_line_1": "Address Line 1", "address_line_2": "Address Line 2", "adjectives": "Adjectives", "admin_email": "Admin email", "admin_login": "<PERSON><PERSON>", "administrator": "administrator", "advance": "Advance", "advance_config_not_allow": "You are not allowed to set advance configurations please upgrade your plan.", "advance_config_not_allow_team": "You are not allowed to set advance configurations please contact team owner.", "advance_configurations": "Advance Configurations", "advance_deployments": "Advance Deployments", "advance_opt": "Advance Options", "advanced": "Advanced", "advanced_options": "Advanced Options", "advanced_plan": "Advanced Plan", "advanced_site": "Advanced Site", "affiliate_code": "Affiliate code:", "after_end_of_trial_period": "after end of trial period", "agree_to_change_integration_status": "Yes, I agree to change integration status", "agree_to_delete_integration_permanently": "Yes, I agree to delete integration, permanently", "agree_to_delete_site_permanently": "Yes, I agree to delete sites listed above, permanently", "agree_to_delete_template_permanently": "Yes, I agree to delete templates listed above, permanently", "agree_to_delete_snapshot_permanently": "Yes, I agree to delete snapshots listed above, permanently", "agree_to_our": "You agree to our", "ai_provider_not_enabled": "The selected AI provider is not enabled.", "ai_providers_fetched_successfully": "AI providers fetched successfully.", "ai_providers_not_found": "AI providers not found.", "alert": "<PERSON><PERSON>", "alert_rule_created_successfully": "Alert rule created successfully", "alert_rule_deleted_successfully": "Alert rule deleted successfully", "alert_rules": "Alert <PERSON>", "alert_rules_description": "Track important activities inside your WordPress website.", "alias": "<PERSON><PERSON>", "all": "All", "all_migrations": "All Migrations", "all_part_team": "All of the people that are part of this team.", "all_sites": "All sites", "all_your_partner_perks_in_a_single_pane": "All your partner perks in a single pane.", "allow_support_access": "Allow Support Access", "allow_url_fopen": "Allow_url_fopen", "already_configured": "Already Configured", "already_connected": "Already Connected", "already_extended_lifetime": "Site lifetime already extended upto :days days for this site, total time remaining is :remaining_days.", "already_staging_site": "This site is a staging site", "amount": "Amount", "analytics": "Analytics", "analyze": "Analyze", "and": "and", "any_amount_deducted_refunded": "Any amount deducted will be refunded within 4-7 days. If you would like to proceed, you can retry this any time by clicking on Update Plan.", "api_key": "API Key", "api_key_name": "API Key Name", "api_public_key": "Api Public Key", "api_quota_limit": "API Quota Limit", "api_quota_limit_error": "API quota limit has been exhausted, Please buys more addon", "api_rate_limit": "API Rate Limit", "api_token": "API Token", "api_url": "Api Url", "app": "App", "app_connected_successfully": "App Connected Successfully", "app_enterprise_notice": " For increased limits in active sites, restore count, team member count, etc, we offer a variety of enterprise plans. In these plans, you can opt for domain white-labeling & even go for a dedicated server for your customers. To know more about,", "app_integration_deleted": "App integration Deleted", "app_user_token": "App User Token", "applied": "Applied", "apply": "Apply", "apply_coupon": "Apply Coupon", "apply_offer": "Apply Offer", "approve": "Approve", "apps": "Apps", "are_you_sure": "Are you sure?", "are_you_sure_export": "Are you sure you want to export", "are_you_sure_you_want_to_clone_site": "Are you sure you want to clone this site? It may take some time to reflect in your list.", "are_you_sure_you_want_to_continue_migrating_your_site": "Are you sure you want to continue migrating your site? This will completely override the destination website.", "are_you_sure_you_want_to_create_backup": "Are you sure you want to create :type backup? It may take some time to reflect in your list.", "are_you_sure_you_want_to_delete_the_integration_permanently": "Are you sure you want to delete the Integration Permanently?", "are_you_sure_you_want_to_delete_the_site_permanently": "Are you sure you want to delete the site Permanently?", "are_you_sure_you_want_to_delete_waas": "Are you sure you want to delete :waas waas permanently", "are_you_sure_you_want_to_disconnect_this_site": "Are you sure you want to disconnect this site?", "are_you_sure_you_want_to_remove_backup": "Are you sure you want to remove :type backup? It may take some time to reflect in your list.", "are_you_sure_you_want_to_remove_domain_alias": "Are you sure you want to remove :domain Alias?", "are_you_sure_you_want_to_set_domain_alias_as_primary": "Are you sure you want to set :domain as Primary domain? This will also trigger a search and replace from :old_domain to :new_domain automatically.", "are_you_sure_you_want_to_start_over_suffix_domain_will_be_removed": "Are you sure you want to start over, Suffix domain will be removed.", "are_you_sure_you_want_to_status_the_integration": "Are you sure you want to :status the Integration?", "are_your_sure_you_want_to_restore_this_backup": "Are you sure you want to restore this backup? It may take some time to Restore Site. Please purge cache if your data is not showing.", "as_seen": "As seen on", "attaching_database_user": "Attaching Database to User", "attachment_deleted": "Attachment :count deleted :timestamp", "attachment_keyword": "Attachment Keyword", "attachment_updated": "Attachment :count updated :timestamp", "attachment_uploaded": "Attachment :count uploaded :timestamp", "attachments": "Attachments", "auth.disable": "Sorry, you are not allowed to login", "auth.password": "The provided password is incorrect.", "authentication": "Authentication", "authors": "Authors", "auto_login": "Auto Login", "auto_login_content": "You are being redirected to the WordPress admin panel.", "auto_login_wpAdmin": "Auto Login to wp-admin", "auto_login_wp_admin": "Auto Login to wp - admin", "auto_update_core": "Auto Update Core", "auto_update_disabled": "Auto update for :name disabled", "auto_update_enabled": "Auto update for :name enabled", "auto_update_fetch_success": "Auto update fetch successfully", "auto_update_set_success": "Auto updates for Core set successfully", "auto_updates": "Auto Updates", "automated": "Automated", "automated_migrate_tool_for_wordpress": "Automated Migrate Tool for WordPress", "automated_performance_scan_disabled": "Automated Performance Scan Disabled.", "automated_performance_scan_enabled": "Automated Performance Scan Enabled.", "automated_updates_off": "Automated Updates Off", "automated_vulnerability_scan_disabled": "Automated Vulnerability Scan Disabled.", "automated_vulnerability_scan_enabled": "Automated Vulnerability Scan Enabled.", "automatically": "Automatically Sync Changes", "automatically_install": "InstaWP will automatically install and configure multi site WordPress.", "available": "Available", "available_credits": "Available Credits", "available_credits_tooltip": "These are the credits you can use to purchase any plan or add-on. Ready to upgrade? You’ve got credits waiting!", "average_response_bytes_of_each_response": "Average bytes in each response.", "average_response_time": "Average Response Time", "average_response_time_description": "Average time of each response.", "avg_response": "Avg. Response", "back": "Back", "background": "Background", "backup": "Backup", "backup_now": "Backup Now", "backup_providers_fetched": "Backup Providers fetched successfully.", "backup_quota_reached": "Backup quota reached.", "backup_source": "Backup Source", "backups": "Backups", "balance_credit": "Balance Credit", "balance_credit_tooltip": "Unused credit amount by your referral.", "basic": "Basic", "basic_information_about_this_website": "Basic information about this website.", "basic_plan": "Basic Plan", "basic_site": "Basic Site", "begin_migration": "Begin migration", "below_site_remove_server": "Below sites will be removed from server.", "below_template_removed": "Below templates will be removed.", "below_snapshot_removed": "Below snapshots will be removed.", "benefits": "benefits", "beta": "Beta", "beta_program": "Beta Program", "beta_program_join": "I want to join the beta program", "beta_program_sub_heading": "Get latest features and updates via our Beta program", "bill_date": "<PERSON>", "billed": "billed", "billing_address": "Billing address", "billing_details": "Billing Details", "billing_details_updated_successfully": "Billing details updated successfully.", "billing_email": "Billing Email", "billing_email_placeholder": "Leave blank to use the same email as your registered email", "body": "Body", "bought_as_addon": "Bought as addon", "branch": "Branch", "branding": "Branding", "browse_addons": "B<PERSON>e Addons", "browser_session": "Browser Sessions", "build_site_directly": "Build sites directly in your hosting panels using direct Integration.", "building_your_website": "Building your website ..", "bulk_edit": "Bulk Edit", "bulk_update_list_fetched_successfully": "Bulk update list fetched successfully", "buy": "Buy", "buy_now": "Buy Template", "buy_plan": "Buy Plan", "bypass_pool": "Bypass Pool", "c_name": "CNAME (Auto Renew)", "cache_clear": "cache clear", "cache_clear_failed": "P<PERSON> Failed.", "cache_clear_success": "<PERSON><PERSON> purged successfully.", "call_link_unique_waas_checkout": "Calling this API URL will auto provision a one-time WaaS checkout link", "can_create_new_sites_can_only_view_&_delete_own_sites": "Can create new sites, can only view & delete own sites.", "can_invite_team_members_view_&_delete_team_sites_add_&_edit_integrations": "Can invite team members, view & delete team sites, add & edit integrations.", "can_not_delete_site_with_on_demand_backups": "Can not delete site with on demand backups. Please delete On Demand backups first.", "can_not_fetch_heartbeat_data": "Can't fetch heartbeat data", "can_not_fetch_wordpress_users": "Cannot fetch WordPress Users.", "can_not_set_live_on_demand_backups": "Can not set staging site live with on demand backups. Please delete On Demand backups first.", "canada": "Canada", "cancel": "Cancel", "cancel_plan": "Cancel Plan", "cancel_retry_payment": "Cancel Retry Payment", "cancel_subscription": "Cancel Subscription", "cancel_subscription_content": "Cancelling a subscription will downgrade the account to the free plan at the end of the billing period. All running backup services will also be disabled.", "cancel_subscription_survey_completed": "Cancel subscription survey completed successfully", "cancel_subscription_survey_offer_applied": ":percent% Offer Applied Successfully", "cancel_subscription_survey_offer_applied_text": "We have applied the offer to your current subscription", "cancel_subscription_survey_offer_cta": "Claim Now", "cancel_subscription_survey_offer_forever": "Don't Leave Yet!", "cancel_subscription_survey_offer_forever_text": ":percent% off on your current plan forever", "cancel_subscription_survey_offer_once": "Don't Leave Yet!", "cancel_subscription_survey_offer_once_text": ":percent% off on your next bill", "cancel_subscription_survey_offer_repeating": "Don't Leave Yet!", "cancel_subscription_survey_offer_repeating_text": ":percent% off on your current plan for next :text", "cancel_subscription_survey_submit_step_cancel_cta": "Continue to Cancel", "cancel_subscription_survey_submit_step_cancel_text": "I understand that the <span class='text-grayCust-1740 font-medium'>price of InstaWP may increase</span> in the future, and I may not be able to rejoin at my current, locked-in price.", "cancel_subscription_survey_submit_step_continue_cta": "Apply :percent% Discount", "cancel_subscription_survey_submit_step_continue_text": "I have changed my mind, apply the :percent% offer and continue my subscription.", "cancellation_content": "You can cancel at any point of time. Even after cancellation you will be able to use the paid features till the end of your billing period.", "cancellation_title": "Cancellation", "cannot_delete_ssh_key_is_used_in_site": "Cannot delete SSH key, it is used in site", "card": "Card", "card_details": "Card Details", "card_ending_with_xxxx": "Card Ending with XXXX-", "card_holder_name": "Card Holder Name", "card_is_added": "card is added", "card_number": "Card number", "card_verify_notice": "As part of the verification process, a temporary $1 charge will be applied to your card. This charge will be refunded within a 3-4 business days.", "cart": "<PERSON><PERSON>", "cart_coupon_code": "Coupon code", "cashier": "Cashier", "categories": "Categories", "categories_fetched_successfully": "Categories fetched successfully.", "category": "Category", "cert_exp": "<PERSON>rt<PERSON>pi<PERSON>", "change": "Change", "change_language": "Change Language", "change_plan": "Change Plan", "change_settings": "Change Settings", "change_suffix_domain": "Change Suffix Domain", "change_wp_admin_password": "Randomize WP Admin password", "change_wp_username_password_wp_admin_please_update_here": "If you change WP username/password in wp-admin, please update here too", "changes": "Changes", "changes_will_be_made_globally": "Changes will be made globally.", "changing": "Changing", "channel": "Channel", "charge": "Charge", "check_ssl": "Check SSL", "checking_host": "Checking Host", "child_url_already_linked": "Child Url already linked", "child_url_must_be_http": "Child Url must be starts with http:// or https://", "child_url_not_found": "Child Url connect not found or not connected to your account", "choose": "choose", "choose_any_plan_to_activate_your_trial_days_days_trial_make_sure_a": "Choose any plan to activate your :trialDays days trial, make sure a", "choose_command": "Choose Command", "choose_configuration": "Choose configuration", "choose_data_center": "Choose Data center", "choose_external_services": "Choose an external service", "choose_file": "Choose file", "choose_hosting_package": "Choose a Hosting Package", "choose_php_version": "Choose PHP version", "choose_plan": "Choose <PERSON>", "choose_pricing_plan": "Choose Pricing Plan", "choose_provider": "<PERSON><PERSON> Provider", "choose_select_template_for_waas": "Please choose one or more templates for this WaaS", "choose_support_providers": "Choose from supported providers", "choose_template": "Choose a template", "choose_website_setting": "Choose Website Setting", "choose_wp_version": "Choose WP version", "choose_your_plan": "Choose your plan", "chrome": "Chrome", "cities_list_fetched": "Cities list fetched", "city": "City", "city_placeholder": "Select a city", "clear_all": "Clear all", "cleared_survey_paused_successfully": "Cleared paused survey successfully", "click_here": "Click here", "click_to_upload": "Click to upload or drag and drop", "clicks": "<PERSON>licks", "clicks_tooltip": "Keep track of how many times your referral link has been clicked. Every click gets you closer to earning more rewards!", "client": "Client", "client_id": "Client ID", "client_reporting": "PDF Client Reports", "client_secret": "Client Secret", "clone": "<PERSON><PERSON>", "clone_failed_txt": "Failed to create a site backup.", "clone_site": "Clone Site", "cloning": "Cloning", "close": "Close", "close_panel": "Close panel", "cloudflare": "cloudflare", "code": "Code", "code_editor": "Code Editor", "collect_email": "Collect Email", "collect_email_subtitle": "Provide slug or .zip files for plugins or theme you wish to pre-install on new sites", "collect_email_subtitle2": "Toggle to enable or disable the collection of guest user emails.", "collect_name": "Collect Name", "collect_name_email": "When monetization is activated “Collect Name” and “Email Required” can’t be disabled.", "comma_separated": "Comma Separated", "command_created_successfully": "Command created successfully", "command_deleted_successfully": "Command deleted successfully", "command_executing": "Commands Executing", "command_fetched_successfully": "Command fetched Successfully.", "command_imported_successfully": "Command imported successfully", "command_library": "Command Library", "command_permanently_removed": "The command will be permanently removed.", "command_updated_successfully": "Command updated successfully", "commands": "Commands", "commands_name": "Commands Name", "common_options": "Common Options", "community": "Community", "company": "Company", "company_name": "Company Name", "company_name_placeholder": "Enter your company name", "complete": "complete", "completed": "Completed", "completed_migrations": "Completed Migrations", "component": "Component", "config": "Config", "config_manager": "Config Manager", "config_name": "Configuration Name", "config_not_allow": "You are not allowed to make changes in this configuration please upgrade your plan.", "config_site": "Config Site", "configs_fetched_successfully": "Configurations Fetched Successfully", "configurable_integrations_fetched_successfully": "Configurable integrations fetched successfully", "configuration": "Configuration", "configuration_copied_successfully": "Configurations Copied Successfully", "configuration_deleted_successfully": "Configurations Deleted Successfully", "configuration_saved_successfully": "Configurations Saved Successfully", "configurations": "Configurations", "configure": "Configure", "configuring_site": "Configuring Site", "confirm": "Confirm", "confirm_message_move_tenants_versions": "Do you wish to move tenants from versions (:previous_versions) to :current_version?", "confirm_message_set_as_production": "Do you wish to mark this version as “production”? All new tenants will be created under this version from here on.", "confirm_message_template_wpcs": "Do you wish to sync InstaWP template (:template_name) to your wildcloud account (:account_name)? This will create a new version with the contents of InstaWP template. (You may need to mark it production manually after the sync is complete).", "confirm_password": "Confirm Password", "confirm_remove_mailtrap_integration": "Are you sure you want to remove mailtrap integration from this site?", "confirm_the_site_url_before_deletion": "Confirm the site URL before deletion", "connect": "Connect", "connect_a_site": "Connect a site", "connect_deleted_successfully": "Connect deleted successfully", "connect_features": "Connect Features", "connect_hosting_account": "Connect Hosting Account", "connect_hosting_provider": "Connect Hosting Provider", "connect_is_managed": "This Connected site is now managed.", "connect_is_unmanaged": "This Connected site is now unmanaged.", "connect_new": "Connect New", "connect_new_account": "Connect New Account", "connect_not_found": "Connect Site Not Found.", "connect_plans_fetched": "Connect plans fetched", "connect_plugin_completed": "WP Connect Plugin Completed", "connect_provider": "Connect Your Hosting Provider", "connect_provider_account": "Hosting Account", "connect_restored_successfully": "Connect restored successfully", "connect_settings": "Connect Settings", "connect_site": "Connect Site", "connect_site_not_disconnect": "Connect sites sync is in progress. Please try after sometime", "connect_sync_fetched": "Connect Sync Fetched", "connect_sync_initiated": "Connect Sync is Initiated.", "connect_sync_quota_fetched": "Connect Sync Quota Fetched.", "connect_sync_updated": "Connect Sync is Updated.", "connect_syncs_fetched": "Connect Syncs Fetched", "connect_template": "Connect Template", "connect_to_install_migration_plugin": "Please use the Connect button to auto-install our migration plugin on both the source & destination websites.", "connect_url_updated_successfully": "Connect url updated successfully", "connect_website": "Connect Website", "connected": "Connected", "connected_providers": "Connected Providers", "connected_site_content_add_new": "Get started by connecting production sites", "connected_site_found": "Connected site found", "connected_site_successfully": "Connected Site Fetch Successfully", "connected_sites": "Connected Sites", "connecting": "Connecting", "connection_create_successfully": "Connection Created Successfully", "connection_details": "Connection Details", "connection_form": "Connection Form", "connection_test_successfully": "Connection Test Successfully.", "connection_type": "Connection Type", "connections": "Connections", "connects": "Connects", "contact_list_fetched_successfully": "Contact List Fetched Successfully", "contact_list_id": "Contact List Id", "contact_sales": "Contact Sales", "contact_support": "Contact Support", "contact_support_for_assistance": "Contact Support for Assistance", "contact_support_link": "Contact Support Link", "contact_team_owner": "Contact team owner", "contact_us": "Contact Us", "content_add_new": "Get started by adding a new app integration from button above.", "content_delete_activity_log_count": "{1} Do you wish to delete activity log?|[2,*] There are :count activity logs selected, Do you want to delete them?", "content_domain": "Are you sure you want to remove this domain from the site.", "content_domain_name": "Are you sure you want to remove :domain domain from the site.", "content_subscription": "You have a cancelled subscription, however you have a grace period till it expires,click here to", "content_template": " Are you sure you want to remove this backup template?", "content_snapshot": " Are you sure you want to remove this backup snapshot?", "content_template_count": "There are :site_count active sites for this template, do you wish to delete all the sites along with the template?", "content_snapshot_count": "There are :site_count active sites for this snapshot, do you wish to delete all the sites along with the snapshot?", "content_template_shared": "There is :activeSiteCount active site for this template, do you wish to delete all the sites along with the template?", "content_snapshot_shared": "There is :activeSiteCount active site for this snapshot, do you wish to delete all the sites along with the snapshot?", "continue": "Continue", "continue_to_website": "Continue to website", "continue_with": "Continue with", "convert_now": "Convert Now", "cookie": "<PERSON><PERSON>", "copied": "<PERSON>pied", "copied_link": "Copied link", "copy": "Copy", "copy_and_share_to_enjoy_trial_partner_program": "Copy and share to enjoy Trial Partner Program", "copy_embed": "<PERSON><PERSON> Embed", "copy_key": "Copy key", "copy_link": "Copy Link", "copy_this_site": "Copy this site to my account", "copy_token": "Please copy your new API token. For your security, it wont be shown again.", "core": "Core", "core_fake": "<PERSON>", "count_addons": ":count<PERSON><PERSON><PERSON>s", "count_yr": "$:year/yr", "countries_list_fetched": "Countries list fetched", "country": "Country", "country_placeholder": "Select a country", "coupon_applicable_only_for_new_subscription": "The coupon is applicable only for new subscription.", "coupon_applied_successfully": "Coupon applied successfully", "coupon_code": "Enter Coupon Code", "coupon_code_applied": "Coupon code applied", "coupon_error": "Coupon Error", "coupon_not_applicable_notice": "The coupon is not applicable for the selected plan. Please remove the coupon or choose a different plan.", "coupon_trail_not_allow": "Please note: Using this coupon code excludes the free trial and incurs immediate charges. Thank you for understanding!", "cpu_cores": "CPU Cores", "create": "Create", "create_api": "Create API Token", "create_general_token": "Create General <PERSON>", "create_new": "Create New Team", "create_new_2": "Create New", "create_new_from_template": "Create a new from template", "create_new_from_snapshot": "Create a new from snapshot", "create_new_site": "Create New Site", "create_public_sandbox_with_templates": "Create public sandbox with Shared templates", "create_public_sandbox_with_snapshots": "Create public sandbox with Shared snapshots", "create_site": "Create Site", "create_site_from_scratch_pro_tip": "Pro tip: Mark this template as \"Instant Template\" for instant site\r\n    creation.", "create_team": "Create Team", "create_template": "Create Template", "create_snapshot": "Create Snapshot", "create_these_NS_records": "Create These NS Records", "create_user": "Create User", "created": "Created.", "created_2": "Created", "created_at": "Created At", "created_days_ago_by_vikas_singhal": "Created :createdDays days ago, by vika<PERSON> singhal", "created_maximum_number_template_register": "You have created the maximum number of templates allowed under your current plan. To create more templates please upgrade your plan.", "created_maximum_number_snapshot_register": "You have created the maximum number of snapshots allowed under your current plan. To create more snapshots please upgrade your plan.", "creating_a_new_site": "Creating a new site", "creating_backup": "Creating a site backup.", "creating_backup_failed": "Cloning Failed", "creating_database": "Creating Database", "creating_database_": "Creating Database...", "creating_database_user": "Creating Database User", "creating_migration": "Creating Migration", "creating_migration_finished": "Creating Migration Finished", "creating_site": "Creating Site...", "creating_staging": "Creating Staging", "creating_sub_domain_record": "Creating Sub Domain Record", "creating_template": "Creating Template", "creating_snapshot": "Creating Snapshot", "creating_version": "Creating New Version...", "credit_applied_tooltip": "This invoice was paid with :amount credit applied", "credit_card": "Credit Card", "credit_debit_card": " Your Credit/Debit Card", "credit_history": "Credit History", "credited_at": "Credited At", "credited_successfully": "Free Credits Added.", "credits": "Credits", "credits_help_text": "Negative values are treated as a credit (a reduction in the amount owed by the customer) that you can apply to the next invoice. Positive values are treated as a debit (an increase in the amount owed by the customer to you) that you can apply to the next invoice.", "cretical_operation_limit_api": "Daily Critical operation quota exceed, :limit operations allowed under your current plan.", "crisp": "<PERSON><PERSON><PERSON>", "curated_configurations": "Curated Configurations", "current_earning": "Current earning", "current_password": "Current Password", "current_plan": "Current Plan", "current_scan": "Current <PERSON>an", "current_site_url": "Current site URL", "current_subscription": "Current Subscription", "custom": "custom", "custom_color": "Custom Color", "custom_css": "Custom CSS", "custom_domain": "Custom Domain", "custom_domain_not_allow": "You are not allowed to map domain please upgrade your plan.", "custom_domain_not_allow_team": "You are not allowed to map domain please contact team owner.", "custom_hostname_details": "Use a custom combination of nouns and adjectives to create custom list of hostnames to be used for creating new sites.", "custom_hostnames": "Custom Hostnames", "custom_redirect_for_demo_site": "Custom Redirect for Demo Site [ Go Live ]", "custom_site_name": "Custom site name", "custom_suffix_domain": "All sites in this team will have a custom suffix domain.", "customer_email": "Customer <PERSON><PERSON>", "customer_emails": "Customer Emails", "customize_email": "Customize Email", "customize_magic_login_user_role": "Customize Magic Login User Role", "customize_privacy_policy": "Customize Privacy Policy", "cvc": "CVC", "daily": "Daily", "danger_dialog_message_delation": "Are you sure? Site delation process will destroy site from server", "dashboard": "Dashboard", "dashboard_statistics_fetched_successfully": "Dashboard statistics fetched successfully", "dashboard_subtitle": "Site credentials, information, tools, and settings", "data_center": "Data Center", "data_fetched_successfully": "Data Fetched successfully.", "data_invalid": "The given data was invalid.", "data_not_available": "The URL doesn't have enough performance data available.", "data_not_found": "Data Not Found.", "data_retrived": "Data retrived.", "database": "Database", "database_backup": "Database Backup", "database_manager_failed": "Database Manager Failed.", "database_pulling_is_completed": "Database pulling is completed", "database_pulling_is_running": "Database pulling is running", "database_pushing_is_completed": "Database pushing is completed", "database_pushing_is_running": "Database pushing is running", "date": "Date", "day": "Day", "days": "{1} :count day|[2,*] :count days", "days_2": "Days", "days_ago": ":days days ago", "db_backup": "DB Backup", "db_backup_zip_files": "Database Backup Zip Files", "db_editor": "DB Editor", "db_size": "Database Size", "deactivate": "Deactivate", "deactivated": "Deactivated", "decline": "Decline", "dedicated_server": "Dedicated server", "default": "<PERSON><PERSON><PERSON>", "default_magic_login_user": "Default Magic Login User", "default_payment_method": "The default payment method will be used for any biling purposes.", "default_user_for_magic_login_set_successfully": "Default User for Magic Login set successfully", "defaults": "De<PERSON>ults", "delete": "Delete", "delete_account": "Delete Account", "delete_account_confirm_content": "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "delete_account_content": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "delete_activity_log": "Delete Activity Log", "delete_alias_successfully": "Delete alias successfully", "delete_api": "Delete API Token", "delete_hosting_connection": "Are you sure you want to remove :name hosting connection?", "delete_not_allow": "You are not allow to delete because of active orders.", "delete_not_allow_active_order": "You are not allow to delete because of active orders.", "delete_not_allow_listed_store": "You are not allow to delete because it is used in Store.", "delete_not_allow_waas": "You are not allow to delete because of active WaaS templates.", "delete_report": "Delete Report", "delete_selected": "Delete Selected", "delete_site": "Delete Site", "delete_site_versions": "Delete Site Versions", "delete_sites": "Delete Sites", "delete_templates": "Delete Templates", "delete_snapshots": "Delete Snapshots", "delete_ssh_sftp_user": "Are you sure you want to remove :name SSH/SFTP user?", "delete_team": "Delete Team", "delete_token": "You may delete any of your existing tokens if they are no longer needed.", "deletion_started_successfully": "Deletion :resource started successfully", "demo": "Demo", "demo_site": "Demo Sites", "demo_site_published_and_is_ready_to_use": "Your demo website is now published as a hosted website and is ready to use!", "demo_text": "Demo text", "deny": "<PERSON><PERSON>", "deploy": "Deploy", "deployments": "Deployments", "description": "Description", "description_here": "Description Here", "deselect_all": "Deselect All", "deselect_all_resources": "Deselect all :number :resources", "desktop": "Desktop", "desktop_scan_failed": "Desktop Scan Failed", "destination": "Destination:", "destination_folder": "Destination Folder", "destination_site_url": "Destination site URL", "destination_url": "Destination URL", "destination_website": "Destination Website", "destination_website_will_be_completely_overwritten": "Note: Destination website will be completely overwritten.", "details": "Details", "disable": "Disable", "disable_proxy_using_cloudflare": "Disable Proxy if you are using Cloudflare.", "disabled": "Disabled", "disallow_file_modifications": "Disallow File Modifications", "disconnect": "Disconnect", "disconnect_suffix_domain": "Disconnect Suffix Domain", "disconnected": "Disconnected", "disk": "Disk", "disk_space": "Disk Space", "disk_storage": "Disk Storage", "disk_usage": "Disk Usage", "dismiss": "<PERSON><PERSON><PERSON>", "display_text": "Display text", "do_you_to_provide_permission": "Do you wish to provide permission?", "do_you_want_to_continue": "Do you want to continue?", "do_you_want_to_continue_override_destination_site": "Do you want to continue? This will completely override destination site.", "do_you_wish_to_continue": "Do you wish to continue?", "do_you_wish_to_enable_it": "Do you wish to enable it?", "do_you_wish_to_proceed": "Do you wish to proceed?", "do_you_wish_to_start_migration": "Do you wish to start migration?", "doc": "Doc", "doc_git_deploy": "How to integrate your InstaWP sites with Git?", "doc_sftp_ssh": "How to access a site through SFTP & SSH?", "documentation": "Documentation", "does_not_expire": "Does not expire", "domain": "Domain", "domain_added_successfully": "Domain Added Succesfully", "domain_aliases": "Domain Aliases", "domain_already_exist": "Domain already exists.", "domain_is_verified": "Domain is verified", "domain_name": "Domain Name", "domain_name_length_error": "Please enter a domain name between 3 and 16 characters.", "domain_name_required": "Domain name is required.", "done": "Done.", "download": "Download", "download_badge": "Download Badge", "download_db": "Download Database", "download_file": "Download File", "download_instawp_connect_plugin": "Download InstaWP Connect Plugin", "downloading": "Downloading", "downloading_files": "Downloading Files", "downloading_files_finished": "Downloading Files Finished", "downtime": "Downtime", "drop_plugin_file": "Drop plugin file here.", "drop_the_file": "Drop the files here ...", "drop_theme_file": "Drop theme file here.", "dropdown": "Dropdown", "dummy_lorem_text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus\r\n    aliquam cum dictumst mi. Scelerisque pulvinar sit lorem ac felis. Fringilla consequat lobortis\r\n    libero ut rhoncus, pellentesque.", "duplicate": "Duplicate", "each": "Each", "earned_by_friends": "Earned by your friends", "earned_by_friends_tooltip": "This shows the total credits your friends have received. Spread the word and help your friends earn more too!", "earned_till_now": "Earned till now", "earned_till_now_tooltip": "Celebrate your progress! This is the total amount you’ve earned so far. Great job, keep it going!", "earnings_fetched_successfully": "Earnings fetched successfully", "earnings_so_far": "Earnings so far", "echo": "Echo", "enable_double_opt_in": "Enable Double Opt-In", "edge_cache": "Edge Caching", "edit": "Edit", "edit_in_admin_panel": "Edit in Admin Panel", "edit_site": "Edit Site", "edit_site_label": "Edit Label", "edit_template": "Edit Template", "elementor": "<PERSON><PERSON><PERSON>", "email": "Email", "email_address": "Email address", "email_and_password_are_wrong": "Email and password are wrong", "email_collection": "Email Collection", "email_from_name": "Email From Name", "email_required": "Email Required", "email_sender_name": "Sender Name", "email_spam_verification": "Email Spam Verification", "email_subject": "Email Subject", "email_templated_updated_successfully": "Email templated updated successfully.", "enable": "Enable", "enable_activity_logs": "Enable Activity Logs", "enable_activity_logs_description": "Activity Logs needs to be enabled inside the plugin to show up here.", "enable_alteast_one_service_to_view_details": "Enable at least one service to view details", "enable_auto_updates_to_modify_this": "Global Auto Updates is disabled", "enable_deployment": "Enable Deployment", "enable_migration": "Enable Migration", "enable_sftp": "Enable SFTP", "enable_sharing": "Enable Sharing", "enable_site_cloning": "Enable Site Cloning", "enable_ssh": "Enable SSH", "enable_two_factor": "You have enabled two factor authentication.", "enable_two_factor_new": "Once 2FA is enabled for your account, you will need a token or QR code to log in to :brand_name.  Use your phone's Google Authenticator application to fetch that QR/token.", "enable_uptime_monitoring": "Enable Uptime Monitoring", "enable_uptime_monitoring_description": "Enabling this option will monitor your website for uptime and SSL related issues.", "enable_white_label": "Enable White Label", "enabled": "Enabled", "end": "End", "enjoy": "<PERSON><PERSON>", "ensure_pass": "Ensure your account is using a long, random password to stay secure.", "enter_a_live_website_url": "Enter a live website URL", "enter_a_theme_or_a_plugin_url": "Enter a theme or a plugin URL", "enter_a_theme_or_a_plugin_url_2": "Enter a :type URL", "enter_affiliate_code": "Enter Affiliate code", "enter_alias": "Enter an alias", "enter_card_holder_name": "Enter card holder name", "enter_commands": "Enter Commands", "enter_destination_url": "Enter Destination URL", "enter_domain_name": "Enter Domain Name", "enter_email_sender_name": "Enter Sender Name", "enter_name": "Enter Name", "enter_price": "Enter Price", "enter_name_for_waas": "Please enter a name for the WaaS", "enter_old_domain": "Enter Old Domain", "enter_plugin_slugs": "Enter Plugin slugs", "enter_reply_to_email": "Enter Reply-To Email", "enter_reply_to_name": "Enter Reply-To Name", "enter_site_name": "Enter Site Name", "enter_site_url": "Enter Site URL", "enter_the_live_website_URL_which_has_blank_wordPress_pre-installed": "Enter the live website URL which has blank WordPress pre-installed", "enter_the_url_of_the_destination_website": "Enter the URL of the destination website", "enter_the_url_of_the_destination_website_blank_wordpress_site": "Enter the URL of the destination website (blank WordPress site)", "enter_the_url_of_the_source_website": "Enter the URL of the source website", "enter_the_url_of_the_website_to_transfer": "Enter the URL of the website to transfer", "enter_theme_slugs": "Enter Theme slugs", "enter_user_role": "Enter User Role", "enter_valid_domain": "Please pass a domain parameter.", "enter_your": "Enter your :name", "enter_your_access_token": "Enter your Access Token", "enter_your_api_key": "Enter Your API Key", "enter_your_domain_name": "Enter Your Domain Name", "enter_your_email": "Enter Your Email", "enter_your_email_address": "Enter your email address", "enter_your_full_name": "Enter Your Full Name", "enter_your_information": "Enter Your Information", "enter_your_name": "Enter Your Name", "enter_your_password": "Enter Your Password", "enter_your_secret_key": "Enter Your Secret Key", "enter_your_slug": "Enter Your Slug", "enter_your_username": "Enter Your Username", "enter_your_webhook_URL": "Enter your webhook URL", "entering_your_email": "by entering your email id", "error": "Error", "error_card_verify": "Unable to process the card, Please try using a different card", "error_card_verify_message": "We're sorry, but we couldn't verify your card. Please try using a different card or contact our support team for assistance.", "error_creating_new_user": "WordPress user can't be created.", "error_creating_temp_login_link": "Temporary login link can't be created.", "error_description_403": "Sorry, you are forbidden from accessing this page.", "error_description_404": "Sorry, the page you are looking for could not be found.", "error_description_419": "Sorry, your session has expired. Please refresh and try again.", "error_description_500": "Whoops, something went wrong on our servers.", "error_description_503": "Sorry, we are doing some maintenance. Please check back soon.", "error_fetching_auto_update_data": "Error fetching auto update data", "error_fetching_configuration": "Error fetching configuration", "error_fetching_configuration_please_update_plugin_to_latest_version": "Error fetching configuration please update plugin to latest version", "error_fetching_plugin_list": "Error fetching plugin list", "error_fetching_plugin_status": "We cannot determine if plugin is already installed. Possible reason: Permalink is not set, try saving the permalinks to \"Post Name\" from Settings > Permalinks.", "error_fetching_uptime_settings": "Error fetching uptime settings", "error_fetching_wp_config_data": "Error fetching WP Config data.", "error_fetching_wp_users": "Error fetching WordPress users", "error_found": "Error Found", "error_from_plugin": "From Plugin", "error_in_data": "Error in given data", "error_in_map_domain": "Error in Map Domain.", "error_in_plugin_config": "Error in WordPress plugin configuration", "error_in_plugin_config_wpcs": "Instawp Connect Plugin is not installed on Template. Please Install Plugin in Template and sync ", "error_in_plugin_install": "Error in WordPress plugin installation", "error_in_sending_mail": "Error in sending mail", "error_in_site_configured": "Error in site configured", "error_in_site_restore": "Error in site restore", "error_updating_activity_logs": "Error updating activity logs", "error_updating_auto_update_data": "Error updating auto update data", "error_updating_wp_config_data": "Error updating WP Config data.", "error_uploading_file": "Error uploading file", "errors_found": "Errors Found", "every": "Every", "example_com": "example.com", "executing_commands_on": "Executing commands on :", "expiration_date": "Expiration date", "expired": "Expired", "expired_at": "Expired At", "expires": "Expires", "expires_in": "Expires in", "expiry": "Expiry", "expiry_days": "Expiry Time", "expiry_time_in_days": "Expiry Time (in days)", "explore_marketplace": "Explore marketplace", "explore_more": "Explore More", "export": "Export", "export_as": "Export As", "export_as_csv": "Export As CSV", "export_csv": "Export CSV", "extend_site_lifetime": "Extend Site Lifetime", "extend_site_lifetime_notice": "Guest user can extend the site lifetime by clicking on the link via webhook.", "extending_site_lifetime": "We are extending the site lifetime.", "failed": "Failed", "failed_migrations": "Failed Migrations", "failed_to_connecting_your_site": "Failed to connecting your site", "faker": "<PERSON><PERSON>", "faq_title": "Frequently asked questions", "fastest_create_site": "fastest way to create a WordPress site.", "fav_plugins": "<PERSON><PERSON>", "feature": "Feature", "feature_availability_list_fetched_successfully": "Feature availability list fetched successfully", "feature_by": "Feature by", "feature_names.advance_config": "Advanced Configurations", "feature_names.advance_deployments": "Advanced Deployments", "feature_names.advance_template_options": "Advanced Options", "feature_names.advanced_connected_sites": "Advanced Connected Sites", "feature_names.api_quota_limit": "API Quota Limit", "feature_names.api_rate_limit": "API Rate Limit", "feature_names.basic_connected_sites": "Basic Connected Sites", "feature_names.build_waas": "Build WaaS", "feature_names.cloning": "Clone Sites", "feature_names.custom_domain": "Custom Domain", "feature_names.dedicated_server": "Dedicated server", "feature_names.disk_storage": "Disk Storage (in MB)", "feature_names.expiry_days": "Expiry Time (in days)", "feature_names.ftp_access": "FTP Access", "feature_names.git_operations": "Git Operations", "feature_names.instant_template": "Instant Template", "feature_names.live_credits": "Live Credits (in USD)", "feature_names.migrations": "Migrations", "feature_names.reserve_sites": "<PERSON> as Permanent", "feature_names.restore_sites": "Restore Sites", "feature_names.run_commands": "Run Commands", "feature_names.sell_templates": "<PERSON><PERSON>", "feature_names.server_loc": "Server Location", "feature_names.site_versions": "Site Versions", "feature_names.sites": "Sites", "feature_names.ssh_access": "SSH Access", "feature_names.sync_quota_limit": "Sync Events Quota", "feature_names.team_member": "Team Members", "feature_names.teams": "Teams", "feature_names.templates": "Templates", "feature_names.use_external_hosting": "Use External Hosting", "feature_names.vulnerability_scanner": "Vulnerability Scanner", "feature_names.white_label": "White Label/Suffix Domain", "features": "Features", "features_active_sites_help_text": "Build instant WordPress websites for staging, dev, testing or client projects.", "features_advance_configurations_help_text": "PHP Config values, zip URL for plugin/theme", "features_advance_deployments_help_text": "Post Deployment Commands for Git Integration", "features_advance_opt_help_text": "Customize look and feel of Shared Templates", "features_advanced_connected_sites_help_text": "Connect unlimited live websites by installing the Connect plugin, available in the Free plan as well.", "features_api_quota_limit_help_text": "Number of Core Operation APIs can be called in a month", "features_api_rate_limit_help_text": "Number of APIs can be requested in a minute", "features_basic_connected_sites_help_text": "Connect unlimited live websites by installing the Connect plugin, available in the Free plan as well.", "features_build_waas_help_text": "Sell pre-made websites combined with hosting of your choice and build a recurring revenue stream.", "features_dedicated_server_help_text": "[Enterprise] Dedicated server just for your company", "features_disk_storage_help_text": "Total disk storage allowed for the whole account, for staging sites.", "features_expiry_time_in_days_help_text": "Lifetime of a Temporary site", "features_ftp_access_help_text": "Access the website's files & folders using a SFTP client such as Filezilla.", "features_git_operations_help_text": "Number of times Git Deployments can run / mo", "features_instant_template_help_text": "Sites created from templates take about 10-30 secs, this options removes the wait time.", "features_live_credits_help_text": "Automatically reduce bills of your hosted sites (with InstaWP Live) every single month.", "features_map_domain_help_text": "Point your domain to a InstaWP site", "features_migrations_help_text": "Migrations from and to InstaWP staging, per month.", "features_reserve_sites_help_text": "Reserved sites never expire or get deleted.", "features_restore_sites_help_text": "Number of times an expired site can be restored", "features_run_commands_help_text": "Run CLI commands on staging sites without logging into Terminal.", "features_sell_templates_help_text": "You can sell templates by simply sharing a link (like gumroad) or by uploading to our Store.", "features_server_loc_help_text": "Build sites in non-default (USA) country for that extra speed.", "features_site_versions_help_text": "Store backups or restore points of your staging sites and restore in 1-click.", "features_ssh_access_help_text": "Use SSH to run commands on the staging server remotely. WP CLI commands supported.", "features_sync_quota_limit_help_text": "Number of Syncs can be called in a month", "features_team_member_help_text": "Invite team members to work with you in the team.", "features_teams_help_text": "Create multiple teams or workspaces to categorize your work.", "features_templates_help_text": "Create blueprints by storing a staging site as \"Template\", spin new sites from templates on demand.", "features_use_external_hosting_help_text": "Link external hosting from our support providers to use in Go Live or WaaS features.", "features_vulnerability_scanner_help_text": "Scan for known vulnerabilities in Plugins, Themes & Core. Works for Staging or Connected sites.", "features_white_label_suffix_domain_help_text": "[Enterprise] Setup a wildcard domain for all sandbox sites to use *.yourcompany.com suffix", "feedback_survey_not_found": "Feedback survey not found", "fetch_account_list": "Fetch Account List", "fetch_contact_list": "Fetch Contact List", "fetching_site_performance_in_progresss": "Fetching site performance in progresss", "fields_are_encrypted": "Your :fields stored as encrypted strings in our database", "fifth": "Fifth", "file_backup": "File Backup", "file_manager": "File Manager", "file_manager_failed": "File Manager Failed.", "file_of_zip_type": "The file must be a file of zip type.", "file_permission_update_success": "File permission updated successfully", "file_size_should_be_less_than": "File size should be less than :size MB.", "svg_not_allowed": "SVG image is not allowed.", "file_system_permission": "File System Permission", "files": "Files", "files_pulling_is_completed": "Files pulling is completed", "files_pulling_is_running": "Files pulling is running", "files_pushing_is_completed": "Files pushing is completed", "files_pushing_is_running": "Files pushing is running", "finalizing_site": "Finalizing Site", "finish": "Finish", "finishing_up_setup": "Finishing up setup...", "first": "First", "first_connect_your_website_with_our_site_import_tool": "First connect your website with our site import tool", "first_name": "First name", "fixed_in": "Fixed in", "follow_prompts_on_both_the_websites": "Follow prompts on both the websites.", "for_more_details": "for more details", "for_team": "for team", "forever": "forever", "forge": "Forge", "passwords_does_not_match": "Passwords does not match.", "forgot_password": "Forgot Password", "forgot_your_password": "Forgot your password? No problem. Just let us know your email address and we will email you a password\r\n    reset link that will allow you to choose a new one.", "fourth": "Fourth", "free": "Free", "free_user_monthly_limit": "Free plan users can create :limit sites in a month, upgrade to any paid plans to remove this limit.", "frequency": "Frequency", "friday": "Friday", "friends_unutilized_credits": "Your Friends Unutilized Credits", "friends_unutilized_credits_tooltip": "See how many credits your friends have used to buy add-ons or paid plans. Your referrals are making the most of their rewards!", "from_now": "from now", "from_scratch": "From Scratch", "from_template": "From Template", "from_snapshot": "From Snapshot", "from_the_blog": "FROM THE BLOG", "ftp_access": "FTP Access", "ftp_ssh": "FTP/SSH Details", "ftp_ssh_access": "FTP/SSH Access", "full_name": "Full Name", "gallery": "Gallery", "gallery_cover_updated_successfully": "Gallery cover updated successfully", "gallery_created_successfully": "Gallery created successfully", "gallery_deleted_successfully": "Gallery deleted successfully", "gb": "GB", "general": "General", "general_created_successfully": "General Created Successfully", "general_features": "General Features", "generate": "Generate", "generate_link": "Generate link", "generate_new": "Generate New", "generate_report": "Generate Report", "generating_report_we_will_send_you_an_email": "Generating report.<br />We will send you an email with the report shortly.", "get_more_with_plan": "Get more with :planName Plan", "get_started": "Get started by creating a new site.", "get_temp_login_url": "Copy Temp Login URL", "git_deploy_des": "Deploy repository code automatically to the website.", "git_deployment": "Git Deployment", "git_operations": "Git Operations", "git_repo_details_added": "Git repository details added.", "git_repo_details_deleted": "Git repository details deleted.", "git_repo_details_updated": "Git repository details updated.", "git_repository": "Git Repository", "global_auto_updates": "Global Auto Updates", "global_switch_automated_updates_off": "Global Switch for Automated Updates is OFF. Do you wish to first switch on automated updates globally? It will also switch it on for Core Updates.", "global_updates_disabled": "Global Updates disabled", "global_updates_enabled": "Global Updates enabled", "go_back": "No, Go Back", "go_back_txt": "Go Back", "go_live": "Go Live", "go_live_initiated": "Go Live Initiated", "go_to_site": "Go to site", "going_live": "Going Live", "going_live_now": "Going live now...", "gonna_come_back": "I knew you are gonna come back. Lets do not waste time and resume your subscription.", "graph": "Graph", "graphs": "Graphs", "guest_site_send_email_txt": "We have sent you an email with Site Login details", "have_domain": "I have a domain", "hide": "<PERSON>de", "hide_calculator": "Hide Calculator", "hide_privacy_checkbox": "Hide Privacy Checkbox", "holder_name": "Holder Name", "home": "Home", "horizon": "Horizon", "host": "Host", "host_name": "Host Name", "host_not_alive": ":url Host not alive", "host_with_us": "Host with Us", "hosted_sites": "Hosted Sites", "hosting": "Hosting", "hosting_account": "Hosting Account", "hosting_connection": "Hosting Connection", "hosting_connection_deleted": "Hosting Connection Deleted Successfully.", "hosting_connection_updated": "Hosting Connection Updated Successfully.", "hosting_features": "Hosting Features", "hosting_manager": "Hosting Manager", "hosting_plan_server_selection": "Select server on :hostingPlan", "hosting_provider": "Hosting Provider", "hosting_provider_alias": "<PERSON><PERSON>", "hosting_provider_fetch_successfully": "Hosting Provider <PERSON><PERSON> Successfully", "hosting_provider_meta_fetch_successfully": "Hosting Provider <PERSON><PERSON> Successfully", "hosting_sales": "Hosting Sales", "hosting_site": "Hosting Site", "hosting_with_instawp": "Host with :brand_name", "hour": "hour", "hour_ago": "hours ago", "hours": "{1} :count hour|[2,*] :count hours", "hrs": "hrs", "hurray": "<PERSON><PERSON><PERSON>", "i_have_taken_backup_of_my_destination_website_i_understand_that_this_will_overwrite_the_destination_website_or_the_process_may_fail_b_w_due_to_variety_of_reasons": "I have taken backup of my destination website, I understand that this will overwrite the destination website or the process may fail b/w due to variety of reasons.", "i_will_wait": "I will wait", "id": "Id", "if_you_stop_your_site_transfer_you_will_have_to_start_the_process_again_from_the_beginning": "If you stop your site transfer, you will have to start the process again from the beginning.", "import": "Import", "import_site": "Import Site", "imported": "Imported", "impression_created_successfully": "Impression created Successfully.", "impressions": "Impressions", "in_progress": "In Progress...", "inactive": "Inactive", "inbox_list_fetched_successfully": "Inbox List Fetched Successfully", "inboxes": "Inboxes", "incident": "Incident", "included": "Included", "included_in_base_plan": "Included in base plan", "inclusions": "Inclusions", "india": "India", "individual": "Individual", "info": "Info", "information": "Information", "information_from_personal": "This information is based on your current plan subscription.", "initiated": "Initiated", "insta_live_will_take_backup_automatically": "Insta Live will take backup automatically.", "insta_site": "Insta Site", "insta_wp": "InstaWP", "install": "Install", "install__zip": "Install .zip", "install_content_des": "Easy 1 click Installation of plugins & themes to the website.", "install_plugin": "Install Plugin", "install_plugin_themes": "Install Plugin/Themes", "install_plugins": "Install Plugins", "install_the_plugin_on_your_live_site_and_connect_it_to_instawp_account": "Install the plugin on your live site and connect it to InstaWP account.", "install_themes": "Install Themes", "installation_begin": "Your installation is about to begin…", "installation_failed": "Installation Failed", "installation_in_progress": "Installation in progress...", "installed": "Installed", "installing": "Installing", "installing_instaWP_connect_plugin": "We are installing InstaWP Connect plugin to communicate with your website", "installing_plugin": "Installing Plugin", "installing_plugins": "Installing plugin", "installing_ssl": "Installing SSL", "installing_wordpress": "Installing WordPress", "installs": "Installs", "instant": "Instant", "instant_template": "Instant Template", "instant_snapshot": "Instant Snapshot", "instant_template_not_allow": "You are not allowed to use Instant Template please upgrade your plan.", "instawp": "InstaWP", "instawp_connect": "instawp-connect", "instawp_live": ":brand_name Live", "instawp_serve": "Instawp Server", "integration_deleted_successfully": "Integration Deleted Successfully", "integration_fetched_successfully": "Integration fetched successfully", "integration_status_updated_successfully": "Integration Status Updated Successfully", "integration_steps": "integration steps", "integration_type_list_fetch_successfully": "Integration Type List Fetch Successfully", "integration_uninstalled_successfully": "Integration uninstalled successfully", "integration_updated_successfully": "Integration Updated Successfully", "integrations": "Integrations", "intelligent_way_to_manage_websites_automate_everything": "Intelligent way to manage websites. Automate everything.", "invalid_ai_provider": "The selected AI provider is invalid.", "invalid_color_code": "Invalid Color Code.", "invalid_command_variable": "Invalid :attribute variable", "invalid_coupon_code": "Invalid Coupon Code", "invalid_coupon_plan": "Invalid Coupon for a selected plan.", "invalid_credentials": "You have invalid credentials for :provider", "invalid_file": "Invalid file", "invalid_matching_brackets": "The :attribute has invalid matching brackets.", "invalid_offer": "Offer is invalid.", "invalid_otp": "Invalid OTP.", "invalid_recaptcha": "Invalid <PERSON>a.", "invalid_signup_link": "Invalid Signup link.", "invalid_ssh_key": "Invalid SSH key.", "inventory_installation_completed": "Plugins and themes installation completed.", "inventory_installation_started": "Plugins and themes installation started.", "invoice": "Invoices", "invoices_fetched_successfully": "Invoices fetched successfully.", "ip": "IP", "ip_address": "IP Address", "ip_not_allowd": "Your IP address is not allowd to perform this action.", "is_now_connected_to": "is now connected to ", "is_public_waas": "Allow Public Access to the WaaS", "is_trying_to_connect_to_your_instawp_account": "is trying to connect to your InstaWP account.", "issue_with_heartbeat": "There is an issue with the heartbeat of your site. Please run scan for updates.", "issue_with_uptime_monitoring_please_try_again_or_reload_the_page": "Issue with Uptime Monitoring. Please try again or reload the page.", "it_will_take_about_seconds": "It will take about :seconds seconds", "items": "Items", "jetpack": "Jetpack", "keep_Instant_site_creation": "Keep the site name as blank for Instant site creation.", "keep_as_it_is": "Keep as it is (no change)", "keep_blank_for_instant_random_site": "Keep blank for instant random site", "kyc_pending": "Update KYC", "kyc_re_enter": "Re-enter KYC", "label": "Label", "language": "Language", "laravel": "<PERSON><PERSON>", "last": "Last", "last_active": "Last active", "last_activity": "Last Activity", "last_month": "Last Month", "last_name": "Last name", "last_push": "last push", "last_scan_at": "Last Scan", "last_time": "Last time, your payment did not go through. Please try a different payment method to subscribe to current plan.", "last_updated": "Last Updated", "last_updated_on": "Last updated on", "last_used": "Last used", "last_week": "Last Week", "last_year": "Last Year", "launch_demo": "Launch Demo", "launch_instant": "You are going to launch an instant", "launch_site": "Launch Site", "launch_wp_admin_panel": "Launch WP Admin Panel", "launch_wp_new": "Launch new WordPress", "launch_your_site": "Launch your site", "lbl_in_progress": "In Progress", "lbl_sites": "sites", "learn_more": "Learn more", "leave": "Leave", "leave_empty_we_will_fetch_it_for_you": "Leave empty we will fetch it for you", "leave_team": "Leave Team", "library_commands_fetched_successfully": "Library commands fetched successfully", "license_key": "License Key", "licensed_plugins": "Licensed Plugins", "limit": "limit", "limit_reached": "Limit Reached", "line_1_placeholder": "Enter your primary address", "line_2_placeholder": "Enter second address line", "link": "Link", "link_staging_site": "Link Staging Site", "link_to_hosting": "Link to Hosting", "list_id": "List Id", "list_of_all_mapped_domains": "List of all mapped domains to your website.", "list_of_past_invoices_paid_by_you": "List of past invoices paid by you", "list_of_plugins_in_your_connected_site": "List of plugins in your connected site", "list_of_recent_activity": "List of recent activity.", "list_of_resources_where_integration_will_be_deleted": "List of :resources where integration will be deleted.", "list_of_resources_where_integration_will_be_status": "List of :resources where integration will be :status", "list_of_sites_created_from_this_template": "List of sites created from this template", "list_of_sites_created_from_this_snapshot": "List of sites created from this snapshot", "list_of_sites_with_installed_integration_fetched_successfully": "List of sites with installed integration fetched successfully", "list_of_staging_sites_created_for_this_connected_site": "List of Staging sites created for this connected site", "list_of_subscriptions_for_waas": "Your InstaWP Live subscription for WaaS", "list_of_themes_in_your_connected_site": "List of themes in your connected site", "list_of_users_in_your_connected_site": "List of Users in your connected site", "list_of_your_ssh_public_keys": "List of your SSH public keys", "list_sites": "Sites", "live_credits": "Live Credits (in USD)", "live_site_url": "Live site URL", "loading": "Loading...", "local_wp": "LocalWP", "wordpress_studio": "Wordpress Studio", "locations": "Locations", "lock_down": "Lock down", "log_created_successfully": "Log created successfully", "log_deleted_successfully": "{1} :count Log deleted successfully|[2,*] :count Logs deleted successfully", "log_in": "Log in", "log_out": "Log Out", "log_type": "Log Type", "login": "<PERSON><PERSON>", "login_billing_portal": "Login To Billing Portal", "login_now": "Login Now", "login_reg_wlcome_txt": "Welcome to the world of lightning fast WordPress sites.", "login_to_website": "Login to Website", "logo": "Logo", "magic_login": "Magic Login", "mailchimp_list_id": "Mailchimp List Id", "mailtrap_integration_removed": "Mailtrap integration removed", "mailtrap_successfully_integrated": "Mailtrap successfully integrated", "main": "main", "make_sure_your_credit_cards_have_enough_funds_for_this_transaction": "Make sure your credit cards have enough funds for this transaction.", "manage": "Manage", "manage_account": "Manage Account", "manage_api": "Manage API Tokens", "manage_keys": "Manage Keys", "manage_logout": "Manage and log out your active sessions on other browsers and devices.", "manage_role": "Manage Role", "manage_site": "Manage Site", "manage_wordpress_hosting_with_cdn": "Managed WordPress Hosting with CDN, Security (WAF) and Backups in-built.", "manage_wp_config_settings_disabled": "Managing Config is disabled in your plugin settings. Enable it to manage it from here.", "manage_wp_config_settings_for_connected_site": "Manage wp-config settings for the connected site.", "manager_role_descp": "Can view team sites, can only edit & delete own sites.", "manually_manage_your_websites_forever_free": "Manually manage your websites. Forever free.", "map_as_well": "Map 'www' as well", "map_domain": "Map Domain", "map_domain_feature_current_plan": "Map Domain feature is not available in your current plan. Please upgrade your account to use this feature.", "map_domain_feature_team_plan": "Map Domain feature is not available in your team's plan. please contact team owner.", "map_domain_name": "Map Domain Name", "map_domain_successfully": ":url map domain Successfully", "map_domain_take_time": ":url map domain shall take 5 mins.", "mapped_domain": "Mapped Domain", "mark_all_as_read": "Mark all as read", "mark_as_public": "Mark as public", "mark_automated_updates_on": "<PERSON> Automated Updates ON", "max_discount_amount": "Maximum Discount Amount", "max_execution_time": "max_execution _time", "max_frequency": "<PERSON>", "max_frequency_in_plan": "max :frequency in your plan", "max_input_time": "max_input _time", "max_input_vars": "max_input _vars", "max_instant_pool_count": "Maximum Template Pool Sites", "max_locations": "Max Locations", "max_locations_in_plan": "max :count locations in your plan", "maximum_execution_time_limit": "Maximum execution time limit for commands is 300 seconds (5 mins).", "maximum_file_are_allowed": "Maximum :fileNumber images can be uploaded", "maximum_menu_selected": "Maximum :maximumMenu menu selected", "mb": "MB", "membership_group": "Membership Group", "menu_connects_description": "Remote management of websites.", "menu_created": "Menu :count created :timestamp", "menu_deleted": "Menu :count deleted :timestamp", "menu_staging_description": "Dev sites or Client projects.", "menu_template_description": "Blueprint of staging sites.", "menu_updated": "Menu :count updated :timestamp", "menus_fetched_successfully": "<PERSON><PERSON> Fetched Successfully", "menus_updated_successfully": "<PERSON>u Updated Successfully", "message": "Message", "mexico": "Mexico", "migrate": "Migrate", "migrate_logs": "Migrate Logs", "migrate_site_success": "Site migrated successfully.", "migrate_status_not_matched": "Migrate status not match with our database", "migrate_success": "Site migrate successful", "migrate_v3_php_version_warning": "PHP versions on source (:source_version) and destination (:destination_version) websites do not match, migration may fail.", "migrate_via_url": "Migrate via URL (works with any hosting panel)", "migrate_website": "Migrate Website", "migrate_websites": "Migrate Websites", "migrate_your_files_content_and_data": "Migrate your files, content and data...", "migrating_your_files_content_and_data": "Migrating your files, content, and data...", "migration_aborted": "Migration Aborted.", "migration_already": "Migration is in progress already.", "migration_complete": "Go Live is now complete", "migration_completed": "Migration completed", "migration_completed_your_data_has_been_successfully_transferred_to_its_new_destination": "Migration Completed. Your data has been Successfully transferred to its new destination.", "migration_created_successfully": "Migration created successfully", "migration_dashboard": "Migration Dashboard", "migration_failed": "Migration failed.", "migration_fetched_successfully": "Migration fetched successfully", "migration_finish_successfully": "Migration finish successfully", "migration_initiated": "Migration Initiated", "migration_is_completed": "Migration is completed", "migration_not_found": "Migration not found", "migration_note": "You can now close this browser window and track the migration via the following link", "migration_started": "Migration started", "migration_status_fetched_successfully": "Migration status fetched successfully", "migration_status_updated_successfully": "Migration status updated successfully", "migration_timeout": "Migration timeout.", "migrations": "Migrations", "min": "min", "min_discount_amount": "Minimum Discount Amount", "minutes": "{1} :count minute|[2,*] :count minutes", "missing_name": "Missing Name", "mo": "mo", "mobile": "Mobile", "mobile_not_found": "Mobile number not found.", "mobile_scan_failed": "Mobile Scan Failed", "mobile_verified": "Mobile number verified.", "monday": "Monday", "monetize": "Monetize", "pricing": "Pricing", "monetize_features": "Monetize Features", "monetize_payouts_not_allow": "You are not allow to monetize because user payout is not available.", "monetized": "Monetized", "monitor_off": "Monitor OFF", "monitor_on": "Monitor ON", "month": "Month", "monthly": "Monthly", "months": "Months", "more": "more", "more_act": "More actions", "more_options": "More Options", "more_sites": "more sites", "most_popular": "Most Popular", "move_tenants_here": "Move tenants here", "move_to_dashboard": "Move to Staging Tab", "multi_installation": "Multi Site Installation", "multi_site_update_success": "MultiSite updated successfully.", "multisite_operation_non_reversible": "This operation is non-reversible, you cannot setup sub-folder or sub-domain multisite after conversion", "my_account": "My Account", "my_hosting": "My Hosting", "my_purchases": "My Purchases", "my_settings": "My Settings", "my_team": "My Team", "my_teams": "My Teams", "my_template_purchases": "My Template Purchases", "name": "Name", "name_available": "Name available.", "name_of_the_product": "Name of the Product", "necessary": "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.", "need_support": "Need support", "new_alert": "<PERSON> Alert", "new_credit_debit_card": "New Credit/Debit Card", "new_password": "New Password", "new_plan": "New Plan", "new_pricing": "New Pricing", "new_scan": "New Scan", "new_site": "New Site", "new_subscription": "New Subscription", "new_version_notice": "We have detected a new release for InstaWP. Refresh this page to view changes.", "next": "Next", "next_bill_amount": "Next Bill Amount", "next_payout_date": "Next payout date", "next_step": "Next Step", "no": "No", "no_active_hosting_plan": "No active hosting plans were found.", "no_active_integration_found": "No active integration found.", "no_active_sites": "No Active Sites", "no_activity_logs": "No Activity Logs", "no_activity_logs_description": "There are currently no activity logs to display.", "no_activity_logs_found": "No activity logs found", "no_alert_rules": "No Alert Rules", "no_alert_rules_description": "There are currently no alert rules configured. Create a new alert rule to get started.", "no_app": "No App Integration", "no_backup_found": "No Backup Found", "no_changes_made": "No changes made", "no_config_params_in_config_file": "We couldn't find any config parameters in your config file.", "no_connected_site_list_found": "Get started by installing our <a target=\"_blank\" href=\"https://wordpress.org/plugins/instawp-connect\" style=\"color:#005e54;\"><b>WordPress Plugin</b></a> on your existing sites", "no_continue_my_transfer": "No, continue my transfer", "no_credit_history_found": "No credit history found.", "no_data_found": "No data found.", "no_email_specified": "No email specified", "no_expiry": "No Expiry", "no_hosting_connection_list_found": "Get started to connecting provider", "no_incidents": "No incidents", "no_incidents_found": "No incidents found", "no_integrations_are_active": "No integrations are active.", "no_mb_disk_storage": ":number MB Disk Storage", "no_of_coupons": "Number of Coupons", "no_of_customers": "Number of Customers", "no_payouts_found": "No Payout found!", "no_performance_data_found": "No performance data found", "no_plugins": "No Plugins", "no_plugins_found": "No plugins found.", "no_record_found": "No Records Found.", "no_referral_history_found": "No referral history found.", "no_reports": "No Reports", "no_reports_description": "There are currently no reports to display.", "no_result_found": "No Result Found.", "no_scan_yet": "No Scan Yet", "no_site": "No sites found!", "no_sites": "No Sites", "no_sites_selected": "No sites are selected to delete", "no_ssh_key_found": "No SSH Key found.", "no_stack_found": "No Stack Found!", "no_stack_found_sub": "Selected category doesn't have any items.", "no_staging_sites_found": "No staging sites found", "no_template_found": "Sorry no templates found. Please create shared templates with monetization activated for the templates", "no_themes": "No Themes", "no_themes_found": "No themes found.", "no_username": "No user name", "no_users": "No Users", "no_users_found": "No Users Found", "no_versions_has_tenant": "No other versions has tenants", "no_vulnerabilities_found": "No vulnerabilities found", "no_vulnerability_report_subtitle": "Right now not found any vulnerability scans in this site, so click on scan and view report", "no_vulnerability_report_title": "No Vulnerability Report Found!", "no_vulnerability_scans_found": "No vulnerability scans found", "none": "None", "not_a_template_site": "Not a template site, you can not extend site lifetime of a regular staging site.", "not_able_fetch_system_users": "Not able to fetch system users", "not_able_to_create_site": "Sorry, we are unable to create new sites at the moment, please retry after some time.", "not_allow_to_extend_lifetime": "Not allowed to extend lifetime of this site.", "not_allowed": "Not Allowed", "not_available_in_your_plan": "Not available in your plan. Contact admin.", "not_connected": "Not Connected", "not_enable": "You have not enabled two factor authentication.", "not_found_any_icon": "Not found any Icon.", "not_in_plan": "Not in Plan", "not_monetized": "Not Monetized", "not_scanned": "Not Scanned", "not_verified": "Not Verified", "note": "Note", "notification": "Notifications", "notification_sent_successfully": "Notification sent successfully", "notifications": "Notifications", "nouns": "Nouns", "now_enable_auth": "Two factor authentication is now enabled. Scan the following QR code using your phones authenticator application.", "num_active_sites": ":number Active Sites", "num_connected_site": "{1} :count connected site|[2,*] :count connected sites", "number_of_emails_should_be_less_than_14": "Number of emails should be less than 14", "number_of_requests": "Number of Requests", "number_of_requests_per_second": "Number of requests per second.", "number_of_sites": "Number of Sites", "number_sites_selected": "{1} :count site selected|[2,*] :count sites selected", "number_templates_selected": "{1} :count template selected|[2,*] :count templates selected", "number_snapshots_selected": "{1} :count snapshot selected|[2,*] :count snapshots selected", "off": "Off", "offer_applied_successfully": "Offer applied successfully.", "offers_available": "offers available", "offers_fetched_successfully": "Offers fetched successfully.", "ok": "Ok", "old_domain": "Old Domain", "on": "On", "on_delete_child_mark_as_parent": " This hosting site has a child staging site. The child site will be marked as the parent site.", "on_demand": "On-demand", "on_going_migration": "On Going Migration", "on_the": "On the", "onboard_line_1": "Create 3 WordPress sites", "onboard_line_2": "Temporary sites for 7 days", "onboard_line_3": "Access to dashboard", "onboard_line_4": "Install Chrome extension", "onboard_line_5": "Export site to .zip", "onboard_line_6": "Migrate site anywhere", "onboard_site_detail_line": "Free users get temporary sites. Upgrade to make sites permanent.", "once": "once", "once_in_frequency": "Once in :frequency", "once_promoted_and_continue": "once promoted and continue.", "one_click_login_to_your_site": "One click login to your site", "ongoing": "Ongoing", "only_alpha_numeric_allowed": "Only alpha and numeric value allowed for :field", "only_available_as_add_on": "This feature is only available as :feature add on", "only_external_checkout_allowed": "Only external checkout allowed", "open_deployment": "Open Deployments", "open_website": "Open Website", "order_created_successfully": "Order created successfully", "order_status": "Order status", "orders": "Orders", "orders_fetched_successfully": "Orders fetched successfully", "other": "Other", "other_browser": "Log Out Other Browser Sessions", "other_city_placeholder": "Enter your city", "other_limit_content": "We have a limit of 500 mb on storage for the free account and higher limits for paid account. These limits will be announced very soon. Also, we may put a limit of number of visitors based on the usage of the platform.", "other_limit_title": "Are there any other Limits?", "other_settings": "Other Settings", "others": "Others", "otp_attempts": "Too many attempts. Try again after an hour.", "otp_not_sent": "OTP Can't sent.", "otp_sent": "OTP Sent.", "otp_text": "Your InstaWP verification code is :otp.", "otp_try_after": "Please try after ", "our_free_plan_allow": "Our free plan allows you to create a maximum of :maximumSiteCount sites. You can delete your expired sites if any in order to create new sites.", "our_pricing_reduces_as_you_purchase_more_sites": "Our pricing reduces as you purchase more sites. Currently you have created :site_count Live sites.", "our_scanner_checks_for_potential_issues_affecting_load_times_plugin_performance_and_overall_site_speed": "Our scanner checks for potential issues affecting load times, plugin performance, and overall site speed.", "our_team_of_experts_is_available": "Our team of experts is available 24/7.", "out_of_num_sites": "out of :num sites", "outstanding_balance": "Outstanding balance", "outstanding_balance_tooltip": "This is the amount you’ll earn once your friend makes their first purchase (excluding free trials and using referral credits). Keep sharing to boost your balance!", "overwrite_existing": "Overwrite Existing", "owners_email": "Owner's Em<PERSON>", "pack": "Pack", "page_builder": "Page Builder", "page_insight": "Page Insight", "page_speed_insight_performance_check": "Page Speed Insight Performance Check", "page_speed_insights_limit_reached_add_your_own_key_to_keep_testing_through": "Page Speed Insights Limit reached! Add your own key to keep testing through", "pages": "Pages", "paid": "Paid", "panel_domain": "Panel Domain", "parent_child_link_successfully": "Parent Child Url successfully connected", "parent_site_created_successfully": "Parent site created successfully.", "parent_url_already_linked": "Parent Url already linked", "parent_url_must_be_http": "Parent Url must be starts with http:// or https://", "parent_url_not_found": "Parent Url connect not found or not connected to your account", "partner": "Partner", "partner_portal": "Partner Portal", "password": "Password", "password_added_successfully": "password added successfully.", "password_confirm": "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.", "password_protect": "Protect Site", "password_removed_successfully": "password removed successfully.", "password_updated_successfully": "Password Updated Successfully.", "paste_the": "Paste the", "paste_your_public_ssh_key": "Paste your public SSH key", "payment": "Payment", "payment_card": "Payment Card", "payment_error": "Payment Error", "payment_failure": "Payment Failure", "payment_failure_message": "Your account will be suspended due to non-payment for your subscription.", "payment_history": "Payment History", "payment_method": "Payment Method", "payment_pending_for": "Payment pending for", "payment_processing": "Payment Processing...", "payment_success": "Payment Success", "payout_account_link": "Setup your creator account <a href=\":link_href\" target=\"_blank\">:link_text</a>", "payout_dashboard": "Login to Dashboard", "payout_is_already_configured": "Payout method is already configured.", "payout_method": "Update Payout method", "payout_setup_pending": "Payout setup pending", "payout_updated_successfully": "Payout updated successfully", "payouts": "Payouts", "payout": "Payout", "payout_desc": "Decide whether to monetize your template and set a suitable price", "payouts_fetched_successfully": "Payouts fetched successfully", "pending": "Pending", "pending_invoice": "Pending Invoice", "pending_team_invitations": "Pending Team Invitations", "per": "per", "per_page": "Per Page", "perform_your_first_scan": "Perform your first scan", "performance": "Performance", "performance_scan_data_fetched_successfully": "Performance scan data fetched successfully.", "performance_scan_limit_reached": "Your daily limit for performance scan has reached. Please wait for :hours hours to perform another scan.", "performance_scan_started": "Performance scan started.", "performance_scanner": "Performance Scanner", "performance_scanning": "Performance Scanning", "automated_performance_scanning": "Automated Performance Scanning", "performing_setup": "Performing Setup", "permanently_delete_your_account": "Permanently delete your account.", "permission_dialog": "Permission Dialog", "permissions": "Permissions", "php": "PHP", "php_config": "PHP Config", "php_config_des": "Configuration of your website php settings & limits.", "php_config_updated": "PHP configurations Updated.", "php_file_permission_update_success": "Php file permission updated successfully.", "php_script_installing": "PHP Script Installing", "php_version": "PHP Version", "php_version_differ_from_template": "PHP version (:current_php_version) is different from your Staging Site PHP Version (:template_php_version). Are you sure you want to proceed?", "php_version_update_success": "Php Version updated successfully", "plan": "Plan", "plan_addons": "Plan Addons", "plan_billing": "Plan & Billing", "plan_customize": "Plan Customizer", "plan_id": "Plan ID", "plan_info": "Plan Info", "plan_is_same_as_current_plan": "Plan is same as current plan", "plan_mode": "Plan mode", "plan_price": "Plan price", "plan_server_choose_error": "Plan server choose error", "plan_subscribe_success": "Connect plan subscribe successfully", "plans": "Plans", "please_activate_an_integration_and_refresh_this_screen": "Please activate an integration and refresh this screen.", "please_add_your_api_key_to_run_more_than_1_test_per_day": "Please add your API KEY to run more than 1 test per day.", "please_check_back_in_few_minutes": "Please check back in few minutes", "please_choose_package": "Please Choose Package!", "please_choose_template": "Please Choose Template!", "please_confirm_access_your_account": "Please confirm access to your account by entering the authentication code provided by your authenticator application.", "please_connect_template": "Please Connect Template.", "please_contact_team_owner": "Please contact team owner.", "please_contact_team_owner_to_upgrade_plan": "To upgrade the team's plan, contact the team owner.", "please_enter_secured_url": "Please Enter URL starting with https.", "please_enter_valid_url_slug": "Please enter a valid URL slug.", "please_enter_valid_zip_file_url": "Please Enter a valid zip file URL.", "please_fill_all_the_required_fields": "Please fill all the required fields", "please_install_connect_plugin_error": "First install the 'instawp-connect' plugin in the template and try again. If the issue persists, please contact support for assistance.", "please_provide_email": "Please provide the email address of the person you would like to add to this team.", "please_repository": "Please add a new repository in the ‘Deployments’ tab", "please_select_ai_provider_list": "Please select AI provider.", "please_select_at_least_one_location": "Please select at least one location", "please_select_template_list": "Please select a template from the list", "please_uncheck_reserve_sites": "Reserved Sites cannot be deleted. To delete, Please uncheck the reserve sites", "please_upgrade_to_continue": "Please upgrade to continue", "please_wait": "Please wait...", "please_wait_for_start_installation": "Fetching WordPress installation details.", "plugin": "Plugin", "plugin_activated": "Plugin :count activated :timestamp", "plugin_configuring": "WP Plugin Configuring", "plugin_deactivated": "Plugin :count deactivated :timestamp", "plugin_deleted": "Plugin :count deleted :timestamp", "plugin_installed": "Plugin :count installed :timestamp", "plugin_is_installed_but_not_connected_to_instawp": "Plugin is installed but not connected to InstaWP", "plugin_slugs": "Plugin slugs from wp.org repo", "plugin_theme_slugs": ":type slugs from wp.org repo", "plugin_updated": "Plugin :count updated :timestamp", "plugin_updated_successfully": "Plugin updated successfully", "plugins": "Plugins", "plugins_and_themes": "Plugins & Themes", "plugins_installation_in_progress": "Plugin(s) Installation In Progress", "point_cname_a_records_to": "Point ‘:type’ records to", "popular": "Popular", "popular_category": "Popular Category", "port": "Port", "post": "Post", "post_created": "Post :count created :timestamp", "post_deleted": "Post :count deleted :timestamp", "post_deployment": "Post-Deployment Commands", "post_max_size": "post_max_size", "post_migration_checks": "Post migration checks", "post_restored": "Post :count restored :timestamp", "post_status": "Post Status", "post_trashed": "Post :count trashed :timestamp", "post_updated": "Post :count updated :timestamp", "postal_code": "Postal code", "postcode_placeholder": "Enter your post code", "posts": "Posts", "pre_install": "Pre-Install Plugins", "pre_install_plugins_or_themes": "Pre-install plugins or themes", "pre_install_theme": "Pre-Install themes", "presigned_url_generated_successfully": "Presigned URL generated successfully", "press_enter_add_new_tag": "Press enter to add new tag", "preview": "Preview", "previous": "Previous", "price": "Price", "template_price": "Template Price", "template_sales_desc": "You don't have any Template Sales", "template_hosting_desc": "You don't have any Hosting Sales", "price_starts_from": "Price starts from", "price_when_you_will_host": "Price when you will host :qty websites with us.", "pricing_calculator": "Pricing Calculator", "pricing_future_content": "Currently every pricing is introductory, but if you subscribe now we will never change the pricing, until you cancel your plan.", "pricing_future_title": " Will you change my pricing in future?", "pricing_plan": "Pricing Plans", "pricing_plan_com": "Pricing plan comparison", "primary": "Primary", "primary_domain_added_successfully": "Primary Domain Added successfully", "primary_payment_card": "Primary Payment Card", "privacy": "Privacy", "privacy_policy": "Privacy policy", "private": "Private", "private_mode": "Private Mode", "pro": "Pro", "proceed": "Proceed", "proceed_and_cancel_plan": "Do you really want to proceed and cancel this plan?", "process_will_take_while_please_check_again_after_few_minutes": "Process will take a while! Please check again after few minutes.", "processing": "Processing...", "product_already_under_review": "Product already under review", "product_created_successfully": "Product created successfully", "product_details_fetched_successfully": "Product details fetched successfully", "product_manager": "Product Manager", "product_title": "Product Title", "product_type": "Product Type", "product_updated_successfully": "Product updated successfully", "product_version_created_successfully": "Template submitted for store approval process.", "product_version_updated_successfully": "Product version updated successfully", "production_site_is_not_ready": "Production site installation is in progress, Please try after sometime.", "products": "Products", "products_fetched_successfully": "Products fetched successfully", "professional": "Professional", "profile": "Profile", "profile_information": "Profile Information", "profile_settings": "Profile Settings", "profile_settings_updated": "Profile Settings Updated Successfully.", "profile_updated_successfully": "Profile Updated Successfully.", "provide_values_for_following_commands": "Provide values for following commands.", "provider_fetch_successfully": "Provider <PERSON> Successfully", "provider_name": "Provider Name", "public": "Public", "public_key": "Public Key", "public_template": "Public Template", "published_pages": "Published Pages", "published_posts": "Published Posts", "pull": "<PERSON><PERSON>", "pull_completed_for_files_and_database": "Pull completed for files and database", "pull_started_for_files_and_database": "Pull started for files and database", "purchase_failed": "Purchase Failed", "purchase_successful": "Purchase Successful", "purchase_template": "Purchase Template", "purge_cache": "<PERSON><PERSON>", "push": "<PERSON><PERSON>", "push_stagine_to_prod": "Push Staging to Production", "push_started_for_files_and_database": "Push started for files and database", "random": "Random", "read_more": "Read More", "read_more_reviews_on": "Read more reviews on", "read_write": "Read Write", "ready": "ready", "ready_to_pull_files_and_database": "Ready to pull files and database", "ready_to_push_files_and_database": "Ready to push files and database", "recent_activity": "Recent Activity", "recent_connected_sites": "Recent Connected Sites", "recent_hosted_sites": "Recent Hosted Sites", "recent_sites": "Recent Staging Sites", "recent_templates": "Recent Templates", "recommendation_found": "Recommendation Found", "recommendations": "Recommendations", "recommendations_found": "Recommendations Found", "recommended": "Recommended", "reconnect": "Reconnect", "records_not_found": "Records Not Found", "recovery_code": "Recovery Code", "redirect_non_wh_users": "Redirect Non-whitelisted Users", "redirect_to_domain": "Redirect www to domain", "redirect_url": "Redirect URL", "redirected_admin_panel": "You are being redirected to the WordPress admin panel.", "redirecting": "Redirecting …", "redirecting_to_stripe_onboarding": "Redirecting to Stripe Onboard Process.", "referral_history": "Referral History", "referral_program": "Referral Program", "referral_subtitle": "If your referral signs up for a paid account, you will get $:self_amount and your referral will get $:referral_amount each.", "referral_title": "Refer your Friend", "referrals_signed_up": "Referrals Signed up", "referrals_signed_up_tooltip": "See how many friends have signed up at InstaWP through your link. They just need to add their card to start earning credits!", "referrals_subscribed": "Referrals Subscribed", "referrals_subscribed_tooltip": "Track the friends who have upgraded to a paid plan using their referral credits. Their subscription means more rewards for you!", "refresh": "Refresh", "refund_policy": "Refund Policy", "regenerate_code": "Regenerate Recovery Codes", "region": "Region", "register": "Register", "register_as_an_affiliate": "Register as an affiliate", "register_block1": "Registrations from your company domain", "register_block2": "has been blocked due as we have detected unusually large number of registrations. For commercial usage of InstaWP, feel free to contact to our sales team", "registrations_blocked": "Registrations blocked!", "rejected": "Rejected", "remark": "Remark", "remember_me": "Remember me", "remote_scanner": "<PERSON><PERSON> Results", "remote_scanner_subtitle": "We will check vulnerabilities against Core, installed Plugins and installed Themes.", "remove": "Remove", "remove_alias": "<PERSON><PERSON><PERSON>", "remove_backup": "Remove <PERSON>up", "remove_coupon": "Remove Coupon", "remove_domain": "Remove domain", "remove_existing_team_members": "You have added the maximum number of team members allowed under your current plan. To add more team members please upgrade your plan or remove existing team members.", "remove_gallery_image": "Remove gallery image", "remove_hosting_connection": "Remove Hosting Connection", "remove_mailtrap": "Remove Mailtrap", "remove_map_domain_successfully": ":url remap domain Successfully", "remove_map_domain_take_time": ":url remap domain shall take 5 mins.", "remove_password": "Remove Password", "remove_ssh_sftp_user": "Remove SSH/SFTP User", "remove_suffix_domain": "Remove Suffix Domain", "remove_team_member": "Remove Team Member", "remove_template": "Remove Template", "remove_snapshot": "Remove Snapshot", "renews_on": "Renews On", "repeating": "repeating", "reply_to_email": "Reply-To Email", "reply_to_name": "Reply-To Name", "repo_ssh": "Repo SSH Link", "repo_type": "Repo Type", "repo_url": "Repository URL", "report": "Report", "report_deleted_success": "Report deleted successfully", "report_not_found": "Report not found", "report_not_found_on_server": "Report not found on server", "report_resent_failed": "Report sent failed", "report_resent_success": "Report sent successfully", "report_sent_successfully": "Report sent successfully.", "reporting_period": "Reporting Period", "reports": "Reports", "repositories": "No repositories found", "repository_id": "Repository ID", "requested_access_type_": "Requested access type :", "requests_per_second": "Requests Per Second", "require_updates": "require updates", "required": "required", "resend": "Resend", "resend_report": "Resend Report", "resend_verification_email": "Resend verification email", "reserve": "Reserve", "reserve_feature_not_available_contact_team_owner": "Reserve site feature is not available in your team's plan. please contact team owner.", "reserve_site_confirm_active_plan": "Reserve site feature is locked in \"Free\" plan please upgrade your account to use this feature.", "reserve_site_please_upgrade_plan_register": "Reserve site feature is not available in your current plan. To reserve sites please upgrade your plan.", "reserve_sites": "Reserve Sites", "reserved": "Reserved", "reserved_site": "Reserved Site", "permanent": "Permanent", "temporary": "Temporary", "site_will_be_deleted_after": "Site will be deleted after", "reserved_sites_cannot_be_deleted_to_delete_this_site_please_Unreserve_it_first": "Reserved Sites cannot be deleted. To delete this site, please \"Unreserve\" it first", "reset": "Reset", "reset_link": "Reset Link", "reset_password": "Reset password", "reset_password_for_security_enhancement": "Reset Password for Security Enhancement", "resolved": "Resolved", "resource_not_found": "The requested resource not found.", "response": "Response", "response_bytes_per_second": "Response Bytes Per Second", "response_time": "Response Time", "restore": "Rest<PERSON>", "restore_backup": "Restore Backup", "restore_count_content": "For free account, we reset it on 1st of every month and for paid accounts at the end of your billing period.", "restore_count_title": "How does <PERSON><PERSON> count per month work?", "restore_failed": "Backup Restored Failed.", "restore_initiated": "Rest<PERSON> initiated", "restore_initiated_wait": "Restore initiated. it may take some time to complete", "restore_progress": "Restore Progress", "restore_site_contact_team_owner": "The maximum number of restored sites allowed under your current team's plan is exceed. To restore more sites please contact team owner.", "restore_site_disk_limit_api": "The site cannot be restored because the disk storage quota exceeded.", "restore_site_ver_in_progress": "Restoration of site version is in progress.", "restore_site_version": "Restore Site Version", "restore_sites": "Restore Sites", "restore_sites_not_allow": "You are not allowed to restore sites please upgrade your plan.", "restore_sites_not_allow_team": "You are not allowed to restore sites please contact team owner.", "restore_success": "Backup Restored Successfully.", "resume_my_subscription": "Resume My Subscription", "resume_plan": "Resume Plan", "resume_subscription": "Resume Subscription", "retry_payment": "Retry Payment", "retry_verification": "Retry Verification", "robotalp_webhook_received": "Robotalp webhook received", "role": "Role", "run": "Run", "run_command": "Run Command", "run_command_des": "Execute wpcli commands to your website through browser.", "run_command_not_allow": "You are not allowed to run command please upgrade your plan.", "run_commands": "Run Commands", "run_your_first_page_speed_insights_performance_check": "Run your first Page Speed Insights performance check.", "running_analysis": "Running Analysis", "running_wordpress_commands": "Running WordPress commands", "sales": "Sales", "sales_template": "The sales your templates have produced through our Marketplace will appear here", "sales_hosting": "Sales you produced through your hosting requests will appear here", "sanctum": "Sanctum", "sandbox": "Sandbox", "saturday": "Saturday", "save": "Save", "save_as_temp": "Save as template", "save_as_snap": "Save as snapshot", "save_new": "Save As New", "save_new_version": "Save New Version", "save_new_version_text": "Save current snapshot using the button below.", "save_ssh_key": "Save SSH Key", "save_sync": "Save & Sync", "save_template": "Save Template", "save_template_snapshot": "Save Template/Snapshot", "saved": "Saved", "scan_for_updates": "Scan for updates", "scanning": "Scanning...", "scanning_in_progress": "Scanning in progress...", "scanning_is_available_once_every_24_hours": "Scanning is available once every 24 hours.", "schedule": "Schedule", "scheduled": "Scheduled", "scheduled_report": "Scheduled Report", "scheduled_report_disabled": "Scheduled report is disabled for :url", "scheduled_report_enabled": "Scheduled report is enabled for :url", "scheduled_report_updated": "Scheduled report settings updated successfully", "scheduled_reports": "Scheduled Reports", "scheduled_updates": "Scheduled Updates", "scheduled_updates_disabled": "Scheduled updates are disabled for :url", "scheduled_updates_enabled": "Scheduled updates are enabled for :url", "scheduled_updates_updated": "Scheduled updates settings updated successfully", "search": "Search", "search_log": "Search Log", "search_market_place_title": "Showing results from Wp.org for keyword “seo”", "sec": "sec", "second": "Second", "seconds": "(seconds)", "secret_key": "Secret Key", "security": "Security", "security_report_created": "Site Vulnerability Report Created.", "security_scan": "Security Scan", "security_scan_not_allow": "You are not allowed to do vulnerability scan please upgrade your plan.", "select": "Select", "select_PHP_version": "Select PHP version", "select_a_server": "Select a Server", "select_all": "Select All", "select_all_number_sites": "{1} Select All :count site|[2,*] Select All :count sites", "select_all_number_templates": "{1} Select All :count template|[2,*] Select All :count templates", "select_all_number_snapshots": "{1} Select All :count snapshot|[2,*] Select All :count snapshots", "select_all_resources": "Select all :number :resources", "select_data_center": "Select Data center", "select_design": "Select Design", "select_discount": "Select Discount", "select_file": "Drag n drop some files here, or click to select files", "select_frequency": "Select Frequency", "select_mailtrap_integration": "Select Mailtrap Integration", "select_method_go_live": "Select a Method to Go Live", "select_monetize": "Select Monetize", "select_plan": "Select Plan", "select_repositories": "Select the repositories you want to deploy when site is created from this template", "select_sites": "Select Sites", "select_template": "Select Template(s)", "select_user": "Select User", "selected_cpu_is_same_as_current_cpu": "Selected CPU is same as current CPU.", "selected_space_is_same_as_current_space": "Selected Space is same as current Space.", "sell_templates_not_allow": "You are not allow to sell templates.", "sell_templates_with_hosting": "<PERSON><PERSON> Templates with Hosting.", "seller": "<PERSON><PERSON>", "sell": "<PERSON>ll", "send_copy_of_the_report_to": "Send copy of the report to", "send_credentials_email": "Send Credentials to Email", "send_to": "Send to", "send_webhook_after_site_creation": "Send Webhook after Site Creation", "sent_invitation_email": "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.", "seo_plugins": "SEO Plugins", "server_error": "Server error", "server_error_re_try": "Server error, please re-try later.", "server_loc": "Server Location", "server_loc_not_allow": "You are not allowed to make changes in server location please upgrade your plan.", "server_location": "Server Location", "server_not_found": "Server not found.", "server_size": "Server size", "service_powered": "WordPress site. This service is powered by", "services": "Services", "session_created_successfully": ":source session created successfully.", "set": "Set", "set_as_primary": "Set as Primary", "set_as_production": "Set as production", "set_default_magic_login_user": "Set as Magic Login User", "set_expiry_date": "Set Expiry Date", "set_magic_login_user_email": "Set Magic Login User Email", "set_password": "Set Password", "set_primary": "Set Primary", "set_primary_domain": "Set as primary domain", "set_primary_domain_successfully": "<PERSON> as Primary successfully", "set_username": "<PERSON>rname", "setting_save_successfully": "Setting save successfully", "setting_up_site": "Setting up your site…", "setting_up_your_site": "Setting up Your Site", "settings": "Settings", "setup": "Setup", "setup_a_new_site": "Setup a new site", "setup_account": "Setup Account", "setup_your": "Setup your", "setup_your_site": "Setup Your New Site", "severity": "Severity", "sftp_feature_not_available": "SFTP Access feature is not available in your current plan please upgrade your account to use this feature.", "sftp_feature_not_available_team": "SFTP Access feature is not available in your team's plan. please contact team owner.", "sftp_ssh": "SFTP/SSH", "sftp_ssh_access_available": "SFTP/SSH Access feature is not available in your current plan please upgrade your account to use this feature.", "sftp_ssh_des": "Access website though SFTP or SSH.", "sftp_ssh_feature_not_available": "SFTP/SSH Access feature is not available in your team's plan. please contact team owner.", "share": "Share", "shared": "Shared", "shared_template_account_creation_message": "You need an account with InstaWP to access your purchase of this template. If you don't have an account, we will create a free account.", "shared_template_account_login_message": "We have detected an account with this email. <PERSON>gin to add this site to your account.", "shared_with": "Shared With Me", "sharing_options": "Sharing Options", "shop": "Shop", "show": "Show", "show_recovery": "Show Recovery Codes", "sign_in": "Sign In", "sign_up": "Sign Up", "signed_url_generated": "Signed URL generated successfully.", "site": "Site", "site_already_config": "Site Already Config", "site_already_configured": "This site is already configured with other user or team.", "site_configured": "Site configured", "site_connected_with_current_team": "This site is already connected with current team.", "site_connected_with_other_team": "This site is already connected with other team or user", "site_count": ":countSite Sites", "site_create": "Create site", "site_create_limit_api": "Create site quota exceed, :limit sites allowed under your current plan.", "site_created": "Site created.", "site_created_at_instaWP": "Site created at InstaWP", "site_creation": "Site creation", "site_creation_failed": "Site Creation Failed", "site_creation_started_in_instaWP": "Site creation started in InstaWP", "site_delete": "Site:delete", "site_deleted": "Site Deleted", "site_deletion_work_in_progress": "Site deletion work in progress.", "site_details_fetched_successfully": "Site details fetched successfully.", "site_disconnected": "Site Disconnected", "site_disk_limit_api": "Disk storage quota exceed, :limit MB disk storage allowed under your current plan.", "site_domain_added": "Site Domain Added", "site_domain_deleted": "Site Domain Deleted", "site_edit": "Site Edit", "site_expired": "Site Expired", "site_export": "Site Export", "site_suspended": "Suspended", "site_export_for_instawp": "Site Export for InstaWP", "site_export_for_localwp": "Site Export for LocalWP", "site_export_for_wp_studio": "Site Export for Wordpress Studio", "site_for_instawp_will_create_zip_file": "Site for InstaWP ? It will create a zip file.", "site_for_wp_studio_will_create_zip_file": "Site for Wordpress Studio ? It will create a zip file.", "site_for_localwp_will_create_zip_file": "Site for LocalWP ? It will create a zip file.", "site_has_been_deleted": "Site has been deleted.", "site_install_complete": "Site installation work is completed, Your website is ready!", "site_install_in_progress": "Site installation work is in progress, Please wait for installation.", "site_installation_in_progress": "Site installation work in progress, wait for installation.", "site_installing": "Site installing", "site_integration_removed": "Site integration removed", "site_integration_successful": "Site integration successful", "site_label_updated": "Site Label updated.", "site_label": "Site Label", "site_language": "Site Language", "site_lifetime": "Site Lifetime", "site_lifetime_notice": "Site Lifetime will not apply to paid Monetized Templates, except for the WaaS demo sites.", "site_list": "Site List as CSV", "site_monthly_limit_exceed": "Monthly Site Limit Exceed", "site_move_to_staging": "This is a temporary staging site created by the shared template. Do you wish to convert :site_url to a regular Staging site and move it under the \"Staging\" tab. This can be useful for more operations such as Reserving the site or checking logs etc.", "site_moved": "Site moved successfully.", "site_name": "Site Name", "site_name_not_available": "site name is not available.", "site_ns_record_not_found": "Site NS record not found", "site_plan": "Site Plan", "site_read": "Site:read", "site_reserve": "Site Reserve", "site_reserve_info": "To prevent automatic deletion, mark staging sites as reserved.", "site_reserve_info_2": "This site remains reserved while your subscription is active but may be deleted if your subscription expires.", "site_reserve_title": "This site will be deleted in :days days", "site_reserve_title_2": "This is a permanent staging site", "site_restore_progress": "Site restoration work in progress, Wait for restoration.", "site_restored": "Site restored", "site_security_reports_fetched_successfully": "Site vulnerability reports fetched.", "site_suffix_domain": "Site Suffix Domain", "site_template_deleted": "Site Template Deleted", "site_snapshot_deleted": "Site Snapshot Deleted", "site_unreserve": "Site Unreserve", "site_update": "Site:update", "site_update_in_progress": "Site update is in progress.", "site_url": "Site URL", "site_usage": "Site Usage", "site_usage_fetched_successfully": "Site usage fetched successfully", "site_ver_not_for_site": "Site version is not from selected site.", "site_version": "Site Version", "site_version_created": "Site version created successfully.", "site_version_deleted": "Site versions deleted.", "site_version_not_allow": "You are not allowed to make a site version please upgrade your plan.", "site_version_not_allow_team": "You are not allowed to make a site version please contact team owner.", "site_version_not_complete": "Site version is not completed.", "site_version_updated": "Site version updated successfully.", "site_versions": "Site Versions", "site_versions_fetched_successfully": "Site versions fetched successfully.", "site_will_create_zip_file": "Site ? It will create a zip file.", "site_will_move": "The site will be moved to the dashboard.", "site_will_not_delete": "Site will not be deleted", "sites": "Sites", "sites_fetched_successfully": "Sites fetched Successfully.", "sites_selected": "sites selected", "sites_updates": "Sites/Updates", "skip_form_if_url_query_parameters_are_set": "Skip form if URL query parameters are set", "slack": "<PERSON><PERSON>ck", "slug": "Slug", "smtp_encryption": "SMTP Encryption", "smtp_from_address": "SMTP From Address", "smtp_from_name": "SMTP From Name", "smtp_host": "SMTP Host", "smtp_password": "SMTP Password", "smtp_port": "SMTP Port", "smtp_username": "SMTP Username", "snapshot": "Snapshot", "snapshot_desc": "To create more sites", "snapshot_info": "Templates are renamed to Snapshots and Monetized Templates are moved to Sell > Templates", "template_info": "New: Monetized Templates are now just 'Templates'", "sold": "Sold", "something_went_wrong": "Something went wrong.", "sort_by_created_date": "Sort by Created Date", "sort_by_name": "Sort by Name", "sort_by_updates": "Sort by Updates", "source": "source", "source_destination_domain_not_same": "source domain and destination domain must be different", "source_site_url": "Source site URL", "source_url": "Source URL", "source_website": "Source Website", "sources": "sources", "space": "Used Space", "space_used": "Space Used", "speed_plugin": "Speed Plugin", "sponsor": "Sponsor", "ssh_access": "SSH Access", "ssh_command": "SSH Command", "ssh_fetched_successfully": "SSH key Fetched Successfully.", "ssh_key": "SSH Key Pair", "ssh_key_deleted_successfully": "SSH key deleted successfully.", "ssh_key_stored_successfully": "SSH key stored successfully.", "ssh_key_title": "SSH Key", "ssh_keys": "SSH Keys", "please_enable_ssh_to_add_new_ssh_key": "Please enable SSH to add new SSH key", "ssh_or_terminal_access_is_required_for_this_method": "SSH or Terminal access is required for this method", "ssl_retry_initiated": "SSL retry initiated.", "staging": "Staging", "staging_features": "Staging Features", "staging_site": "Staging Site", "staging_site_linked": "Staging Site is Linked.", "staging_sites": "Staging Sites", "staging_sites_description": "You can create one staging live site", "start": "Start", "start_building_or_hosting_wordpress_site_by_clicking_on_new_site": "Start building or hosting a WordPress website by clicking on “New Site”", "start_cloning": "Start Cloning", "start_connecting_a_wordpress_website_by_clicking_on_connect_site": "Start connecting a WordPress website by clicking on “Connect Site”", "start_free_trial": "Start Free Trial", "start_migration": "Start Migration", "start_over": "Start Over", "start_your_days_trial": "Start your :days days trial", "started_at": "Started at", "starting_installation": "Starting Installation", "state": "State", "state_placeholder": "Select a state", "states_list_fetched": "States list fetched", "static": "Static", "statistics": "Statistics", "statistics_description": "Here is a summary of your hosting site statistics.", "stats": "Stats", "status": "Status", "stay_current_team": "Stay in my current team", "step": "Step", "step_1_run_this_command_on_source": "Step 1 : Run this command on source", "step_2_run_this_command_on_destination": "Step 2 : Run this command on destination", "stop": "Stop", "stop_site_transfer": "Stop site transfer", "store": "Store", "store_categories_fetched_successfully": "Store Categories fetched successfully.", "store_product": "Store Product", "store_product_details_fetched_successfully": "Product details fetched successfully", "store_product_version_details_fetched_successfully": "Product version details fetched successfully", "store_profile_updated_successfully": "Store profile updated successfully", "store_recovery": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "store_tags_fetched_successfully": "Store tags fetched successfully.", "store_template_will_be_unmarked_as_reserved": "is linked to the store and this action will unmark the website as \"Reserved\" and expire after a specific time period. You will be lost the preview for the store after the site delete.", "street_address": "Street address", "stripe_prices_fetched_successfully": "Stripe Prices Fetched Successfully", "sub_domain": "Sub Domain", "sub_domain_not_found": "Sub domain not found. This will not work on old sites.", "subject": "Subject", "subscribe_mailing_list": "Subscribe to mailing list", "subscribed_successfully": "You are now subscribed to new plan successfully.", "subscribers": "Subscribers", "subscription": "Subscription", "subscription_cancelled": "Subscription Cancelled.", "subscription_details": "Subscription Details", "subscription_error": "Subscription Error", "subscription_status": "Subscription Status", "subscription_status_tooltip": "If your referral has subscribed or not.", "subtitle": "Subtitle", "success": "Success", "success_credit_heading_txt": "Thanks for participating in WordCamp Giveaway by InstaWP. You have entered into our raffle and :amount promo credits has been added to your account.", "success_credit_subheading_txt": ":amount Promo Credit Added", "success_extended_lifetime": "Congratulations! Your site lifetime has been extended by :extended_days days, total time remaining is :remaining_days.", "success_rate_in_all_the_time": "Success Rate In All the time", "success_rate_in_last_24_hours": "Success Rate In Last 24 hours", "success_rate_in_last_7_days": "Success Rate In Last 7 days", "suffix_change_diabled_for_mapdoamin": "Note: This option is disabled because you have mapped a domain to this site, please remove the mapping to re-enable this option.", "suffix_domain": "Suffix Domain", "suffix_domain_added": "Suffix Domain Added", "suffix_domain_fetched_successfully": "Suffix domain fetched successfully", "suffix_domain_not_found": "Suffix domain not found", "suffix_domain_removed_successfully": "Suffix Domain Removed Successfully", "suffix_domain_setup_and_verify": "Please Setup and Verify your Suffix Domain.", "suggested_plugins_to_install": "Suggested Plugins to install", "summary": "Summary", "sunday": "Sunday", "supercharge_your_templates_instant_creation": "Supercharge your templates for instant creation", "supercharge_your_snapshots_instant_creation": "Supercharge your snapshots for instant creation", "support_access": "Support Access", "support_access_sub_heading": "Grant :brand_name support temporary access to your account so we can troubleshoot problems on your behalf. You can revoke access at any time.", "support_migration_any_hosting_company": "Supports migration to any hosting company with this option. 90%+ success rate.", "sure_cancel": "Are you sure you want to cancel? Once it gets cancelled you will be subscribed back to Free Plan.", "sure_delete_api": "Are you sure you would like to delete this API token?", "sure_delete_config": "Are you sure you want to delete this Configuration?", "sure_delete_connection": "Are you sure want to disconnect?", "sure_delete_repository": "Are you sure you want to delete this Repository?", "sure_delete_site_versions": "Are you sure you want to delete selected site versions?", "sure_leave_team": "Are you sure you would like to leave this team?", "sure_push_staging_to_production": "Are you sure you want to push staging to production?", "sure_remove_gallery_image": "Are you sure you want to remove this image from the gallery?", "sure_remove_list": "Are you sure you want to remove this URL from the list?", "sure_remove_team": "Are you sure you would like to remove this person from the team (All assets previously assigned to this team member will now be assigned to the Team Owner :", "sure_restore_site_version": "Are you sure you want to restore selected site version?", "survey_created_successfully": "Survey created successfully", "survey_not_found": "Survey not found", "survey_paused_successfully": "Survey paused successfully", "survey_response_saved_successfully": "Survey response saved successfully", "survey_saved_successfully": "Survey saved successfully", "suspended": "Suspended", "suspended_due_to_disk_overuse": "Suspended due to disk overuse", "switch_off_activity_logs": "Switch off Activity Logs", "switch_to_team": "Switch to team", "switch_to_your_team": "Switch To Your Team", "sync_changes_from_parent_site": "Sync Changes from Parent Site", "sync_from_site": "Sync from Site", "sync_now": "Sync Now", "sync_parent_site_php_configs": "Sync Parent Site PHP Configurations", "sync_quota_limit": "Sync Events Quota", "sync_quota_limit_error": "Sync quota limit has been exhausted, Please buys more addon", "sync_template": "Sync Template", "synced": "Synced", "synced_with": "synced with", "syncs": "Syncs", "table": "Table", "tag_name": "Tag Name", "tag_remove_confirm": "Are you sure you want to remove \":name\" tag?", "tags": "Tags", "tags_updated_success": "Tags updated.", "tags_comma_separated": "Tags (comma separated)", "task_details_retrived": "Task details retrived.", "tax_id": "Tax ID", "tax_number_placeholder": "Enter your tax number", "tax_type": "Tax Type", "tax_type_placeholder": "Select your tax type", "team-store-logo-deleted": "Team store logo deleted", "team_clone_upgrade_subheading": "You are not allowed to clone sites please contact team owner.", "team_created_successfully": "Team is created successfully", "team_details": "Team Details", "team_details_description": "Create a new team to collaborate with others on projects.", "team_invitation_sent": "Team Invitation sent", "team_members": "Team Members", "team_members_fetched_successfully": "Team members fetched successfully.", "team_name": "Team Name", "team_name_owner_information": "The teams name and owner information.", "team_owner": "Team Owner", "team_quota_exceed": "Team quota exceed", "team_quota_exceed_subheading": "The maximum number of templates allowed under your current team's plan is exceed. To create more templates please contact team owner.", "team_setting": "Team Settings", "team_settings": "Team Settings", "team_site_quota_exceed": "Team site quota exceed", "team_tags_fetched_successfully": "Team tags fetched successfully.", "teams": "Teams", "teams_fetched_successfully": "Teams fetched successfully", "teams_not_allow": "You are not allowed to create team, please upgrade your plan.", "temp_link_copy_in_clipboard": "Temporary login link copied to clipboard. Link is valid for 6 hours.", "temp_login_link_created": "Temporary login link created successfully.", "temp_login_url": "Temp Login URL", "template": "Template", "template_advance_config_not_allow": "You are not allowed to change template advance configurations please upgrade your plan.", "snapshot_advance_config_not_allow": "You are not allowed to change snapshot advance configurations please upgrade your plan.", "template_created_successfully": "Template created successfully", "snapshot_created_successfully": "Snapshot created successfully", "template_csv_export_not_allow": "You are not allowed to export CSV please upgrade your plan.", "snapshot_csv_export_not_allow": "You are not allowed to export CSV please upgrade your plan.", "template_customization_not_allow": "You are not allowed to change template customization please upgrade your plan.", "snapshot_customization_not_allow": "You are not allowed to change snapshot customization please upgrade your plan.", "template_features": "Template Features", "template_hosting_provider_add": "Hosting provider added successfully.", "snapshot_hosting_provider_add": "Hosting provider added successfully.", "template_hosting_provider_update": "Hosting provider updated successfully.", "snapshot_hosting_provider_update": "Hosting provider updated successfully.", "template_is_now_monetized": "Template is now monetized.", "template_moved": "Your site has been moved to the dashboard.", "snapshot_moved": "Your site has been moved to the dashboard.", "template_moved_successfully": "Your site has been moved to the dashboard.", "snapshot_moved_successfully": "Your site has been moved to the dashboard.", "template_name": "Template Name", "snapshot_name": "Snapshot Name", "template_not_associate_with_waas": "<PERSON><PERSON><PERSON> is not associate with current WaaS", "template_not_found": "<PERSON><PERSON><PERSON> not found.", "template_sales": "Template Sales", "template_share_successfully": "Template share status updated successfully.", "snapshot_share_successfully": "Snapshot share status updated successfully.", "template_sites": "Demo Sites", "template_sites_statistics": "demo sites statistics of :days days.", "template_statistics": "Template Statistics", "snapshot_statistics": "Snapshot Statistics", "template_statistics_retrived": "Template statistics retrived.", "snapshot_statistics_retrived": "Snapshot statistics retrived.", "template_store_coming": "We are accepting submissions to the template store. If you a designer or theme author, upload your templates today!", "template_sync_initiated": "Template sync has been initiated.", "template_type": "Template Type", "snapshot_type": "Snapshot Type", "template_updated": "Template Updated.", "template_updated_successfully": "Template updated successfully", "snapshot_updated_successfully": "Snapshot updated successfully", "templates": "Templates", "templates_desc": "To sell via link, WaaS, or Store", "monetize_templates": "Monetize Templates", "snapshots": "Snapshots", "snapshot_updated": "Snapshot Updated.", "templates_delete_progress": "Template deletion in progress.", "snapshot_delete_progress": "Snapshot deletion in progress.", "templates_fetched_successfully": "Templates Fetched Successfully", "snapshots_fetched_successfully": "Snapshots Fetched Successfully", "temporary_site": "Temporary Site", "tenant_move_current_version": "Tenant moved to current version successfully.", "tenants_created_successfully": "Tenant Created Successfully", "term_created": "Term :count created :timestamp", "term_deleted": "Theme :count deleted :timestamp", "term_updated": "Theme :count updated :timestamp", "terms_of_service": "Terms of service", "text_format_incorrect": "Text format is incorrect. Use only comma separate text. no space and enter allowed.", "the_default_payment_method_will_be_any_billing_purposes": "The default payment method will be used for any billing purposes.", "the_maximum_disk_quota_of_sites_allowed": "The maximum disk quota of sites allowed under your current teams plan is exceed. To create more sites please contact team owner.", "the_maximum_disk_quota_of_team_members_allowed": "The maximum disk quota of team members allowed is :quota.", "the_parent_site_either_deleted_or_expired": "The parent site either deleted or expired.", "the_payment_method_will_be_removed": "The Payment Method will be removed", "the_payment_method_will_permanently_removed": "The Payment Method will be permanently removed from the stripe.", "the_plugin_url_must_be_start_with_https_wordpress_org_plugins_": "The plugin url must be start with https://wordpress.org/plugins/", "the_site_name_must_not_be_greater_than_characters": "The site name must not be greater than :characters characters.", "the_site_permanently_removed_from_the_server": "The site will be permanently removed from the server.", "the_site_will_be_removed_from_server": "The site will be removed from server", "the_ssh_key_permanently_removed_from_the_server": "The SSH key will be permanently removed from the server.", "the_theme_url_must_be_start_with_https_wordpress_org_themes_": "The theme url must be start with https://wordpress.org/themes/.", "the_user_has_been_added_successfully": "New user has been added successfully.", "theme": "Theme", "theme_activated": "Theme :count activated :timestamp", "theme_deleted": "Theme :count deleted :timestamp", "theme_installed": "Theme :count installed :timestamp", "theme_slugs": "Theme slugs from wp.org repo", "theme_updated": "Theme :count updated :timestamp", "theme_updated_successfully": "Theme updated successfully", "themes": "Themes", "third": "Third", "third_party_hosting": "3rd Party Hosting", "third_party_token": "API tokens allow third-party services to authenticate with our application on your behalf.", "this_feature_is_available_in_plan_and_above": "This feature is available in :planName plan and above.", "this_information_displayed_publicly_careful_what_you_share": "This information will be displayed publicly so be careful what you share.", "this_information_will_be_displayed_publicly_so_be_careful_what_you_share": "This information will be displayed publicly so be careful what you share.", "this_information_will_be_displayed_publicly_so_please_be_careful_about_what_you_share": "This information will be displayed publicly, so please be careful about what you share.", "this_integration_will_be_visible_to_the_admin_only": "This integration will be visible to the admin only.", "this_integration_will_be_visible_to_the_entire_team": "This integration will be visible to the entire team.", "this_month": "This Month", "this_settings_can_only_be_changed_in_the_advanced_plan": "These settings can only be changed in the Advanced Plan", "this_site_is_connected_with_a_live_site": "This site is connected with a live site", "this_site_will_expire": "This site will expire in", "this_site_will_expire_hours": "This site will expire in 4 hours.", "this_week": "This Week", "this_will_automatically_reset_the_instawp_connect_plugin_from_the_site": "This will automatically reset the InstaWP Connect plugin from the site.", "this_will_completely_override_destination_site": "This will completely override destination site.", "this_will_not_delete_any_staging_sites_created_from_this_connected_site": "This will NOT delete any staging sites created from this connected site!", "this_year": "This Year", "thursday": "Thursday", "time_expire": "Time To Expire", "time_to_upgrade": "Time to Upgrade!", "title": "Title", "to": "to", "to_personalize_the_trial_landing_page": "To personalize the trial landing page.", "to_start_selling": "To Start selling, setup your payout account", "to_unlock": "to unlock!", "today": "Today", "token_deleted_successfully": "Token Deleted Successfully", "token_fetched_successfully": "To<PERSON> Fetched Successfully", "token_generated_successfully": "Token generated successfully", "token_mismatched": "Token does not match", "token_permissions": "API Token Permissions", "token_validated_successful": "<PERSON><PERSON> validated successfully", "tools": "Tools", "total": "Total", "total_orders": "Total Orders", "total_sites": "Total Sites", "track": "Track", "trial_days": "Trial Days", "trial_plan_started_successfully": "Trial Plan Started Successfully", "trial_program": "Trial Program", "trial_program_description": "Please select a discount type and enter your affiliate code to generate a unique link which can be used by your customers to start a free trial of InstaWP.", "trial_program_started_successfully": "🎉Trial Program Started Successfully", "trigger": "<PERSON><PERSON>", "try": "Try", "tuesday": "Tuesday", "turn_off_automated_performance_scan": "Turn off Automated Performance Scan", "turn_off_automated_vulnerability_scan": "Turn off Automated Vulnerability Scan", "turn_on_automated_performance_scan": "Turn on Automated Performance Scan", "turn_on_automated_vulnerability_scan": "Turn on Automated Vulnerability Scan", "two_factor_auth": "Two Factor Authentication", "two_way_sync": "2 Way Sync", "type": "Type", "type_new_password": "Type new password", "type_new_username": "Type new username", "type_record_not_created": ":type record not created", "unique_link_copy_in_clipboard": "Unique link copied in your clipboard.", "unlock": "Unlock", "unmonetize_not_allow": "You are not allow to unmonetized because of active orders.", "unmonetize_not_allow_waas": "You are not allow to unmonetized because of active WaaS templates.", "unreserve": "Unreserve", "upcoming_invoice": "Upcoming Invoice", "update": "Update", "update_account": "Update your accounts profile information and email address.", "update_all": "Update All", "update_available": "Update Available", "update_billing_details": "Update your billing details to show in your invoices.", "update_card": "Update card", "update_configuration": "Update Configuration", "update_connect_plugin_to_latest_version": "Please update Connect plugin to the latest version.", "update_now": "Update Now", "update_pass": "Update Password", "update_payout_info": "Update your payout method and information", "update_php_config": "Update PHP configurations", "update_plan": "Update Plan", "update_plugin_to_latest_version": "Update plugin to latest version", "update_started_successfully": "Update :resource started successfully", "update_subscription": "Update Subscription", "update_to": "Update to", "updates": "Updates", "upgrade": "Upgrade", "upgrade_free_acc": "To unlock more power from your InstaWP account and supercharge your WordPress workflow, upgrade to a paid plan today!", "upgrade_now": "Upgrade Now", "upgrade_plan": "Upgrade", "upgrade_to": "Upgrade to", "upgrade_to_pro_plan": "Upgrade to Pro Plan", "upgrade_unlock": "Upgrade to unlock", "upload": "Upload", "upload_file": "Upload File", "upload_max_filesize": "upload_max_filesize", "upload_your_logo": "Upload your logo", "upload_zip_url": "Upload ZIP URL", "uploading_files": "Uploading Files", "uploading_files_finished": "Uploading Files Finished", "uptime": "Uptime", "uptime_history": "Uptime History", "per_minute_uptime_monitoring": "Per-minute Uptime Monitoring", "uptime_monitoring": "Uptime Monitoring", "uptime_monitoring_cannot_be_enabled": "Uptime Monitoring cannot be enabled", "uptime_monitoring_disabled": "Uptime Monitoring Disabled", "uptime_monitoring_enabled": "Uptime Monitoring Enabled", "uptime_settings_fetched_successfully": "Uptime settings fetched successfully.", "uptime_settings_for_title": "Uptime settings for :title", "uptime_settings_updated_successfully": "Uptime settings updated successfully.", "uptime_ssl_status_description": "We check your website's uptime and SSL status here.", "url": "URL", "url_is_now_connected": ":url is now connected.", "url_must_be_secured": "Url must be secured with https", "url_theme": "URL for theme or plugin (.zip)", "usa": "USA", "usage_description": "Here is a summary of your hosting site usage.", "usages_exceeded": "O-oh! <PERSON>it reached", "usages_exceeded_subtext": "You have reached your limits in your current plan. But do not worry! You can buy Addons on top of your plan and increase limits.", "use_a_recovery_code": "Use a recovery code", "use_authentication_code": "Use an authentication code", "use_default_settings_blazing_fast_launch": "Use default settings for blazing fast launch", "use_hosting_provider_domain": "Use :providerName domain", "use_instawp_domain": "Use InstaWP domain", "user": "User", "user_added_to_team": "User added to team successfully", "user_agent": "User Agent", "user_already_in_team": "User is already exist in team", "user_clone_upgrade_subheading": "You are not allowed to clone sites please upgrade your plan.", "user_created_successfully": "User created successfully.", "user_deleted": "User :count deleted :timestamp", "user_does_not_exist": "User does not exist.", "user_has_been_created": "User has been created.", "user_invited_to_team": "User invited to team successfully", "user_is_disabled": "User account is disabled.", "user_logged_in": "User :count logged in :timestamp", "user_logged_out": "User :count logged out :timestamp", "user_name": "User name", "user_not_exist_in_team": "User does not exist in team", "user_not_found": "User does not found", "user_registered": "User :count registered :timestamp", "user_removed_from_team": "User is removed from team successfully", "user_survey_not_found": "User survey not found", "user_updated": "User :count updated :timestamp", "username": "Username", "users": "Users", "utc_timezone": "UTC Timezone", "v1_legacy": "v1 (Legacy)", "v2_beta": "v2", "valid_plugin_slug": "Please enter valid plugin slugs.", "valid_plugin_url": "Please enter valid plugin URLs.", "valid_theme_slug": "Please enter valid theme slugs.", "valid_theme_url": "Please enter valid theme URLs.", "validation.down_grade_plan": "You cannot downgrade to a plan that doesn’t support your current number of sites.", "validation.not_archived_plan": "The selected plan is archived or invalid.", "validation.update_plan": "You are already subscribed to this plan. Please select different plan", "validation.alias_max": "The alias must not be greater than :max characters.", "validation.alias_required": "The alias field is required.", "validation.attributes": [], "validation.disposable_email": "Invalid <PERSON><PERSON>, Please try with different email address.", "validation.in": "The selected :attribute is invalid.", "validation.invalid_credentials": "Invalid api key or list id supplied. Please check your inputs.", "validation.invalid_email": "Email is not valid. Please try with a valid email address.", "validation.max.file": "The :attribute must not be greater than :max kilobytes.", "validation.max_file_size": "The :attribute must not be greater than :max.", "validation.no_reserve_sites": "Creating reserve sites is not supported in :plan plan. Please upgrade to use this feature.", "validation.no_sites": "Creating sites is not supported in :plan plan. Please upgrade to use this feature.", "validation.not_regex": "The :attribute format is invalid.", "validation.overuse_email": "Email domain is Overused, Please contact to our sales team.", "validation.php_version_compatible": "PHP version is not compatible with this Site.", "validation.plain_email_exist": "Email is already exist. Please try with different email address.", "validation.regex": "The :attribute format is invalid.", "validation.required": "The :attribute field is required.", "validation.reserve_sites_limiter": "Sorry you have reached limit of \":count reserve sites\" in the :plan plan. To create more sites, please upgrade your plan.", "validation.sites_limiter": "Sorry you have reached limit of \":count active sites\" in the :plan plan. To create more sites, please upgrade your plan.", "validation.subdomain": "Invalid Domain Name, Enter domain name without \"http\", \"www\" e.g. mydoamin.demo.com", "validation.downgrade_plan": "You have already consumed current plan active sites. Immediate downgrading the plan is not possible. Please wait till the current plan ends.", "validation.not_archived": "Currently this Plan is archived. Please select another plan for subscription.", "validation_error": "Validation Error", "values": "Values", "vendor_name": "Vendor Name", "verification_failed": "Verification Failed", "verification_in_progress": "Verification in progress", "verification_required": "Verification Required", "verification_suffix_domain_in_progress": "Verification Suffix Domain In Progress.", "verified": "Verified", "verify": "Verify", "verify_account": "Verify Account", "verify_email_message": "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you did not receive the email, we will gladly send you another.", "verify_email_message_top": "Please verify your email to remove the :site_hours hours site usage restriction.", "verify_email_sent": "Verification email sent.", "verify_now": "Verify Now", "verify_record": "Verify Record", "verify_your_email_address": "Verify your email address", "version": "VERSION", "version_invalid": "Selected version are invalid.", "versions": "Versions", "versions_fetched_successfully": "Versions Fetched Successfully", "view": "View", "view_all": "View all", "view_all_connected_sites": "All Connected Sites", "view_all_hosted_sites": "All Hosted Sites", "view_all_posts": "View all posts", "view_all_sites": "All Staging Sites", "view_all_templates": "All Templates", "view_billing_details": "View billing details", "view_breakdown": "View breakdown", "view_dashboard": "View Dashboard", "view_details": "View Details", "view_doc": "View Doc", "view_docs": "View Docs", "view_documentation": "View Documentation", "view_less": "View Less", "view_log": "View Logs", "view_logs_description": "Track changes, updates, and important events to maintain site security and performance.", "view_more": "View More", "view_pricing": "View Pricing", "view_receipts": "View receipts", "view_report": "View Report", "view_stats": "View Stats", "view_subscription": "View Subscription", "view_treemap": "View Treemap", "view_trends_of_staging_site_creation_for_your_template": "View trends of staging site creation for your template", "view_trends_of_staging_site_creation_for_your_snapshot": "View trends of staging site creation for your snapshot", "visibility": "Visibility", "visitors": "Visitors", "visual_composer": "Visual composer", "vulnerabilities": "Vulnerabilities", "vulnerability_scan_limit_reached": "Your daily limit for vulnerability scan has reached. Please wait for :hours hours to perform another scan.", "vulnerability_scanner": "Vulnerability Scanner", "vulnerability_scanning": "Vulnerability Scanning", "automated_vulnerability_scanning": "Automated Vulnerability Scanning", "waas": "WaaS", "waas_add_card": "Add Card", "waas_add_feature": "Add Feature", "waas_add_more": "Add More", "waas_add_price": "Add price", "waas_ask_for_payment_in_the_beginning": "Ask for payment in the beginning", "waas_ask_for_payment_sub_heading": "Be default, we will ask for credit card details and charges the card during the setup.", "waas_background_color": "Background Color", "waas_background_image": "Background Image", "waas_brand_color": "Brand Color", "waas_branding": "Branding", "waas_click_go_live_inside_wordpress": "This is a demo website, once you are ready to publish, click on Go Live inside the WordPress admin panel", "waas_color": "Color", "waas_create": ":WaaS is created successfully", "waas_create_add_a_valid_card": "To create a WaaS please add a valid card. <a target=\"_blank\" href=\"//docs.instawp.com/en/article/waas-pricing-nrz7h2/\" class=\"hover-item\">Why?</a>", "waas_default_plan": "Default plan", "waas_delete_existing": "Delete Existing", "waas_enabled": "WaaS Enabled", "waas_enter_brand_color": "Enter Brand Color", "waas_enter_short_description": "Enter short description here", "waas_hosting_plan_tooltip": "Please complete all plan details", "waas_hosting_plans": "Hosting Plans", "waas_hosting_provider": "Hosting Provider", "waas_hosting_settings": "Hosting Settings", "waas_image": "Image", "waas_integration": "Private WaaS for ", "waas_integration_sub_heading": "Call WaaS webhooks to generate one-time use checkout links.", "waas_name": "WaaS Name", "waas_new_feature": "New Feature", "waas_new_plan": "New Plan", "waas_options": "Options", "waas_options_sub_heading": "These options change behaviour of your WaaS", "waas_plan_details": "Plan Details", "waas_plan_name": "Plan Name", "waas_plan_price": "Plan Price", "waas_primary_color": "Primary Color", "waas_proceed_to_payment": "Proceed to payment", "waas_publicly_information": "This information will be displayed publicly so be careful what you share.", "waas_review": "Review", "waas_secondary": "Secondary", "waas_secondary_color": "Secondary Color", "waas_select_server": "Select Servers", "waas_send_email_after_link_generate": "Send email after the unique checkout link is generated", "waas_server_alias": "<PERSON> <PERSON><PERSON>", "waas_settings": "WaaS Settings", "waas_skip_instawp_demo_site_creation": "Skip InstaWP demo site creation", "waas_skip_instawp_switch_label": "When switched off, a demo / trial site will be created on InstaWP before going live.", "waas_skip_steps": "Skip steps for choosing template or hosting", "waas_skip_steps_sub_heading": "If only one hosting or template is available, skip that step in checkout", "waas_statistics": "WaaS Stats", "waas_subscription": "WaaS Subscription", "waas_template_price": "Template Price", "waas_update": ":WaaS is updated successfully", "waas_updated": "WaaS Updated", "waas_use_instawp_payment_in_checkout": "Use InstaWP Payments in WaaS Checkouts", "waas_use_instawp_payment_in_checkout_sub_heading": "Checkout docs on integrating with Surecart and other ecommerce", "waas_use_public_links_or_restrict_unique_link": "Turn this off when 3rd Party checkouts or unique links are in use", "waas_waived_off": "Waived off", "warming_up": "Warming Up", "wc_faker_tip": "We will try to install the latest version of WooCommerce before running Faker", "wc_generator": "WC Generator", "we_have_created_a_copy_of_free_template": "We have created a copy of free template! ", "we_have_created_a_copy_of_your_purchased_template": "We have created a copy of purchased template! ", "we_will_automatically_install_the_migration_plugin_on_both_source_and_destination_websites": "We will automatically install the migration plugin on both source and destination websites.", "we_will_charge_your_card": "We will charge your card", "we_will_check_vulnerabilities_against_core_installed_plugins_and_installed_themes": "We will check vulnerabilities against Core, installed Plugins and installed Themes.", "webhook": "Webhook", "webhook_callback_details": "Webhook Callback Details", "webhook_history": "Webhook History CSV", "webhook_request_type": "Webhook Request Type", "webhook_url": "Webhook URL", "webhook_url_for_provisioning_waas_link": "API call URL for provisioning WaaS Links", "website_url": "Website URL", "wednesday": "Wednesday", "weekday": "Weekday", "weekend_day": "Weekend Day", "weekly": "Weekly", "weeks": "Weeks", "welcome": "Welcome", "what_included": "What’s included", "what_to_update": "What to Update", "white_label": "White Label", "white_label_content": "Are you sure you want to disconnect the suffix domain, old sites may continue working but SSL will not get renewed.", "white_label_not_allow": "You are not allowed to change white label please purchase white label addon.", "white_label_suffix_domain": "White Label/Suffix Domain", "whitelabel_domain": "Whitelabel Domain", "whoops_something_went_wrong": "Whoops! Something went wrong", "widget_deleted": "Widget :count deleted :timestamp", "widget_updated": "Widget :count updated :timestamp", "will_be_applied_upon_checkout": "will be applied upon checkout.", "will_be_deleted_after": "Will be deleted after", "will_be_installed_upon_launch": "will be installed upon launch", "will_be_marked_as_reserved": "will be marked as \"Reserved\" and it will NOT expire or deleted automatically.", "will_be_unmarked_as_reserved": "will be unmarked as \"Reserved\" and expire after a specific time period.", "will_expired": "It will expire", "wizard_not_found": "WaaS not found!", "wizard_package_not_fount": "WaaS Package Not Found!", "wizard_server_not_fount": "WaaS Server Not Found!", "woocommerce": "woocommerce", "wordpress": "WordPress", "wordpress_org": "Wordpress.org", "wordpress_seo_plugin": "Wordpress SEO Plugin", "wordpress_username": "WordPress Username", "wordpress_version": "WordPress Version", "wp_admin": " /wp-admin/", "wp_backup_zip_files": "WordPress Backup Zip Files", "wp_config_data_fetch_success": "WP Config data successfully fetched.", "wp_config_data_save_success": "WP Config data successfully saved.", "wp_config_updated": "WP Config updated.", "wp_content": "wp-content/...", "wp_details": "WP Details", "wp_login_credentials": "WP Login Credentials.", "wp_role": "WP User Role", "wp_tavern": "WP Tavern", "wp_version": "WordPress Version", "wp_version_update_success": "WordPress Version updated successfully", "wpcs_version_not_deployed": "Wpcs Version is not deployed. Please try after sometime.", "wpcs_version_production_set_success": "Version set as production successfully.", "wppass": "Password", "wpuser": "Username", "wrap": "Wrap", "year": "Year", "yearly": "Yearly", "yes": "Yes", "yes_cancel": "Yes, <PERSON>cel", "yes_stop_my_transfer": "Yes, stop my transfer", "you_account_will_be_credited_with": "You account will be credited with", "you_are_about_delete": "You are about to delete", "you_are_about_move": "You are about to move", "you_are_about_to_abort_migration": "You are about to abort migration. This will stop the migration process and you will not be able to continue it later.", "you_are_activating_trial": "You are activating a free trial of InstaWP", "you_are_not_allowed_perform": "You are not allowed to perform this action.", "you_are_step_away_from_creating_new_hosted_site": "You are just a step away from creating a new hosted site, do you to wish to continue?", "you_are_updating_addons_subscriptions": "You are updating addons subscriptions.", "you_are_using_old_version": "You are using old version of InstaWP Connect plugin (v:version). Please update to the latest version.", "you_can_leave_this_page_we_ll_notify_you_via_email_once_your_site_has_been_migrated": "You can leave this page. We'll notify you via email once your site has been migrated", "you_can_leave_this_page_well_notify_you_via_email_once_your_site_has_been_migrated": "You can leave this page. We'll notify you via email once your site has been migrated", "you_can_link_custom_domain": "You can link custom domain", "you_dont_have_any_connected_site": "You don’t have any connected site", "you_dont_have_any_connected_site_with_advanced_plan": "You don’t have any connected site with Advanced plan", "you_dont_have_any_connected_site_with_basic_plan": "You don’t have any connected site with Basic plan", "you_dont_have_any_site": "You don’t have any site", "you_dont_have_any_templates_yet": "You don’t have any templates yet", "you_dont_have_any_snapshots_yet": "You don’t have any snapshots yet", "you_have_created_maximum": "You have created the maximum number of sites allowed under your current plan. To create more sites please upgrade your plan.", "you_have_created_maximum_number": "You have created the maximum number of sites allowed under your current plan. To create more sites please upgrade your plan.", "you_have_joined_the_beta_program": "You have joined the beta program.", "you_have_left_the_beta_program": "You have left the beta program.", "you_have_not_any_account": "Don't have an account", "you_have_not_created_any_templates_start_by_saving_site_as_template_for_future_use": "You have not created any templates. Start by saving a site as a template for future use.", "you_have_not_created_any_snapshots_start_by_saving_site_as_snapshot_for_future_use": "You have not created any snapshots. Start by saving a site as a snapshot for future use.", "you_have_not_enabled_any_integrations": "You have not enabled any Integrations.", "you_have_requested_a_paid_feature": "You have requested a paid feature", "you_have_used_maximum_disk": "You have used :plan_name plan's disk quota (:storage_quota mb). To create more sites please either upgrade your plan or delete existing sites.", "you_have_used_maximum_team_members": "You have used :plan_name plan's team members quota (:quota). To create more team members please either upgrade your plan or delete existing team members.", "you_ll_get": "You’ll Get", "you_need_to_be_on_a_paid_plan_to_buy_an_addon": "You need to be on a paid plan to buy an addon", "you_restore_site_register_account": "You have restored the maximum number of sites allowed under your current plan. To restore more sites please upgrade your plan.", "you_will_be_charged": "You will be charged", "you_will_be_redirected_to_wordpress": "You will be redirected to WordPress where you will be asked to login and then allow the \"Site Import\" application to connect to your account. This is required to migrate your site.", "your_card_details_will_be_used": "We will not charge your card until trial expires, we'll email you before your trial ends.", "your_card_information": "Do not worry; your card information will never directly touch our servers.", "your_card_is_securely_saved_with": "Your card is securely saved with", "your_card_will_be_charged": "Your card will be charged $:price:paymentInterval. Instructions will be sent on email.", "your_cards": "Your Cards", "your_configurations": "Your Configurations", "your_credits_available": "Your credits available", "your_emergency_recovery_codes": "Please confirm access to your account by entering one of your emergency recovery codes.", "your_kyc_has_been_rejected_please_re_try_with_correct_details": "Your KYC has been rejected, please re-try with correct details.", "your_kyc_is_pending_please_provide_your_documents_to_get_it_approved": "Your KYC is Pending, please provide your documents to get it approved.", "your_local_time": "Your Local Time (UTC :timezone)", "your_migration_is": "Your migration is", "your_migration_is_complete": "Your Migration is Complete!", "your_new_wordpress_website_ready": "Your new WordPress website is ready!", "your_new_wp_site_is_now_live": "Your new WordPress website is now live!", "your_payment_is_pending_please_pay_to_make_ads_live": "Your payment is pending, please pay to make Ads live", "your_payout_account_is_now_active": "Your payout account is now active.", "your_plan": "Your Plan", "your_primary_card": "Your Primary Card", "your_provider_site_is_ready": "Your new :provider site is ready!", "your_server_or_account_is_not_properly_configured": "Your server or account is not properly configured", "your_site_is_creating": "Your site is creating...", "your_site_is_ready": "Your Site is Ready", "your_site_remove_server": "Your site has been removed from the server.", "your_site_will_be_created_in_a_moment": "Your site will be created in a moment..", "your_subscription_cancelled": "Your WaaS subscription is cancelled and site is deleted", "your_subscription_will_be_updated_from": "Your subscription will be updated from", "your_website": "Your website", "your_website_is": "Your website is", "your_website_is_connected": "This website can be managed with InstaWP Connect plugin", "your_website_is_inactive": "We have not received heartbeat from the InstaWP Connect plugin", "your_website_is_waiting_to_be_connected": "Your website is waiting to be connected", "zip_file": "Zip File", "install_content": "Plugins Themes", "active_integration": "Active Integration", "by_severity": "By Severity", "all_actions": "All Actions", "all_severity": "All Severity", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "view_logs": "View Logs", "2_way_sync": "2 Way Sync", "safe_update": "Safe Update", "more_actions": "More Actions", "abuse_suspended_description": "This site has been suspended due to abuse. Please contact support if you believe this is an error.", "overuse_suspended_description": "This site has been suspended due to resource overuse. Please delete some sites to restore access.", "download_logs": "Download Logs", "initiated_by": "Initiated by", "mode": "Mode", "progress": "Progress", "track_migration": "Track Migration", "website": "Website", "migration_contact_support_data": "Migration contact support data", "tracking_page_text": "Tracking page text", "migration_began": "Migration began", "var_placed_bw_double_curly_brackets_example": "Variable should be placed between double curly brackets, example", "variable": "Variable", "downloading_templates": "Downloading templates", "cleaning_existing_database": "Cleaning existing database", "restoring_template": "Restoring template", "template_has_been_restore": "Template has been restore", "restoring_database": "Restoring database", "database_has_been_restored": "Database has been restored", "configuring_wordpress": "Configuring WordPress site", "export_to_csv": "Export to CSV", "wordpress_configuration_completed": "WordPress configuration completed", "plugins_have_been_installed": "Plugins have been installed", "commands_executed_successfully": "Commands executed successfully", "cleaning_up_wordpress": "Cleaning up WordPress", "select_items": "Select items", "no_item_available": "No item available", "period": "Period", "per_minute": "per minute", "per_month": "per month", "waas_demo_sites": "WaaS Demo Sites"}