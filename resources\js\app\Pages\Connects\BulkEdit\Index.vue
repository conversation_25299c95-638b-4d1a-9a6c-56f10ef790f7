<template>
  <Head>
    <title>Bulk Edit Connects - {{ setting.brandShortName }}</title>
  </Head>
  <PageHeader
    :title="$t('manage')"
    :route-name="route('connects')"
    :is-inner-page="true"
  >
    <template #left>
      <div class="flex items-center gap-2 break-all text-lg font-medium text-warning-1300">
        {{ appStore.selectAllConnectBulkEdit ? $t('bulk_edit') + ' - ' + $t('all') : $t('bulk_edit') + ' - ' + $tChoice('num_connected_site', appStore.connectBulkSelection.length) }}
      </div>
    </template>
  </PageHeader>
  <BulkEditTable />
</template>

<script>
import PageHeader from "@/app/Components/PageHeader.vue";
import BulkEditTable from '@/app/Pages/Connects/BulkEdit/BulkEditTable.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import { mapState, mapStores } from 'pinia';

export default {
  name: 'ConnectBulkEditIndex',
  components: {
    BulkEditTable,
    PageHeader,
  },
  data() {
    return {
      setting: new Setting(),
    };
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ['connectBulkSelection']),
  },
};
</script>