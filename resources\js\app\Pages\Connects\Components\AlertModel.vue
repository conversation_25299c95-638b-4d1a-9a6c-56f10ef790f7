<template>
  <CommonModal
    :model-value="isAlert"
    size="lg"
    :allow-padding="false"
    extra-footer-class="bg-grayCust-50"
    @close="close()"
  >
    <template #header>
      <div class="text-xl font-semibold text-grayCust-430">
        {{ $t('new_alert') }}
      </div>
    </template>
    <template #content>
      <div class="flex flex-col gap-6 rounded-t-lg bg-white p-6">
        <!-- <div class="h-0.5 w-full rounded-full bg-grayCust-180" /> -->
        <div class="rounded-lg border border-grayCust-180">
          <div class="rounded-t-md bg-grayCust-50 px-4 py-3">
            <h4 class="font-medium text-grayCust-1520">
              {{ $t('trigger') }}
            </h4>
          </div>
          <div class="space-y-3 rounded-b-md px-4 py-3">
            <div class="space-y-1.5">
              <div>
                <label
                  for=""
                  class="text-sm font-medium capitalize text-grayCust-910"
                >{{
                  $t('action') }}</label>
              </div>
              <div>
                <CommonListbox
                  :options="processedActionList"
                  :value="selectedAction"
                  label-key="display_name"
                  value-key="id"
                  return-key="value"
                  placeholder="Select Action type"
                  button-class="w-full"
                  badge-key="severity"
                  badge-text-key="severity"
                  badge-color-key="severityColor"
                  @select="selectedAction = $event"
                />
              </div>
            </div>
            <!-- <div class="space-y-1.5">
                        <div>
                            <label for="" class="text-sm font-medium text-grayCust-910">Severity</label>
                        </div>
                        <div>
                            <Listbox as="div" v-model="selectedSeverity">
                                <div class="relative">
                                    <ListboxButton class="relative w-full py-2 px-3.5 flex items-center justify-between border rounded-lg shadow border-warning-1020 bg-white gap-2 text-sm text-grayCust-700  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-900 cursor-pointer">
                                        <span class="flex items-center">
                                            <span class="text-base font-normal block max-w-[140px] sm2:max-w-[200px] md:max-w-[300px] lg:max-w-[400px] text-grayCust-1740 truncate"
                                                :class="selectedSeverity ? 'text-grayCust-1740' : 'text-grayCust-630'">{{ selectedSeverity ? selectedSeverity.name : 'Select Severity' }}</span>
                                        </span>
                                        <span class="">
                                            <img :src="cdn(`images/ActivityLog/down-arrow-icon.svg`)" class="w-3 h-3" alt=""  />
                                        </span>
                                    </ListboxButton>

                                    <transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
                                        <ListboxOptions class="absolute z-10 p-2 mt-1 w-full max-h-[180px] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm custom-scroll right-0">
                                            <ListboxOption as="template" v-for="item in severityList" :key="item.id" :value="item" v-slot="{ active }">
                                                <li :class="[active ? 'bg-primary-275 text-primary-900' : 'text-grayCust-640 bg-white', 'font-medium rounded-md relative cursor-pointer select-none p-2 truncate']">
                                                    <div class="flex items-start flex-col gap-y-1">
                                                        <div class="max-w-[90%] truncate">{{ item.name }}</div>
                                                    </div>
                                                </li>
                                            </ListboxOption>
                                        </ListboxOptions>
                                    </transition>
                                </div>
                            </Listbox>
                        </div>
                    </div> -->
          </div>
        </div>
        <div class="rounded-lg border border-grayCust-180">
          <div class="flex items-center justify-between rounded-t-md bg-grayCust-50 px-4 py-3">
            <h4 class="font-medium text-grayCust-1520">
              {{ $t('channel') }}
            </h4>
            <button
              v-if="$page.props.TESTING_ENV"
              class="inline-flex items-center justify-center gap-1 text-sm font-medium text-primary-900 focus:outline-none focus:ring-transparent"
            >
              <plus-icon class="size-[18px]" />
              <span>{{ `${$t('add_new')} (${$t('beta')})` }}</span>
            </button>
          </div>
          <div class="space-y-3 rounded-b-md px-4 py-3">
            <div class="space-y-1.5">
              <div>
                <label
                  for=""
                  class="text-sm font-medium text-grayCust-910"
                >{{
                  $t('send_to') }}</label>
              </div>
              <div>
                <CommonListbox
                  :options="processedChannelList"
                  :value="selectedChannel"
                  label-key="display_name"
                  value-key="id"
                  return-key="value"
                  placeholder="Select Channel"
                  button-class="w-full"
                  options-class="w-full"
                  @select="selectedChannel = $event"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3 rounded-b-lg">
        <CButton
          btn-title="Cancel"
          btn-type="gray-outline-btn"
          @click="close()"
        />
        <CButton
          btn-title="Add Alert"
          btn-type="secondary"
          :disabled="!selectedAction || !selectedChannel || loadingAddNew"
          @click="addAlert()"
        />
      </div>
    </template>
  </CommonModal>
</template>
<script>

import CommonListbox from "@/app/Common/CommonListbox.vue";
import CommonModal from "@/app/Common/CommonModal.vue";
// import ChromeIcon from "@/components/UpdatedDesignVersion/ImageComponents/ChromeIcon.vue";
// import PlusIcon from "@/components/UpdatedDesignVersion/ImageComponents/GoLiveNewDesign/PlusIcon.vue";
import { API_SERVER_URL } from '@/const';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { mapStores } from 'pinia';

export default {

  components: {
    CommonListbox,
    CommonModal,
    // PlusIcon,
    // ChromeIcon,
  },
  props: {
    connect: {
      type: Object,
      required: true,
    },
    isAlert: {
      type: Boolean,
      required: false,
    },
    severityList: {
      type: Array,
      required: false,
      default: () => [],
    },
    channelList: {
      type: Array,
      required: true,
    },
    actionList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      selectedAction: null,
      selectedChannel: null,
      loadingAddNew: false
    }
  },
  computed: {
    ...mapStores(useAppStore),
    processedActionList() {
      return this.actionList.map(action => ({
        ...action,
        severityColor: this.getSeverityColor(action.severity)
      }));
    },
    processedChannelList() {
      return this.channelList.map(channel => ({
        ...channel,
        image_url: channel.logo_url // Map logo_url to image_url for CommonListbox
      }));
    }
  },
  methods: {
    getSeverityColor(severity) {
      const severityMap = {
        'critical': 'danger',
        'high': 'warning',
        'medium': 'primary',
        'low': 'secondary'
      };
      return severityMap[severity] || 'secondary';
    },
    close(data) {
      this.$emit('close', data)
      this.selectedAction = null;
      this.selectedChannel = null;
      this.loadingAddNew = false;
    },
    addAlert() {
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      const data = {
        user_id: this.appStore.user.id,
        team_id: this.appStore.user.current_team.id,
        connect_id: this.$props.connect.id,
        wordpress_activity_id: this.selectedAction.id,
        integration_provider_id: this.selectedChannel.id,
        payload: {},
        status: true,
      }
      this.loadingAddNew = true;
      axios.post(API_SERVER_URL + '/api/wordpress-activity-triggers', data, { headers })
        .then(response => {
          const notification = {
            heading: this.$t('success'),
            subHeading: this.$t('alert_rule_created_successfully'),
            type: "success",
          };
          this.appStore.setNotification(notification);
          this.close(response.data.data);
        })
        .catch(() => {

        })
        .finally(() => {
          this.loadingAddNew = false;
        })
    }
  }
};
</script>
<style>
@media (max-height: 600px) {
  .alert-model {
    align-items: start !important;
  }
}
</style>