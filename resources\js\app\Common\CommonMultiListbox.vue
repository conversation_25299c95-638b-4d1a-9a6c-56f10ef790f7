<template>
    <Listbox
      as="div"
      multiple
    >
      <div class="relative">
        <div
          ref="dropdownButton"
          @click="toggleDropdown"
        >
          <ListboxButton
            :class="[buttonStyle, $attrs['btn-extra-class'], 'relative flex w-full cursor-pointer items-center justify-between gap-2 rounded-lg border border-warning-1020 bg-white px-3.5 py-2 text-base text-grayCust-700 shadow-sm transition-shadow focus:border-secondary-800 focus:outline-none focus:ring focus:ring-primary-275']"
          >
            <span
              class="block max-w-full truncate"
              :class="currentSelectedOptions.length ? 'text-grayCust-1740' : 'text-grayCust-630'"
            >
              <div
                v-if="currentSelectedOptions.length"
                class="flex flex-wrap items-center gap-2"
              >
                <div
                  v-for="option in currentSelectedOptions"
                  :key="option.id"
                  :class="pillColor"
                  class="inline-flex items-center justify-center gap-1.5 rounded-full px-2.5 py-0.5 text-sm font-medium"
                >
                  <img
                    v-if="option.iconName"
                    :src="cdn(option.iconName)"
                    class="size-3.5 cursor-pointer rounded-full object-cover"
                    alt=""
                  >
                  <span v-if="option.name">{{ option.name }}</span>
                  <XIcon
                    class="size-3.5 cursor-pointer"
                    @click="removeOption(option)"
                  />
                </div>
              </div>
              <h4 v-if="!currentSelectedOptions.length && btnPlaceholder">{{ btnPlaceholder }}</h4>
            </span>
            <div>
              <component
                :is="iconComponent(btnIconType, btnIconName)"
                aria-hidden="true"
                class="size-4"
              />
            </div>
          </ListboxButton>
        </div>
  
        <transition
          leave-active-class="transition ease-in duration-100"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <div
            v-show="showDropdown"
            ref="dropdownList"
            :class="[dropdownBoxWidth, dropdownPosition, 'absolute z-10 my-1']"
          >
            <ListboxOptions
              :class="[maxHeight, 'custom-scroll w-full overflow-y-auto rounded-lg border border-grayCust-160 bg-white py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm2:text-base']"
            >
              <slot name="header" />
              <ListboxOption
                v-for="item in optionList"
                :key="item.id"
                :value="item"
              >
                <li
                  :class="[currentSelectedOptions.includes(item) ? cellColor : 'text-grayCust-640', [cellOptionSize, 'relative mt-0.5 flex select-none items-center truncate px-3 py-1.5', item.disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer']]"
                  @click="toggleValue(item)"
                >
                  <div v-if="isCheckbox">
                    <Checkbox
                      id="base-checkbox"
                      value="base-checkbox"
                      :checked="currentSelectedOptions.includes(item)"
                      class="mr-4"
                    />
                  </div>
                  <div class="flex w-full items-center justify-between">
                    <div class="flex w-full items-center gap-2">
                      <div class="flex items-center gap-2">
                        <img
                          v-if="item.iconName"
                          :class="cellImageStyle"
                          :src="cdn(item.iconName)"
                          alt=""
                          class="size-5"
                        >
                        <span
                          :class="[currentSelectedOptions.includes(item) ? cellTextColor : 'text-grayCust-640', 'block max-w-[300px] truncate']"
                        >
                          {{ item.name }}
                        </span>
                      </div>
                      <div class="flex items-center gap-2">
                        <slot
                          name="extra-content"
                          :item="item"
                        />
                      </div>
                      <span
                        v-if="currentSelectedOptions.includes(item) && isCheckIcon"
                        class="absolute inset-y-0 right-0 flex items-center pr-4"
                      >
                        <CheckIcon
                          class="size-5"
                          aria-hidden="true"
                        />
                      </span>
                    </div>
                    <div>
                      <span
                        class="text-xs "
                        :class="[currentSelectedOptions.includes(item) ? 'text-primary-900' : 'text-grayCust-910']"
                      >{{
                        item.size }}</span>
                    </div>
                  </div>
                </li>
              </ListboxOption>
            </ListboxOptions>
          </div>
        </transition>
      </div>
    </Listbox>
  </template>
  
  <script>
  
  import Checkbox from '@/components/New/Blocks/Checkboxes/Checkbox.vue';
  import Pill from '@/components/New/Blocks/Pills/Pill.vue';
  import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/vue';
  import * as OutlineIcons from "@heroicons/vue/outline";
  import * as SolidIcons from "@heroicons/vue/solid";
  
  export default {
    name: 'MultiselectListBox',
  
    components: {
      Listbox,
      ListboxButton,
      ListboxOption,
      ListboxOptions,
      SelectorIcon: OutlineIcons.SelectorIcon,
      CheckIcon: SolidIcons.CheckIcon,
      XIcon: OutlineIcons.XIcon,
      Pill,
      Checkbox
    },
    props: {
      btnPlaceholder: {
        type: String,
        required: false,
        default: ''
      },
      btnImage: {
        type: String,
        required: false,
        default: ''
      },
      btnIconName: {
        type: String,
        required: false,
        default: 'ChevronDownIcon'
      },
      btnIconType: {
        type: String,
        required: false,
        default: "outline"
      },
      btnImageType: {
        type: String,
        required: false,
        default: 'default'
      },
      btnSize: {
        type: String,
        default: 'btn-sm',
        required: false
      },
      defaultSelectFirst: {
        type: Boolean,
        required: false,
        default: true
      },
      optionList: {
        type: Array,
        required: false,
        default: () => []
      },
      dropdownBoxWidth: {
        type: String,
        default: 'w-full',
        required: false
      },
      cellHoverColor: {
        type: String,
        default: 'primary',
        required: false
      },
      cellImageShape: {
        type: String,
        default: 'default',
        required: false
      },
      cellImagePosition: {
        type: String,
        default: 'first',
        required: false
      },
      cellSize: {
        type: String,
        default: 'cell-lg',
        required: false
      },
      selectedOptions: {
        type: Array,
        default: () => [],
        required: false
      },
      maxHeight: {
        type: String,
        default: 'max-h-56',
        required: false
      },
      pillColor: {
        type: String,
        default: 'bg-grayCust-280 text-grayCust-660'
      },
      isCheckIcon: {
        type: Boolean,
        default: true
      },
      isCheckbox: {
        type: Boolean,
        default: false
      }
    },
    emits: ['onHandleChange'],
    data() {
      return {
        buttonType: '',
        buttonStyle: '',
        buttonImageType: '',
        cellOptionSize: '',
        cellColor: '',
        cellTextColor: '',
        cellImageStyle: '',
        dropdownPosition: '',
        currentSelectedOptions: [],
        showDropdown: false
      };
    },
    created() {
      this.currentSelectedOptions = [...this.selectedOptions];
      switch (this.btnImageType) {
        case 'default':
          this.buttonImageType = 'min-w-[20px] w-5 h-5';
          break;
        case 'rounded':
          this.buttonImageType = 'min-w-[24px] h-6 rounded-full overflow-hidden';
          break;
        case 'status':
          this.buttonImageType = 'min-w-[12px] w-3 h-3 rounded-full overflow-hidden';
          break;
      }
      switch (this.btnSize) {
        case 'btn-sm':
          this.buttonStyle = 'min-h-[40px]';
          break;
        case 'btn-lg':
          this.buttonStyle = 'min-h-[44px]';
          break;
      }
      switch (this.cellHoverColor) {
        case 'primary':
          this.cellColor = 'bg-primary-275 text-primary-900';
          this.cellTextColor = 'text-primary-900';
          break;
        case 'secondary':
          this.cellColor = 'bg-grayCust-280 text-grayCust-660';
          this.cellTextColor = 'text-grayCust-660';
          break;
      }
      switch (this.cellSize) {
        case 'cell-sm':
          this.cellOptionSize = 'text-sm h-9';
          break;
        case 'cell-lg':
          this.cellOptionSize = 'text-base h-10';
          break;
      }
      switch (this.cellImageShape) {
        case 'default':
          this.cellImageStyle += ' rounded-none';
          break;
        case 'rounded':
          this.cellImageStyle += ' rounded-full';
          break;
      }
      switch (this.cellImagePosition) {
        case 'first':
          this.cellImageStyle += ' order-first';
          break;
        case 'last':
          this.cellImageStyle += ' order-last';
          break;
      }
    },
    methods: {
      toggleDropdown() {
        this.updateDropdownPosition();
        this.showDropdown = !this.showDropdown;
      },
      iconComponent(type, iconName) {
        switch (type) {
          case "solid":
            if (Object.prototype.hasOwnProperty.call(SolidIcons, iconName)) {
              return SolidIcons[iconName];
            } else {
              return null;
            }
          case "outline":
            if (Object.prototype.hasOwnProperty.call(OutlineIcons, iconName)) {
              return OutlineIcons[iconName];
            } else {
              return null;
            }
          default:
            return null;
        }
      },
      toggleValue(option) {
        if (option.disabled) return;
        const index = this.currentSelectedOptions.indexOf(option);
        if (index === -1) {
          this.currentSelectedOptions.push(option);
        } else {
          this.currentSelectedOptions.splice(index, 1);
        }
        this.$emit("onHandleChange", [...this.currentSelectedOptions]);
      },
      removeOption(option) {
        if (option.disabled) return;
        const index = this.currentSelectedOptions.indexOf(option);
        if (index !== -1) {
          this.currentSelectedOptions.splice(index, 1);
        }
        this.$emit("onHandleChange", [...this.currentSelectedOptions]);
      },
      updateDropdownPosition() {
        this.$nextTick(() => {
          const dropdownButton = this.$refs.dropdownButton;
          const dropdownList = this.$refs.dropdownList;
          const dropdownHeight = dropdownList.clientHeight;
          const dropdownButtonHeight = dropdownButton.clientHeight;
          const windowHeight = window.innerHeight;
          const dropdownTop = dropdownButton.getBoundingClientRect().top;
  
          if (windowHeight - dropdownTop - dropdownButtonHeight < dropdownHeight) {
            this.dropdownPosition = "bottom-full top-auto";
          } else {
            this.dropdownPosition = "top-full bottom-auto";
          }
        });
      }
    }
  };
  </script>