<template>
  <Head>
    <title>Manage - {{ setting.brandShortName }}</title>
  </Head>
  <PageHeader :title="$t('manage')">
    <template #right>
      <!-- v-if="isConnectedSiteLoaded && connectedSites?.data?.length > 0" -->
      <div
        class="flex items-center gap-[14px] lg:flex-nowrap flex-wrap"
      >
        <div class="flex items-center gap-[14px] md:flex-nowrap flex-wrap">
          <div class="flex items-center">
            <CommonInput
              id="search"
              v-model="searchParam"
              type="search"
              placeholder="Search"
              image-left="/images/search.svg"
              class="me-2 sm2:flex hidden"
              @input="searchConnect"
            />
            <ConnectProviderComponent
              v-if="connectedSites?.data?.length > 0"
              :preferred-hosting-providers="preferredHostingProviders"
              :plan="planData?.plan"
              @created-site-process="createdSiteProcess"
              @failed-created-site-process="failedCreatedSiteProcess"
              @get-created-site="getCreatedSite($event)"
              @get-latest-connected-site="getConnectedSites"
              @support-click="supportClick"
              @open-generate-report-modal="openGenerateReportModal"
              @generate-report="generateReport"
            />
          </div>
        </div>
      </div>
    </template>
  </PageHeader>
  <connect-managed-live-site
    :connect-live-site="connectLiveSite"
    @close-live-site="closeLiveSite"
  />
  <connected-site-component
    :connected-sites="connectedSites"
    :vulnerability-data="vulnerabilityData"
    :performance-data="performanceData"
    :createdsitedetails="createdsitedetails"
    :is-connected-site-loaded="isConnectedSiteLoaded"
    :is-site-in-process="isSiteInProcess"
    :is-site-process-failed="isSiteProcessFailed"
    :plan-data="planData"
    :placement="'dashboard_page'"
    :is-dashboard-page="true"
    :is-search-box="true"
    :search-param="searchParam"
    @failed-created-site-process="failedCreatedSiteProcess()"
    @get-connected-sites="appStore.refresh_dashboard_counts = !appStore.refresh_dashboard_counts;getConnectedSites($event)"
    @get-site-data="getCreatedSite($event)"
    @site-in-process="siteInProcess()"
    @open-generate-report-modal="openGenerateReportModal"
  />
  <generate-report-modal
    v-model="selectedSites"
    :connected-sites-list="connectedSitesList"
    :is-report-modal="isReportModal"
    :is-generate-report-processing="isGenerateReportProcessing"
    @close-modal="onGenerateReportClose"
    @generate-report="generateReport"
  />
</template>

<script>
import CommonInput from "@/app/Common/CommonInput.vue";
import PageHeader from "@/app/Components/PageHeader.vue";
import ConnectedSiteComponent from "@/app/Pages/Connects/ConnectedSiteComponent.vue";
import ConnectProviderComponent from "@/app/Pages/Connects/ConnectProviderComponent.vue";
import ConnectManagedLiveSite from "@/app/Pages/Connects/ConnectManagedLiveSite.vue";
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from "axios";
import { trans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import { defineAsyncComponent } from "vue";

const GenerateReportModal = defineAsyncComponent(() => import('@/app/Pages/Connects/Components/GenerateReport/Index.vue'));

export default {
  name: "ConnectsIndex",
  components: {
    ConnectProviderComponent,
    GenerateReportModal,
    ConnectManagedLiveSite,
    PageHeader,
    ConnectedSiteComponent,
    CommonInput
  },
  props: {
    vulnerabilityData: {
      type: Object,
      required: true
    },
    performanceData: {
      type: Object,
      required: true
    },
    planData: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      connectedSites: null,
      createdsitedetails: [],
      current_childitemid: '',
      isshow_current_childitemid: false,
      isSiteInProcess: false,
      isSiteProcessFailed: false,
      processcpmolated: 0,
      getRestorStatusTimeOut: null,
      isConnectedSiteLoaded: false,
      connectLiveSite: false,
      setting: new Setting(),
      searchParam: '',
      isReportModal: false,
      selectedSites: [],
      isGenerateReportProcessing: false,
    }
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ['preferredHostingProviders', 'connectBulkSelection']),
    connectedSitesList() {
      if (!this.connectedSites) return [];
      return this.connectedSites.data.filter(site => site.connect_status == 'Connected').map(site => {
        site.name = site.label ?? site.site_title ?? site.url?.replace(/^(https?:\/\/)/, '');
        site.disabled = !(site.advanced && site.plan_features.client_reporting);
        return site;
      });
    }
  },
  mounted() {
    this.appStore.getHostingProviders();
    this.getConnectedSites();
  },
  unmounted() {
    clearTimeout(this.getRestorStatusTimeOut)
  },
  methods: {
    searchConnect() {
      this.getConnectedSites();
    },
    getConnectedSites(params) {
      let connectParams = {
        page: 1,
        per_page: 10,
        ...params,
      }
      if (this.searchParam) {
        connectParams.search = this.searchParam;
      }
      this.isConnectedSiteLoaded = false;
      axios
        .get("/api/v1/get-connected-sites", {
          params: connectParams
        })
        .then((response) => {
          this.connectedSites = response.data.data.data;
        })
        .catch((error) => {
          let message = {
            heading: trans('failed'),
            subHeading: error?.response?.data.message || error.message,
            type: "error",
          };
          this.appStore.setNotification(message);
        })
        .finally(() => {
          this.isConnectedSiteLoaded = true;
        });
    },
    Restore(connect_id) {
      let that = this;
      that.process = true;
      that.current_childitemid = connect_id;
      that.isshow_current_childitemid = true;
      axios
        .get("/api/v1/insta-wp-restore/" + connect_id)
        .then((response) => {

          if (response.data.status) {
            // let message = {
            //     heading: "Success",
            //     subHeading: 'Restore Initiated',
            //     type: "success",
            // };
            // that.appStore.setNotification(message);
            // that.getRestorStatus({'task_id':response.data.data.restore_initiated.task_id,'connect_id':response.data.data.restore_initiated.connect_id,'destination_connect_id':connect_id,'site_id':response.data.data.site_id});
            let message = {
              heading: that.$t('success'),
              subHeading: that.$t('restore_initiated'),
              type: "success",
            };
            that.getConnectedSites();
            that.appStore.setNotification(message);
          }

          if (response.data.status == 0) {
            let message = {
              heading: that.$t('error'),
              subHeading: response.data.data,
              type: "error",
            };
            that.appStore.setNotification(message);
          }
        })
        .catch((error) => {
          let message = {
            heading: that.$t('failed'),
            subHeading: error?.response?.data.message || error.message,
            type: "error",
          };
          that.appStore.setNotification(message);
        })
        .finally(() => {

        });
    },
    getRestorStatus(data, count = 1) {
      let that = this;
      that.process = true;

      axios
        .post("/api/v1/connects/get_restore_status", data)
        .then((response) => {

          that.processcpmolated = response.data.data.progress;
          if (count == 20 || count > 20) {
            let message = {
              heading: that.$t('info'),
              subHeading: that.$t('process_will_take_while_please_check_again_after_few_minutes'),
              type: "info",
            }
            that.appStore.setNotification(message);
            that.isshow_current_childitemid = false;
            that.current_childitemid = '';
          }

          if (response.data.data.status) {
            if (response.data.data.progress == "100") {
              let message = {
                heading: that.$t('success'),
                subHeading: that.$t('restore_initiated'),
                type: "success",
              };
              that.getConnectedSites();
              that.appStore.setNotification(message);
              that.isshow_current_childitemid = false;
              that.current_childitemid = '';
            } else if (count < 20) {
              that.getRestorStatusTimeOut = setTimeout(() => {
                that.getRestorStatus(data, count + 1);
              }, 30000);

            }

          }

        })
        .catch((error) => {
          let message = {
            heading: that.$t('failed'),
            subHeading: error?.response?.data.message || error.message,
            type: "error",
          };
          that.appStore.setNotification(message);
        })
        .finally(() => {

        });
    },
    getCreatedSite(data) {
      this.createdsitedetails = data;
    },
    createdSiteProcess() {
      this.isSiteInProcess = true;
    },
    siteInProcess() {
      this.isSiteInProcess = false;
    },
    failedCreatedSiteProcess() {
      this.isSiteInProcess = false;
      this.isSiteProcessFailed = true;
    },
    supportClick() {
      this.connectLiveSite = true
    },
    closeLiveSite() {
      this.connectLiveSite = false
    },
    openGenerateReportModal(item) {
      this.isReportModal = true;
      if (item) {
        this.selectedSites = [item];
      } else {
        this.selectedSites = this.connectedSitesList.filter(site => this.connectBulkSelection.includes(site.id) && !site.disabled);
      }
    },
    generateReport({ inclusions, customerEmails, reportingPeriod }) {
      if (customerEmails && customerEmails.length > 14) {
        let message = {
          heading: trans('failed'),
          subHeading: trans('number_of_emails_should_be_less_than_14'),
          type: "error",
        };
        this.appStore.setNotification(message);
        return;
      }
      let payload = {
        connect_ids: this.selectedSites.map(connect => connect.id),
        inclusions: inclusions,
        customerEmails: customerEmails,
        reportingPeriod: reportingPeriod,
      }
      this.isGenerateReportProcessing = true;
      axios
        .post("/api/v2/connects/generate-report", payload)
        .then(() => {
          this.isReportModal = false;
          let message = {
            heading: trans('success'),
            subHeading: trans('generating_report_we_will_send_you_an_email'),
            type: "success",
          };
          this.appStore.setNotification(message);
        })
        .catch((error) => {
          let message = {
            heading: trans('failed'),
            subHeading: error?.response?.data.message || error.message,
            type: "error",
          };
          this.appStore.setNotification(message);
        }).finally(() => {
          this.isGenerateReportProcessing = false;
        });
    },
    onGenerateReportClose() {
      this.isReportModal = false;
      this.selectedSites = [];
    },
  }
};
</script>


<style scoped>
.custom-scrollbar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
  background-color: #f5f5f5;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #F5F5F5;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #bebebe;
}


.check-wrapper:hover .check-wrapper-img{
  display: none !important;
}
.check-wrapper:hover .check-wrapper-checkbox{
  display: block !important;
}
</style>