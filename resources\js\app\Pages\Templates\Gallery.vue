<template>
  <div>
    <Head>
      <title>Gallery - {{ setting.brandShortName }}</title>
    </Head>
    <TemplateLayout 
      :template="template" 
      title="Gallery"
      sub-title="Add gallery images in a 4:3 ratio for your template/demo landing page"
    > 
      <div class="">
        <div class="p-4 card-main">
          <div class="logo-width">
            <div class="mb-6">
              <div
                class="relative block rounded-xl border-2 border-dashed border-grayCust-460 bg-grayCust-100 p-4 pt-12"
                @drop="drop"
                @dragover="dragover"
                @dragleave="dragleave"
              >
                <div class="mb-12 text-center ">
                  <input
                    id="image"
                    ref="photoInput"
                    type="file"
                    name="image"
                    accept="image/*"
                    :disabled="gallery.length >= 4"
                    @click="inputClick"
                    @change="newPickFile($event)"
                  >
                  <div class=" mb-3 text-base font-normal text-grayCust-470">
                    Drag and drop gallery images here.
                  </div>
                  <button
                    class="relative inline-flex items-center justify-center rounded-lg bg-secondary-900 px-3 py-2 text-sm font-medium text-white shadow-sm focus:ring-2 focus:ring-secondary-900 focus:ring-offset-2"
                  >
                    <input
                      id="image"
                      ref="photoInput"
                      name="image"
                      class="input-file"
                      type="file"
                      accept="image/*"
                      :disabled="gallery.length >= 4"
                      @change="newPickFile($event)"
                      @click="$event.target.value = ''"
                    >
                    <img
                      :src="cdn(`images/cloud-upload.svg`)"
                      class="mr-2"
                      alt=""
                    >
                    Upload Here
                  </button>
                </div>
                <div class="mt-2 flex flex-wrap items-start justify-center lg:justify-start gap-3">
                  <div
                    v-for="(item, index) in gallery"
                    :key="index"
                    class="image-hover relative"
                    style="width: 140px; height: 113px;"
                  >
                    <img
                      v-if="item.url"
                      :class="previewImage == item.url ? 'border-secondary-900' : 'border-grayCust-310'"
                      class="relative cursor-pointer select-none rounded-xl border-2 object-cover object-center"
                      style="width: 140px; height: 113px;"
                      :src="item.url"
                      alt="cover-image"
                      srcset=""
                      @click="imageClick(item)"
                    >
                    <div
                      v-if="item.isLoading"
                      style="width: 95px; height: 90px;right: 25px;top: 13px"
                      class="absolute top-0 flex justify-center"
                    >
                      <svg
                        class="text-gary flex size-5 animate-spin self-center"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        />
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                    </div>
                    <div
                      v-if="!item.isLoading && !item.cover"
                      @click="makeCover(item)"
                    >
                      <div
                        v-tooltip="'Set as cover image'"
                        class="absolute size-4 cursor-pointer rounded-full border border-grayCust-390 bg-white data-v-tooltip"
                        style="top: -106px;right: -84%;"
                      />
                    </div>

                    <div
                      v-if="item.cover"
                      class="select-cover absolute cursor-pointer rounded-full bg-white"
                      style="top: 5%; right: 5%"
                    >
                      <div v-tooltip="'Set as cover image'">
                        <SolidCheckCircleIcon class="text-secondary-900 h-4 w-4" />
                      </div>
                    </div>

                    <!-- delete image -->
                    <div
                      v-if="!item.isLoading"
                      class="delete-icon absolute flex size-9 cursor-pointer items-center justify-center rounded-full"
                      style="top: 50%; left: 50%;transform: translate(-50%,-50%);background: rgba(0, 0, 0, 0.30);backdrop-filter: blur(1px)"
                      @click="confirmingGalleryDeletion = true; selectGalleryForDeletion = item"
                    >
                      <OutlineTrashIcon class="text-white h-5 w-5" />
                    </div>
                  </div>

                  <label
                    id="image-label"
                    for="image"
                    :class="[gallery.length >= 4 ? 'cursor-not-allowed' : 'cursor-pointer', showDropBorder ? 'dragged-over' : '']"
                    class="relative  flex cursor-pointer items-center justify-center rounded-xl border-2 border-dashed border-grayCust-350 bg-white text-center"
                    style="width: 143px; height: 113px;"
                  >
                    <div class="text-base font-normal text-grayCust-460 ">
                      <svg
                        width="25"
                        height="25"
                        class="mx-auto mb-0.5"
                        viewBox="0 0 25 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4.5 16.5L9.08579 11.9142C9.86683 11.1332 11.1332 11.1332 11.9142 11.9142L16.5 16.5M14.5 14.5L16.0858 12.9142C16.8668 12.1332 18.1332 12.1332 18.9142 12.9142L20.5 14.5M14.5 8.5H14.51M6.5 20.5H18.5C19.6046 20.5 20.5 19.6046 20.5 18.5V6.5C20.5 5.39543 19.6046 4.5 18.5 4.5H6.5C5.39543 4.5 4.5 5.39543 4.5 6.5V18.5C4.5 19.6046 5.39543 20.5 6.5 20.5Z"
                          stroke="#9CA3AF"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                      Upload Image
                    </div>
                    <input
                      id="image"
                      ref="photoInput"
                      name="image"
                      type="file"
                      accept="image/*"
                      class="input-file"
                      :disabled="gallery.length >= 4"
                      @change="newPickFile($event)"
                      @click="$event.target.value = ''"
                    >
                  </label>
                </div>
              </div>
            </div>
            <div class="rounded-2xl border border-primary-260 bg-warning-1170 p-4 space-y-4">
              <div class="flex items-center text-sm font-semibold text-primary-900">
                <img
                  :src="cdn(`images/Profile/Partner/eye.svg`)"
                  class="mr-1"
                  alt=""
                >
                Preview
              </div>
              <div>
                <img
                  class="h-auto w-full"
                  :src="previewImage"
                  style="border-radius: 9.598px;"
                  alt=""
                >
              </div>
            </div>
          </div>
          <CommonModal
            v-model="confirmingGalleryDeletion"
            position="top"
            extra-footer-class="bg-gray-100"
          >
            <template #header>
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t('remove_gallery_image') }}
              </h3>
            </template>

            <template #content>
              <div class="flex items-center gap-3">
                <div class="bg-red-100 mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-8 sm:w-8">
                  <OutlineExclamationIcon class="h-5 w-5 text-red-600" />
                </div>
                <p class="text-sm font-medium text-gray-700">
                  {{ $t('sure_remove_gallery_image') }}
                </p>
              </div>
            </template>

            <template #footer>
              <div class="flex justify-end gap-3">
                <CommonButton
                  :btn-title="$t('cancel')"
                  btn-type="gray-outline-btn"
                  @click="confirmingGalleryDeletion = false"
                />
                <CommonButton
                  :btn-title="$t('delete')"
                  btn-type="danger"
                  @click="deleteGallery(selectGalleryForDeletion)"
                />
              </div>
            </template>
          </CommonModal>
        </div>
      </div>
    </TemplateLayout>
  </div>
</template>

<script>
import CommonButton from "@/app/Common/CommonButton.vue";
import CommonModal from "@/app/Common/CommonModal.vue";
import { GALLERY_IMAGE_SIZE } from "@/const.js";
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import TemplateLayout from "./TemplateLayout.vue";

export default {
  name: "GalleryPage",
  components: {
    TemplateLayout,
    CommonModal,
    CommonButton,
  },
  props: {
    template: {
      required: true,
      type: Object
    }
  },
  data() {
    return {
      setting: new Setting(),
      activeTabName: "gallery",
      isImageShow: false,
      previewImage: "",
      privacyText: '',
      emailBody: '',
      fileName: "",
      imageError: [],
      logo: null,
      images: [],
      SelectCover: Number,
      gallery: [],
      confirmingGalleryDeletion: false,
      selectGalleryForDeletion: null,
      galleyImageMaxSize: GALLERY_IMAGE_SIZE,
      serverErrors: [],
      processing: false,
      form: [],
      isPublic: this.template.mark_as_public,
      showDropBorder: false
    }
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ["user", "teamAllow", "teamCan", "teamUsed", "featureAvailableFrom"]),
    disableSubmit() {
      if (this.processing) {
        return true
      } else {
        return false
      }
    }
  },
  created() {
    if (this.template) {
      const template = this.template
      this.form = {
        id: template.id,
        name: template.name,
        slug: template.slug,
        mark_as_public: template.mark_as_public ? true : false,
        active: template.active,
        custom_email_subject: template.custom_email_subject,
        custom_email_body: template.custom_email_body,
        custom_privacy_text: template.custom_privacy_text,
        custom_css: template.custom_css,
        custom_primary_color: template.custom_primary_color ? template.custom_primary_color : '#15b881',
        custom_accent_color: template.custom_accent_color ? template.custom_accent_color : '#ffffff',
        logo: null,
        logo_file_name: template.logo,
        gallery: template.gallery ?? [],
      }
      this.previewImage = template.gallery[0]?.url
      this.logo = template.logo;
      this.gallery = this.form.gallery;
    }
  },
  methods: {
    imageClick(item) {
      this.previewImage = item.url
    },
    onFileChange(e) {
      const file = e.target.files[0];
      this.logo = URL.createObjectURL(file);
    },
    isSelectCover(item) {
      this.SelectCover = item;
    },
    remove(index) {
      this.images.splice(index, 1);
      this.SelectCover = this.SelectCover - 1;
    },
    newPickFile(e) {
      const file = e.target.files[0];
      const fileType = file.type;
      // Check file type
      if (fileType === 'image/svg+xml') {
        this.appStore.setNotification({
          heading: trans('invalid_file'),
          subHeading: trans('svg_not_allowed'),
          type: 'error',
        });
        return false;
      }
      
      // Check file size
      const fileSizeKB = parseFloat(file.size / 1024).toFixed(2);
      const maxSizeMB = this.galleyImageMaxSize / 1024;
      
      if (fileSizeKB > parseInt(this.galleyImageMaxSize)) {
        this.appStore.setNotification({
          heading: trans('invalid_file'),
          subHeading: trans('file_size_should_be_less_than', { size: maxSizeMB }),
          type: 'error',
        });
        return false;
      }
      // Upload the valid file
      this.uploadGalleyItem(file, this.gallery.length);
    },
    pickFile(e) {
      const file = e.target.files[0];
      this.form.logo = file;
      this.logo = URL.createObjectURL(file);
      this.fileName = file.name;
    },
    dragover(event) {
      if (!event.currentTarget.classList.contains('bg-primary-50')) {
        event.currentTarget.classList.remove('bg-grayCust-100');
        event.currentTarget.classList.add('bg-primary-50');
      }
    },
    dragleave(event) {
      event.currentTarget.classList.add('bg-grayCust-100');
      event.currentTarget.classList.remove('bg-primary-50');
    },
    drop(event) {
      event.currentTarget.classList.add('bg-grayCust-100');
      event.currentTarget.classList.remove('bg-primary-50');
    },
    inputClick(event) {
      event.preventDefault();
    },
    removeFile() {
      this.$refs.photoInput.value = this.fileName = this.logo = this.form.logo = null;
    },
    removeExisting() {
      this.form.logo_file_name = false;
      this.removeFile();
    },
    uploadGalleyItem(file, index) {
      this.gallery.push({
        isLoading: true
      });
      const formData = new FormData();
      formData.append('resource_id', this.form.id);
      formData.append('resource_type', 'template');
      formData.append('gallery[0][order]', index + 1);
      formData.append('gallery[0][type]', 'image');
      formData.append('gallery[0][file]', file);
      axios.post('/api/v2/galleries', formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        }
      }).then((response) => {
        const obj = response.data.data[0];
        obj.isLoading = false;
        this.gallery[index] = obj;
        this.previewImage = this.gallery[index]?.url

      }).catch(() => {
        this.gallery.splice(index, 1);
      })
    },
    makeCover(gallery) {
      gallery.isLoading = true;
      axios.post(`/api/v2/galleries/${gallery.id}/cover`)
        .then(() => {
          this.gallery.forEach(i => i.cover = false);
          gallery.cover = true;
        }).catch(() => {
        }).finally(() => {
          gallery.isLoading = false;
        })
    },
    deleteGallery(gallery) {

      this.selectGalleryForDeletion = null;
      this.confirmingGalleryDeletion = false;
      gallery.isLoading = true;
      axios.delete(`/api/v2/galleries/${gallery.id}`)
        .then(() => {
          const index = this.gallery.findIndex(i => i.id == gallery.id);
          if (index != -1) {
            this.gallery.splice(index, 1);
          }
          if (gallery.cover && this.gallery.length > 0) {
            this.gallery.sort((a, b) => a.id < b.id)[0].cover = true
          }
          this.previewImage = this.gallery[this.gallery.length - 1]?.url
        }).catch(() => {
          gallery.isLoading = false;
        })
    },

    changeStatus(status) {
      this.isPublic = status;
      this.$inertia.visit(this.$route("template.edit.setup", this.template));
    },
    onDragOver() {
      this.showDropBorder = true
    },
    onDragLeave() {
      this.showDropBorder = false
    },
  }
};
</script>

<style scoped>
/* .set-cover {
    display: none;
} */

#image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  display: block;
}

#image-label {
  position: relative;
}

#image-label.dragged-over {
  border: 2px solid #11BF85;
}

/* .image-hover:hover .set-cover {
    display: flex;
    border: 2px solid #11BF85;
} */

.fav-width {
  width: 900px;
}

.card-shadow {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
}

.input-file::-webkit-file-upload-button {
  cursor: pointer !important;
}

.input-file {
  cursor: pointer !important;
}

.data-v-tooltip::before {
  bottom: 16px !important;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  border-color: #fff transparent transparent transparent !important;

}

.data-v-tooltip::after {
  background-color: #fff !important;
  color: #000 !important;
  font-size: 10px !important;
  font-weight: 500 !important;
}

#image1 {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  display: block;
}

.image-hover:hover .delete-icon {
  opacity: 1;
}

.delete-icon {
  opacity: 0;
  transition: all 0.3s;
}

@media screen and (max-width:991px) {
  .fav-width {
    width: 100%;
  }
}
</style>