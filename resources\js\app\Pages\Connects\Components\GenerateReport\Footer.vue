<template>
  <div class="px-6 py-5 flex flex-col flex-wrap sm2:flex-row md:items-center justify-between gap-4 bg-grayCust-50 border-t border-grayCust-180">
    <CButton
      :btn-title="$t('cancel')"
      btn-type="gray-outline-btn"
      size="btn-md"
      class="w-fit"
      @click="closeModal"
    />
    <div class="flex gap-4">
      <!-- <CButton
        btn-title="Preview"
        btn-type="gray-outline-btn"
        icon-name="EyeIcon"
        size="btn-md"
      /> -->
      <CButton
        :disabled="isGenerateReportProcessing"
        :btn-title="$t('generate')"
        btn-type="secondary"
        size="btn-md"
        @click="generateReport"
      />
    </div>
  </div>
</template>

<script>

export default {
  name: "GenerateReportFooter",
  props: {
    isGenerateReportProcessing: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['closeModal', 'generateReport'],
  methods: {
    closeModal() {
      this.$emit('closeModal')
    },
    generateReport() {
      this.$emit('generateReport')
    }
  }
}
</script>