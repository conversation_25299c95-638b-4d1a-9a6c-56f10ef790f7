<template>
  <Head>
    <title>View Logs - {{ setting.brandShortName }}</title>
  </Head>
  <ConnectLayout
    :connect="$props.connect"
    :title="$t('activity_logs')"
    :sub-title="$t('view_logs_description')"
  >
    <template #right>
      <div
        v-if="isActivityLogsEnabled"
        class="flex items-center gap-4 justify-end"
      >
        <div class="flex items-center gap-2">
          <CommonSwitch
            v-model="isActivityLogsEnabled"
            v-tooltip="{ text: $t('switch_off_activity_logs'), position: 'left' }"
          />
          <!-- <span class="text-sm font-medium text-grayCust-700">
              {{ isActivityLogsEnabled ? $t('enabled') : $t('disabled') }}
            </span> -->
        </div>
      </div>
    </template>
    <div
      v-if="!isActivityLogsEnabled"
      class="min-h-[400px] flex flex-col justify-center card-main"
    >
      <div class="flex justify-center items-center h-[99px] w-[99px] mx-auto">
        <img
          v-lazy="cdn('images/ActivityLog/searching.gif')"
          class="w-full h-full"
          alt=""
        >
      </div>
      <div
        class="flex justify-center mt-6 items-center gap-2 py-3 px-4 rounded-lg border border-secondary-900 bg-warning-1170 w-fit mx-auto"
      >
        <CommonSwitch
          v-model="isActivityLogsEnabled"
          :disabled="isSwitchDisabled"
          @open-disabled-modal="showDisabledNotice"
        />
        <span class="text-sm font-medium text-grayCust-700">
          {{ $t('enable_activity_logs') }}
        </span>
      </div>
      <div class="flex flex-col items-center mt-3 space-y-2">
        <span class="text-sm font-medium text-grayCust-660 text-center">
          {{ $t('enable_activity_logs_description') }}
        </span>
        <span class="text-sm font-medium text-grayCust-660 text-center">
          {{ $t('do_you_wish_to_enable_it') }}
        </span>
      </div>
    </div>
    <InitialLoadingState
      v-else-if="initialLoading"
      class="py-24 card-main"
    />
    <div
      v-else
      class="space-y-6"
    >
      <div class="flex w-full flex-wrap items-center justify-between gap-2 card-main p-4">
        <div class="flex items-center gap-2">
          <IconButton
            v-tooltip="$t('refresh')"
            icon-name="OutlineRefreshIcon"
            btn-type="gray-outline-btn"
            @click="getViewLogs"
          />
          <IconButton
            v-if="viewLogsList.length > 0"
            v-tooltip="$t('delete')"
            :disabled="selectedLogs.length == 0"
            icon-name="OutlineTrashIcon"
            btn-type="danger-outline-btn"
            @click="showDeleteLogConfirmation(null)"
          />
        </div>
        <div class="flex flex-wrap items-center gap-3">
          <CommonListbox
            v-model:value="selectedSeverity"
            :options="severityList"
            :placeholder="$t('by_severity')"
            :label="$t('by_severity')"
            :label-key="'display_name'"
            :value-key="'name'"
            :return-key="'name'"
            :options-extra-class="getSeverityClass"
          />
          <CommonListbox
            v-model:value="selectedAction"
            :options="activityListData"
            :placeholder="$t('all_actions')"
            :label="$t('all_actions')"
            :label-key="'display_name'"
            :value-key="'id'"
            :return-key="'id'"
            :options-class="'w-52'"
            :button-class="'w-52'"
          />
        </div>
      </div>
      <EmptyState
        v-if="viewLogsList.length == 0"
        :title="$t('no_activity_logs')"
        :sub-title="$t('no_activity_logs_description')"
        class="py-10 card-main"
      >
        <template #mainIcon>
          <svg
            class="mx-auto size-12 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
            />
          </svg>
        </template>
      </EmptyState>
      <div
        v-else
        class="w-full"
      >
        <CTable
          :fields="tableHeaderData"
          :items="{ data: viewLogsList }"
          :responsive="true"
          :select-table="true"
          :busy="initialLoading"
          row-bordered
          row-height="lg:h-[40px]"
          header-height="lg:h-10"
          bordered
          border-rounded
          @selection-changed="handleSelectionChange"
        >
          <template #cell_action_type="{ item }">
            <div class="flex flex-col items-start gap-1">
              <div v-tooltip="item.activity?.display_name">
                <h4
                  class="max-w-[160px] cursor-pointer truncate text-sm font-semibold text-grayCust-980 sm2:max-w-[340px]"
                >
                  {{ item.activity?.display_name + ' : ' + item.object_name }}
                </h4>
              </div>
              <div class="flex flex-wrap items-center gap-1.5">
                <div
                  v-if="item.wp_user_name"
                  v-tooltip="item.wp_user_name"
                >
                  <p
                    class="max-w-[160px] cursor-pointer truncate text-xs text-grayCust-910 md:max-w-[250px] lg:max-w-[180px]"
                  >
                    {{ item.wp_user_name }}
                  </p>
                </div>
                <span
                  v-if="item.wp_user_name"
                  class="hidden size-0.5 rounded-full bg-grayCust-910 sm2:block"
                />
                <div v-tooltip="item.timestamp">
                  <p
                    class="max-w-[160px] cursor-pointer truncate text-xs text-grayCust-910 md:max-w-[250px] lg:max-w-[145px]"
                  >
                    {{ dateTimeHumanize(item.timestamp) }}
                  </p>
                </div>
              </div>
            </div>
          </template>
          <template #cell_severity="{ item }">
            <div class="flex items-center justify-start gap-2">
              <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
                {{ $t('severity') }}:
              </p>
              <div>
                <label
                  class="rounded-full px-3 py-0.5 text-xs font-medium capitalize"
                  :class="item.activity.severity === 'critical' ? 'bg-redCust-110 text-pinkCust-150' : item.activity.severity === 'high' ? 'bg-warning-350 text-warning-650' : item.activity.severity === 'medium' ? 'bg-primary-350 text-primary-400' : 'bg-grayCust-160 text-grayCust-800'"
                >
                  {{ item.activity.severity }}
                </label>
              </div>
            </div>
          </template>
          <template #cell_action="{ item }">
            <CommonActionButtonGroup>
              <a
                v-if="getLink(item)"
                :href="getLink(item)"
                target="_blank"
              >
                <IconButton
                  v-tooltip="$t('view_more')"
                  icon-name="OutlineExternalLinkIcon"
                  btn-type="gray-outline-btn"
                  size="btn-md"
                />
              </a>
              <IconButton
                v-tooltip="$t('delete')"
                icon-name="OutlineTrashIcon"
                btn-type="danger-outline-btn"
                icon-extra-class="text-redCust-730"
                @click="showDeleteLogConfirmation(item.id)"
              />
            </CommonActionButtonGroup>
          </template>
        </CTable>
      </div>
      <CommonPagination
        v-if="!initialLoading && viewLogsList.length > 0 && pagination.lastPage > 1"
        v-model:current-page="pagination.currentPage"
        v-model:per-page="pagination.perPage"
        :total="pagination.total"
        :resource-name="$t('activity_logs')"
        @page-change="getViewLogs"
        @per-page-change="handlePerPageChange"
      />
    </div>
  </ConnectLayout>
  <CommonModal
    :model-value="confirmingLogDeletion"
    size="md"
    :allow-padding="false"
    extra-footer-class="bg-grayCust-50"
    @close="confirmingLogDeletion = false"
  >
    <template #header>
      <h3 class="text-lg font-medium text-gray-900">
        {{ $t('delete_activity_log') }}
      </h3>
    </template>
    <template #content>
      <div class="p-6">
        <span class="text-sm text-gray-600">
          {{ $tChoice('content_delete_activity_log_count', (deleteLogAction == 'bulk' ? getSelectedLogCount : 1)) }}
        </span>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end space-x-3">
        <CButton
          :btn-title="$t('cancel')"
          btn-type="gray-outline-btn"
          @click="confirmingLogDeletion = false"
        />
        <CButton
          :btn-title="$t('delete')"
          btn-type="danger"
          :disabled="deleteLogProcessing"
          @click="deleteAction"
        />
      </div>
    </template>
  </CommonModal>
  <advance-connect-site-modal
    :is-open="showAdvanceConnectModal"
    :plan-data="$props.planData"
    :connect="$props.connect"
    @refresh-connects="handleRefreshConnects"
    @close-modal="showAdvanceConnectModal = false"
  />
</template>

<script>
import CommonModal from "@/app/Common/CommonModal.vue";
import CommonPagination from "@/app/Common/CommonPagination.vue";
import EmptyState from '@/app/Pages/Connects/Components/EmptyState.vue';
import ConnectLayout from '@/app/Pages/Connects/ConnectLayout.vue';
import InitialLoadingState from '@/components/ConnectEdit/InitialLoadingState.vue';
import { API_SERVER_URL } from '@/const';
import { dateTimeHumanize } from '@/helpers.js';
import JetDangerButton from '@/Jetstream/DangerButton.vue';
import JetSecondaryButton from '@/Jetstream/SecondaryButton.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans, wTrans } from 'laravel-vue-i18n';
import { mapStores } from 'pinia';
import { defineAsyncComponent } from 'vue';

const AdvanceConnectSiteModal = defineAsyncComponent(() => import('@/components/Connect/AdvanceConnectSiteModal/Index.vue'));

export default {
  name: "ConnectsViewLogs",
  components: {
    AdvanceConnectSiteModal,
    ConnectLayout,
    InitialLoadingState,
    EmptyState,
    CommonModal,
    JetSecondaryButton,
    JetDangerButton,
    CommonPagination,
  },
  props: {
    connect: {
      type: Object,
      required: true,
    },
    pluginAndThemeUpdateAvailableCount: {
      type: Object,
      required: true
    },
    planData: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      setting: new Setting(),
      activeTabName: "ViewLogs",
      isAdvanced: this.$props.connect.advanced ?? false,
      isActivityLogsEnabled: this.$props.connect.activity_logs ?? false,
      watchActivityLogsEnabled: true,
      showAdvanceConnectModal: false,
      viewLogsList: [],
      tableHeaderData: [
        {
          key: 'action_type',
          label: wTrans('action_type'),
          headerClass: 'w-1/2'
        },
        {
          key: 'severity',
          label: wTrans('severity')
        },
        {
          key: 'action',
          label: wTrans('action')
        },
      ],
      severityList: [
        {
          id: 0,
          name: 'all_severity',
          display_name: wTrans('all_severity'),
        },
        {
          id: 1,
          name: 'critical',
          display_name: wTrans('critical'),
        },
        {
          id: 2,
          name: 'high',
          display_name: wTrans('high'),
        },
        {
          id: 3,
          name: 'medium',
          display_name: wTrans('medium'),
        },
        {
          id: 4,
          name: 'low',
          display_name: wTrans('low'),
        },
      ],
      pagination: {
        perPage: '10', // String to match with dropdown values
        currentPage: 1,
        lastPage: 0,
        total: 0
      },
      selectedBaseOption: {},
      baseList: [
        {
          id: 1,
          name: '5',
        },
        {
          id: 2,
          name: '10',
        },
        {
          id: 3,
          name: '15',
        },
      ],
      allActivities: [],
      initialLoading: true,
      deleteLogProcessing: false,
      confirmingLogDeletion: false,
      deleteLogId: null,
      deleteLogAction: null,
      selectedSeverity: 'all_severity',
      selectedAction: 1,
      selectedLogs: [],
    }
  },
  computed: {
    ...mapStores(useAppStore),
    getSelectedLogCount() {
      return this.viewLogsList.filter(log => log.isSelected).length;
    },
    isActivityLogsEnabledForPlan() {
      return this.$props.connect.plan_features.activity_logs ?? false;
    },
    isSwitchDisabled() {
      return !this.isAdvanced || !this.isActivityLogsEnabledForPlan;
    },
    activityListData() {
      let activityList = this.allActivities;
      if (this.selectedSeverity != 'all_severity') {
        activityList = activityList.filter(activity => ['none', this.selectedSeverity].includes(activity.severity));
      }
      return activityList;
    }
  },
  watch: {
    isActivityLogsEnabled(newValue, oldValue) {
      if (newValue !== oldValue && this.watchActivityLogsEnabled) {
        this.handleSwitchChange(newValue, oldValue);
      };
    },
    selectedSeverity(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getViewLogs();
      }
    },
    selectedAction(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getViewLogs();
      }
    }
  },
  created() {
    if (this.isActivityLogsEnabled) {
      this.getViewLogs();
      this.getAllActivities();
    }
  },
  methods: {
    getSeverityClass(option) {
      if (!option || !option.name) return '';
      switch (option.name) {
        case 'critical':
          return 'bg-redCust-110 text-pinkCust-150 px-2 py-0.5 rounded';
        case 'high':
          return 'bg-warning-350 text-warning-650 px-2 py-0.5 rounded';
        case 'medium':
          return 'bg-primary-350 text-primary-400 px-2 py-0.5 rounded';
        default:
          return 'bg-grayCust-160 text-grayCust-800 px-2 py-0.5 rounded';
      }
    },
    dateTimeHumanize: dateTimeHumanize,
    setPagination(data) {
      this.pagination.total = data.total;
      this.pagination.lastPage = data.last_page;
      this.pagination.currentPage = data.current_page;
      if (data.per_page) {
        this.pagination.perPage = data.per_page.toString();
      }
    },
    handlePerPageChange(perPage) {
      this.pagination.perPage = perPage;
      this.pagination.currentPage = 1;
      this.getViewLogs();
    },
    getAllActivities() {
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.get(API_SERVER_URL + '/api/wordpress-activities', { headers })
        .then(response => {
          this.allActivities = [{
            name: 'all_actions',
            severity: 'none',
            display_name: trans('all_actions'),
            id: 1,
          }, ...response.data.data];
        })
        .catch(error => {
          console.log(error);
        })
    },
    getViewLogs(page = 1) {
      if (page && Number.isInteger(page)) {
        this.pagination.currentPage = page
      }
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      const params = {
        page: this.pagination.currentPage,
        per_page: this.pagination.perPage,
      }
      if (this.selectedAction != 1) {
        params.activity_id = this.selectedAction
      }
      if (this.selectedSeverity != 'all_severity') {
        params.severity = this.selectedSeverity.toLowerCase()
      }
      axios.get(API_SERVER_URL + '/api/connects/' + this.$props.connect.id + '/activity-log', { headers, params })
        .then(response => {
          if (response.data) {
            this.viewLogsList = response.data.data.data
            this.setPagination(response.data.data)
          }
        })
        .catch(error => {
          console.log(error);
        }).finally(() => {
          this.initialLoading = false;
        });
    },
    showDeleteLogConfirmation(id = null) {
      this.confirmingLogDeletion = true;
      this.deleteLogId = id;
      this.deleteLogAction = id ? 'single' : 'bulk';
    },
    deleteAction() {
      if (this.deleteLogAction == 'bulk') {
        this.bulkDeleteLogs();
      } else {
        this.deleteLog(this.deleteLogId);
      }
    },
    deleteLog(id) {
      this.deleteLogProcessing = true;
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.delete(API_SERVER_URL + '/api/connects/activity-log/' + id, { headers })
        .then(() => {
          const notification = {
            heading: this.$t('success'),
            subHeading: this.$tChoice('log_deleted_successfully', 1),
            type: "success",
          };
          this.appStore.setNotification(notification);
          this.getViewLogs();
        })
        .catch(error => {
          console.log(error);
        }).finally(() => {
          this.deleteLogProcessing = false;
          this.confirmingLogDeletion = false;
          this.deleteLogId = null;
          this.deleteLogAction = null;
        });
    },
    bulkDeleteLogs() {
      this.deleteLogProcessing = true;
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      const body = {
        'ids': this.selectedLogs.map(log => log.id),
        '_method': 'delete'
      };
      axios.post(API_SERVER_URL + '/api/connects/activity-log/bulk-delete', body, { headers })
        .then(() => {
          const notification = {
            heading: this.$t('success'),
            subHeading: this.$tChoice('log_deleted_successfully', this.selectedLogs.length),
            type: "success",
          };
          this.appStore.setNotification(notification);
          this.getViewLogs();
        })
        .catch(error => {
          console.log(error);
        }).finally(() => {
          this.deleteLogProcessing = false;
          this.confirmingLogDeletion = false;
          this.deleteLogId = null;
          this.deleteLogAction = null;
        });
    },
    handleSwitchChange(value, oldValue) {
      this.watchActivityLogsEnabled = false;
      axios.post(`/api/v2/connects/${this.$props.connect.id}/features`, {
        activity_logs: value
      }).then(res => {
        if (res.data.status) {
          if (value) {
            this.getViewLogs();
            this.getAllActivities();
          }
          this.appStore.setNotification({
            heading: this.$t('success'),
            subHeading: res.data.message,
            type: "success",
          });
        }
      }).catch(() => {
        this.isActivityLogsEnabled = oldValue;
      }).finally(() => {
        this.watchActivityLogsEnabled = true;
      });
    },
    handleRefreshConnects() {
      this.$inertia.reload({
        only: ['connect'],
        onSuccess: () => {
          if (this.isActivityLogsEnabledForPlan) {
            this.isActivityLogsEnabled = true;
          } else {
            this.appStore.setNotification({
              heading: this.$t('error'),
              subHeading: this.$t('not_available_in_your_plan'),
              type: "error",
            });
          }
          this.isAdvanced = true;
          this.showAdvanceConnectModal = false;
        }
      });
    },
    showDisabledNotice() {
      if (!this.isAdvanced) {
        this.showAdvanceConnectModal = true;
        return;
      }
      if (!this.isActivityLogsEnabledForPlan) {
        this.appStore.setNotification({
          heading: this.$t('error'),
          subHeading: this.$t('not_available_in_your_plan'),
          type: "error",
        });
      }
    },
    handleSelectionChange(selectedItems) {
      this.selectedLogs = selectedItems;
    },
    getLink(activityLog) {
      let link = this.$props.connect.url

      if (activityLog.action.includes('delete')) {
        return null
      }

      if (activityLog.object_type == 'Posts') {
        link += '/wp-admin/post.php?post=' + activityLog.object_id + '&action=edit'
      } else if (activityLog.object_type == 'Attachments') {
        link += '/wp-admin/upload.php?item=' + activityLog.object_id
      } else if (activityLog.object_type == 'Menus') {
        link += '/wp-admin/nav-menus.php?action=edit&menu=' + activityLog.object_id
      } else if (activityLog.object_type == 'Plugins') {
        link += '/wp-admin/plugins.php?s=' + activityLog.object_name + '&plugin_status=all'
      } else if (activityLog.object_type == 'Themes') {
        link += '/wp-admin/theme-install.php?theme=' + activityLog.object_name
      } else if (activityLog.object_type == 'Taxonomies') {
        link += '/wp-admin/term.php?taxonomy=' + activityLog.object_subtype + '&tag_ID=' + activityLog.object_id
      } else if (activityLog.object_type == 'Users') {
        link += '/wp-admin/user-edit.php?user_id=' + activityLog.object_id
      }
      return link
    }
  }
};
</script>