<template>
  <div
    :class="showProgress && 'py-32'"
    class="text-center"
  >
    <div
      v-if="showProgress"
      class="text-center"
    >
      <div>
        <img
          :src="cdn('images/SiteEdit/Security/www.gif')"
          alt="gif"
          class="mx-auto"
        >
      </div>
      <div class="mb-1 text-sm font-medium text-grayCust-700">
        Scanning in progress...
      </div>
      <div class="mb-1 text-sm font-normal text-grayCust-500">
        Please wait
      </div>
    </div>
    <div
      :class="showProgress && 'mt-4 mx-auto'"
      class="h-2.5 w-64 rounded-full bg-grayCust-350"
    >
      <span class="flex w-full">
        <span class="progress-value h-2.5 rounded-full" />
      </span>
    </div>
    <!-- <img :src="cdn('images/SiteEdit/Security/progressbar-image.gif')" class="mx-auto w-64" alt=""> -->
  </div>
</template>

<script>

export default {
   
   name:'InProgress',
   props:{
        showProgress:{
            type: Boolean,
            required: false,
            default: true,
        }
   }
}
</script>

<style scoped>
.progress-value {
    transition: 0.3s all linear;
    animation: progress-color 4s linear forwards;
    -webkit-animation: progress-color 4s linear forwards;
}
/* animation */
@keyframes progress-color {
    0% {
        width: 0;
        background: #FBBF24;
    }
    50% {
        width: 40%;
    }
    100% {
        width: 100%;
        background: #11BF85;
    }
}
</style>