<template>
  <CommonModal
    v-model="isOpen"
    size="5xl"
    position="top"
    extra-footer-class="bg-gray-100"
    :allow-padding="false"
    @close="onHandleClose"
  >
    <template #content>
      <div class="relative space-y-6 p-6">
        <site-new-common-tab
          :selected-tab="selectedTab"
          :user-configs="userConfigs"
          :configuration-library="configurationLibrary"
          :selected-config="selectedConfig"
          :is-tab-title="false"
          :is-staging-site="true"
          :is-plugin-and-themes="true"
          :tab-list="tabList"
          :current-index="stagingCurrentTab"
          :total-tab-array="selectedTab == 'ai' ? fromAiTabArray : selectedTab == 'store' ? fromStoreTabArray : selectedTab == 'template' ? fromTemplateTabArray : fromScratchTabArray"
          @inapp_config_changed="inappConfigChange"
          @on-handle-select-tab="onHandleSelectTab"
          @toggle-config-modal="isOpenConfigurationDetails = !isOpenConfigurationDetails"
        />

        <div v-if="selectedTab == 'scratch'">
          <setting-form
            v-if="stagingCurrentTab == 1"
            :form="form"
            :selected-tab="selectedTab"
          />

          <!-- <SiteCreatePlans v-else-if="stagingCurrentTab == 2" /> -->

          <PluginsAndThemes
            v-else-if="stagingCurrentTab == 2"
            v-model="pluginAndTheme"
            :is-staging-site="true"
            :user-configs="userConfigs"
            :configuration-library="configurationLibrary"
            :selected-config="selectedConfig"
            @inapp_config_changed="inappConfigChange"
          />
          <Configuration
            v-else-if="stagingCurrentTab == 3"
            :is-staging-site="true"
          />
        </div>

        <div v-if="selectedTab == 'template'">
          <Templates
            v-if="stagingCurrentTab == 1"
            :form="form"
            @open-new-template="openNewTemplate"
          />
          <setting-form
            v-else-if="stagingCurrentTab == 2"
            :form="form"
            :selected-tab="selectedTab"
          />
        </div>

        <div v-if="selectedTab == 'store'">
          <payout-alert
            v-if="makeStoreEnable"
            extra-class="bg-warning-400 text-warning-1100  mb-4 w-full z-50"
            icon-name="payout/warning-icon.svg"
            title="To buy a paid template please add a valid card. "
            btn-title="Add Card"
            :link="route('card.get')"
          />
          <Store
            v-if="stagingCurrentTab == 1"
            v-model:selected-store-item="selectedStoreItem"
            v-model:selected-store-filters="selectedStoreFilters"
            :shimmer-effect="shimmerFlagStore"
            :store-templates="storeTemplates"
          />
          <setting-form
            v-else-if="stagingCurrentTab == 2"
            :form="form"
            :selected-tab="selectedTab"
          />
        </div>

        <div v-if="selectedTab == 'ai'">
          <AiTool
            v-if="stagingCurrentTab == 1"
            v-model:selected-a-i-item="selectedAIItem"
          />
          <setting-form
            v-else-if="stagingCurrentTab == 2"
            :form="form"
            :selected-tab="selectedTab"
          />
        </div>
      </div>
      <!-- information-modal design -->
      <TransitionRoot :show="isOpenConfigurationDetails">
        <Dialog
          as="div"
          @close="isOpenConfigurationDetails = false"
        >
          <div class="fixed inset-0 z-50">
            <div
              class="flex w-full min-h-full items-center justify-center  text-center sm:items-center sm:p-0"
            >
              <TransitionChild
                as="template"
                enter="ease-out duration-300"
                enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enter-to="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leave-from="opacity-100 translate-y-0 sm:scale-100"
                leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <DialogPanel
                  class="information-modal  mr-1 z-50   overflow-hidden top-0 rounded-t-2xl bg-white text-left  xl:mr-0"
                >
                  <div
                    class="z-50 flex flex-wrap items-center  justify-between rounded-t-2xl border-t border-secondary-800 bg-primary-275 px-6 py-4 lg:flex-nowrap"
                  >
                    <div class="mb-4 flex flex-wrap items-center lg:mb-0 lg:flex-nowrap">
                      <div class="text-base font-medium text-warning-1300">
                        Configuration
                        details
                      </div>
                      <div
                        v-if="selectedConfig"
                        class="ml-3 flex flex-wrap items-center lg:flex-nowrap"
                      >
                        <img
                          v-if="selectedConfig.logo_url"
                          :src="selectedConfig.logo_url"
                          :alt="selectedConfig.name"
                          class="mr-1.5 w-7"
                        >
                        <img
                          v-else
                          :src="cdn(`images/word_press_icon_new.png`)"
                          :alt="selectedConfig.name"
                          class="mr-1.5 w-7"
                        >
                        <div class="text-sm font-medium text-grayCust-640">
                          {{
                            selectedConfig.name }}
                        </div>
                      </div>
                    </div>
                    <div class="flex flex-wrap items-center lg:flex-nowrap">
                      <div
                        v-if="false"
                        class="flex cursor-pointer items-center text-sm font-medium text-primary-900"
                      >
                        <img
                          :src="cdn('images/pencil-img.svg')"
                          alt=""
                          class="mr-1"
                        >Edit
                        Configuration
                      </div>
                      <div
                        class="ml-3 select-none rounded-md p-1"
                        style="background: rgba(0, 94, 84, 0.1);"
                        @click="isOpenConfigurationDetails = false"
                      >
                        <img
                          :src="cdn('images/close-button.png')"
                          alt=""
                          class="cursor-pointer text-grayCust-550"
                        >
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="selectedConfig"
                    class="modal-height custom-scroll  grid grid-cols-1 gap-3 break-all p-6 md:grid-cols-2 lg:grid-cols-3 "
                  >
                    <div>
                      <div class="w-full">
                        <div class="mb-3 flex  select-none items-center">
                          <img
                            :src="cdn(`images/wordpress-icon.png`)"
                            alt="seo-image"
                            class="mr-1.5"
                          >
                          <div class="text-sm font-medium text-grayCust-700">
                            WordPress
                          </div>
                        </div>
                        <div class="rounded-xl border border-warning-1020 p-4">
                          <div class="mb-2 select-none">
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Version
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.wp_version }}
                            </div>
                          </div>
                          <div class="mb-2">
                            <div class="mb-1 select-none text-sm font-semibold text-grayCust-620">
                              wp-config
                              values
                            </div>
                            <div
                              v-for="(wp_config, index) in selectedConfig.wp_config_json"
                              :key="wp_config.key"
                              class="text-sm font-normal text-grayCust-470"
                            >
                              {{ wp_config.key }}={{ wp_config.value }}
                            </div>
                          </div>
                          <div class="mb-2">
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Language
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.wp_language }}
                            </div>
                          </div>
                          <div class="mb-2">
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              WordPress
                              Username
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.wp_username ?
                                selectedConfig.wp_username :
                                'Random' }}
                            </div>
                          </div>
                          <div>
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Multi Site
                              Installation
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.multi_site ? 'Yes' : 'No' }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div class="w-full md:w-80">
                        <div class="mb-3 flex  items-center ">
                          <img
                            :src="cdn(`images/php-icon.svg`)"
                            alt="seo-image"
                            class="mr-1.5"
                          >
                          <div class="text-sm font-medium text-grayCust-700">
                            PHP
                          </div>
                        </div>
                        <div class="rounded-xl border border-warning-1020 p-4">
                          <div class="mb-2">
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Version
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.php_version }}
                            </div>
                          </div>
                          <div>
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Server
                              Configuration
                            </div>
                            <div
                              v-for="(php_config, index) in selectedConfig.php_config_json"
                              :key="php_config.key"
                              class="text-sm font-normal text-grayCust-470"
                            >
                              {{ php_config.key }}={{ php_config.value }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="selectedConfig?.faker_json?.core?.status || selectedConfig?.faker_json?.woocommerce?.status"
                    >
                      <div class="w-full md:w-80">
                        <div class="mb-3 flex  items-center ">
                          <img
                            :src="cdn(`images/wordpress-icon.png`)"
                            alt="seo-image"
                            class="mr-1.5"
                          >
                          <div class="text-sm font-medium text-grayCust-700">
                            Faker
                          </div>
                        </div>
                        <div class="rounded-xl border border-warning-1020 p-4">
                          <div
                            v-if="selectedConfig?.faker_json?.core?.status"
                            class="mb-2"
                          >
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Core
                            </div>
                            <div
                              v-for="(value, key) in selectedConfig.faker_json.core"
                              :key="key"
                              class="truncate text-sm font-normal text-grayCust-470"
                            >
                              <span v-if="key != 'status'">{{ key }}={{ value
                              }}</span>
                            </div>
                          </div>
                          <div v-if="selectedConfig?.faker_json?.woocommerce?.status">
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              WooCommerce
                            </div>
                            <div
                              v-for="(value, key) in selectedConfig.faker_json.woocommerce "
                              :key="key"
                              class="truncate text-sm font-normal text-grayCust-470"
                            >
                              <span v-if="key != 'status'">{{ key }}={{ value
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="selectedConfig.wp_pre_ins_theme_slugs || selectedConfig.wp_pre_ins_plugin_slugs || (selectedConfig.wp_plugin_theme_url_json && selectedConfig.wp_plugin_theme_url_json.length)"
                    >
                      <div class="w-full md:w-80">
                        <div class="mb-3 flex  items-center ">
                          <img
                            :src="cdn(`images/wordpress-icon.png`)"
                            alt="seo-image"
                            class="mr-1.5"
                          >
                          <div class="text-sm font-medium text-grayCust-700">
                            Themes &
                            Plugins
                          </div>
                        </div>
                        <div class="rounded-xl border border-warning-1020 p-4">
                          <div
                            v-if="selectedConfig.wp_pre_ins_theme_slugs"
                            class="mb-2"
                          >
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Themes
                              (wp.org)
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.wp_pre_ins_theme_slugs }}
                            </div>
                          </div>
                          <div
                            v-if="selectedConfig.wp_pre_ins_plugin_slugs"
                            class="mb-2"
                          >
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Plugins
                              (wp.org)
                            </div>
                            <div class="text-sm font-normal text-grayCust-470">
                              {{ selectedConfig.wp_pre_ins_plugin_slugs }}
                            </div>
                          </div>
                          <div
                            v-if="selectedConfig.wp_plugin_theme_url_json && selectedConfig.wp_plugin_theme_url_json.length"
                          >
                            <div class="mb-1 text-sm font-semibold text-grayCust-620">
                              Themes/Plugins
                              from URL
                            </div>
                            <div
                              v-for="(url, index) in selectedConfig.wp_plugin_theme_url_json"
                              :key="index"
                              class="truncate text-sm font-normal text-grayCust-470"
                            >
                              {{ url.type }}: {{ url.url }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>
      <div class="flex items-center justify-between border-t border-grayCust-180 bg-grayCust-50 px-6 py-5">
        <CButton
          :btn-title="$t('cancel')"
          btn-type="gray-outline-btn"
          size="btn-md"
          @click="onHandleClose"
        />
        <div class="flex items-center gap-x-3">
          <CButton
            v-if="stagingCurrentTab != 1"
            :btn-title="$t('back')"
            btn-type="gray-outline-btn"
            size="btn-md"
            @click="backClick()"
          />
          <CButton
            v-if="stagingCurrentTab == 2 && selectedTab == 'scratch' && false"
            icon-name="ServerIcon"
            btn-title="Configuration"
            btn-type="gray-outline-btn"
            size="btn-md"
          />
          <CButton
            v-if="!(selectedTab == 'ai' ? fromAiTabArray.length == stagingCurrentTab : selectedTab == 'store' ? fromStoreTabArray.length == stagingCurrentTab : selectedTab == 'template' ? fromTemplateTabArray.length == stagingCurrentTab : fromScratchTabArray.length == stagingCurrentTab)"
            id="site_create_next_step"
            :disabled="(selectedTab == 'template' && !form.template_slug) || (selectedTab == 'store' && !selectedStoreItem) || (selectedTab == 'ai' && !selectedAIItem)"
            btn-title="Next Step"
            btn-type="secondary"
            size="btn-md"
            @click="nextStep()"
          />
          <CButton
            v-if="selectedTab == 'ai' ? fromAiTabArray.length == stagingCurrentTab : selectedTab == 'store' ? fromStoreTabArray.length == stagingCurrentTab : selectedTab == 'template' ? fromTemplateTabArray.length == stagingCurrentTab : fromScratchTabArray.length == stagingCurrentTab"
            id="site_create_modal_button"
            :disabled="(currentSiteTab == 'store' && makeStoreEnable) || processing || (selectedTab == 'ai' && !selectedAIItem)"
            :btn-title="$t('create_site')"
            btn-type="secondary"
            size="btn-md"
            @click="createSite"
          />
        </div>
      </div>
    </template>
  </CommonModal>
</template>

<script>
import SettingForm from "@/components/SiteCreate/StagingSiteCreation/SiteSettings/Index.vue";
import PluginsAndThemes from '@/components/SiteCreate/V4/PluginAndThemes.vue';
import SiteNewCommonTab from '@/components/SiteCreate/V4/SiteNewCommonTab.vue';
// import PluginsAndThemes from "@/components/GoLiveNew/PluginsAndThemes/Index.vue";
import PayoutAlert from '@/Common/Alert.vue';
import CButton from '@/components/New/Blocks/Buttons/CommonButton.vue';
import AiTool from '@/components/SiteCreate/StagingSiteCreation/AITool/Index.vue';
import Store from '@/components/SiteCreate/StagingSiteCreation/Store/Index.vue';
import Templates from '@/components/SiteCreate/StagingSiteCreation/Templates/Index.vue';
import Configuration from '@/components/SiteCreate/V4/Configuration.vue';
import SiteCreatePlans from '@/components/SiteCreate/V4/SiteCreatePlans.vue';
import { useAppStore } from '@/store/index';
import vClickOutside from 'click-outside-vue3';

import { DEFAULT_SUFFIX_DOMAIN_ID, IS_STORE_ACTIVE } from '@/const';
import { wTrans } from 'laravel-vue-i18n';
import { throttle } from 'lodash-es';
import { mapState, mapStores } from 'pinia';
import { route } from 'ziggy-js';

export default {
    directives: {
      clickOutside: vClickOutside.directive
    },
    components: {
        SiteNewCommonTab,
        SettingForm,
        PluginsAndThemes,
        Configuration,
        Templates,
        Store,
        CButton,
        AiTool,
        PayoutAlert,
        SiteCreatePlans
    },

    props:["isOpenNewSiteCreate"],
    data(){
        return {
            stagingCurrentTab: 1,
            selectedTab: 'scratch',
            tabList: [
                {
                    id: 'scratch',
                    title: 'From Scratch'
                },
                {
                    id: 'template',
                    title: wTrans('from_snapshot'),
                },
                {
                    id: 'store',
                    title: 'From Store'
                },
                {
                    id: 'ai',
                    title: 'From AI',
                    iconName: 'StarsIcon'
                }
            ],
            fromScratchTabArray: ["Select Option", "Plugins & Themes"],
            fromTemplateTabArray: ["Select Template", "Select Option"],
            fromStoreTabArray: ["Select Template", "Select Option"],
            fromAiTabArray: ["AI Tool", "Select Option"],

            processing:false,
            failReason:'',
            isMultipleSelected:true,
            isOpenFeatures: false,
            templateIcon:'empty-icon.svg',
            templateTitle: 'No Templates Found!',
            templateSubtitle:'You have not created any templates. Start by saving a site as a Template',
            storeTemplateSubtitle:'Store did not have any published template yet.',
            templateButtonText:'Create Template',
            selectedStoreItem: false,
            selectedStoreProduct: false,
            selectedAIItem: false,
            orderToken:null,
            form: {
                site_name: "",
                wp_version: "",
                php_version: "",
                configuration_id: "",
                template_slug: null,
                is_reserved: "",
                bypass_pool: false,
                server_group_id: "",
                ai_provider: "",
            },
            configInfoTooltip:false,
            isOpen: false,
            errors:[],
            isEditingSiteName: true,
            configDetail: false,
            selectedStoreFilters: {
                search: '',
                pageBuilder: '',
                category: '',
                price:'',
            },
            isEditingPrice: false,
            sorts : [
                { by: 'number_installations', name: 'Popularity', type:'DESC' },
                { by: 'updated_at', name: 'Latest', type:'DESC' },
                { by: 'price', name: 'Price Low to High', type:'ASC' },
                { by: 'price', name: 'Price High to Low', type:'DESC' },
            ],
            storeTemplates:[],
            categoryList:[],
            pbList:[{ id: '',
                    name: "Page Builder",
                    slug: '',
                    icon:"images/page-builder.png" }],
            priceList: [
                {
                    name: "All",
                    isPaid: null,
                },
                {
                    name: "Free",
                    isPaid: false,
                },
                {
                    name: "Paid",
                    isPaid: true,
                },
            ],
            isFeaturesPageShow:true,
            openCustomPriceTextBoxTimeOut: null,
            listenOrderEventTimeOut: null,
            toggleCustomizeSiteNameTimeOut: null,
            isOpen3DModal: false,
            verifyURL: null,
            isStoreActive:  IS_STORE_ACTIVE && IS_STORE_ACTIVE == 'true'? true :false,
            shimmerFlagStore: true,
            searchTimeout: null,
            page: 1,
            loading: false,
            finished: false,
            pluginAndTheme: [],
            isOpenConfigurationDetails: false,
        }
    },
    computed: {
        makeStoreEnable(){
            return !this.getUser.has_payment_method && this.selectedStoreItem && this.selectedStoreProduct.product.price > 0;
        },
        phpVersions() {
            const that = this

            let wp = this.appStore.wp_versions.filter(el => el.wp == that.form.wp_version)
                if(wp.length){
                    wp = wp[0]
                }else{
                    wp = this.appStore.wp_versions.filter(el => el.wp == that.appStore.default_wp)[0]
                }
            return wp.php
        },
        wpVersions() {
            return this.appStore.wp_versions;
        },
        // ...mapGetters(["userConfigs", "teamCan", "teamAllow", "configurationLibrary","serverGroups"]),
        ...mapStores(useAppStore),
        ...mapState(useAppStore, {
            getUser: "getUser",
            userConfigs: "configs",
            teamCan: "teamCan",
            teamAllow: "teamAllow",
            configurationLibrary: "configurationLibrary",
            serverGroups: "serverGroups",
            featureAvailableFrom: "featureAvailableFrom",
            pageBuilderList: 'pageBuilders',
            suffixDoamins: 'suffixDoamins',
            toggleNewTemplate: 'toggleNewTemplate',
         }),
        selectedMultiServer(){
            const that = this
            const server_group = that.serverGroups.filter(el => el.group_id == that.form.server_group_id)
            return server_group.length?server_group[0]:that.serverGroups[0]
        },
        selectedConfig() {
          let config_arr = this.userConfigs.concat(this.configurationLibrary);
          if(config_arr.length){
            return config_arr.filter(el => el.id == this.form.configuration_id)[0];
          }
          return {};
        },
        activeSuffixDomain(){
          let active_suffix_domain = '';
          let team_id = this.$page.props.auth?.user?.current_team?.id;
          if(team_id){
            active_suffix_domain = this.suffixDoamins.find(el=>el.team_id == team_id);
          }
          if(!active_suffix_domain){
            active_suffix_domain = this.suffixDoamins.find(el=>el.type == 'public' &&
            el.cloud_suffix_domain_id == DEFAULT_SUFFIX_DOMAIN_ID);
          }
          return active_suffix_domain;
        },
    },
    watch: {
        selectedTab(value){
            if (value == 'store') {
                this.selectedStoreItem = false
                this.throttledHandleScroll = throttle(this.handleScroll, 300);
                window.addEventListener('scroll', this.throttledHandleScroll);
                this.getStoreTemplates();
            } else if (value == 'scratch'){
                if (this.userConfigs.length) {
                    this.form.configuration_id = this.userConfigs[0].id;
                }
                this.configChange('selectedTab');
            } else if (value == 'template') {
                this.form.template_slug = null
            } else if (value == 'ai') {
                this.selectedAIItem = null
            } else {
                window.removeEventListener('scroll', this.throttledHandleScroll);
            }
        },
        isOpen() {
            if (this.isOpen) {
                this.$nextTick(() => {
                    this.form.is_reserved = false;
                    this.form.suffix_domain_id = this.activeSuffixDomain ? this.activeSuffixDomain.cloud_suffix_domain_id : ''
                    if (this.userConfigs.length && this.form.configuration_id == "") {
                        this.form.configuration_id = this.userConfigs[0].id;
                    }
                    this.configChange('isOpen');
                })
            } else {
                this.stagingCurrentTab = 1
                this.selectedTab = 'scratch'
                this.reset();
            }
        },
        userConfigs(value){
            if (value && value.length) {
                this.form.configuration_id = this.userConfigs[0].id;
            }
        },
        selectedStoreFilters: {
            handler(){
                const self = this;
                self.selectedStoreItem = false
                clearTimeout(self.searchTimeout);
                self.searchTimeout = setTimeout( async () => {
                    self.page = 1;
                    self.storeTemplates = [];
                    self.finished = false;
                    await self.getStoreTemplates();
                }, 700);
            },
            deep: true
        },
        pageBuilderList: {
            handler: function (val, oldVal) {
                this.pbList = JSON.parse(JSON.stringify(this.pageBuilderList));
                const newItem = { id: '',
                                    name: "Page Builder",
                                    slug: '',
                                    icon:"images/page-builder.png"};
                this.pbList.unshift(newItem);
            },
            deep: true
        },
        selectedStoreItem(value) {
            if (value) {
                const item = this.storeTemplates.filter(el => el.id == value)
                if (item.length) {
                    this.selectedStoreProduct = item[0]
                    this.form.php_version = item[0].product?.resource?.php_version
                }
            } else {
                this.selectedStoreProduct = false
            }
        }
    },
    mounted() {
    //   this.appStore.getUserConfigs();
        Promise.all([
            this.appStore.fetchStoreCategories(),
            this.appStore.fetchStackCategories(),
            this.appStore.fetchPageBuilders()
        ]);

        this.pbList = JSON.parse(JSON.stringify(this.pageBuilderList));
        const newItem = { id: '',
                        name: "Page Builder",
                        slug: '',
                        icon:"images/page-builder.png"};
      this.pbList.unshift(newItem);
      // Emit event to parent if needed
      this.$emit('siteCreateModalMounted',true);
    },
    unmounted() {
        clearTimeout(this.openCustomPriceTextBoxTimeOut)
        clearTimeout(this.listenOrderEventTimeOut)
        clearTimeout(this.toggleCustomizeSiteNameTimeOut)
        clearTimeout(this.searchTimeout)
        window.removeEventListener('scroll', this.throttledHandleScroll);
    },
    methods: {
        onHandleClose() {
            console.log('onHandleClose')
            this.isOpen = false
        },
        nextStep() {
            if (this.stagingCurrentTab === 1) {
                this.stagingCurrentTab = 2
            } else if (this.stagingCurrentTab === 2) {
                this.stagingCurrentTab = 3
            }
        },
        backClick() {
            if (this.stagingCurrentTab === 2) {
                this.stagingCurrentTab = 1
            } else if (this.stagingCurrentTab === 3) {
                this.stagingCurrentTab = 2
            }
        },
        onHandleSelectTab(id) {
            this.selectedTab = id
            this.stagingCurrentTab = 1
        },

        openNewTab(){
            window.open('https://instawp.com/become-a-seller/', '_blank', 'noreferrer');
        },
        handleBtnClick(){

        },
        async getStoreTemplates() {
            if (this.loading || this.finished) return;
            this.loading = true;
            this.shimmerFlagStore = true;
            const that = this;
            const params = {
                type : 'store',
                per_page : 40,
                page : that.page,
                q: this.selectedStoreFilters.search,
                page_builder: this.selectedStoreFilters.pageBuilder,
                category_id: this.selectedStoreFilters.category ? this.selectedStoreFilters.category : null,
            }
            if (this.selectedStoreFilters.price) {
                params.is_paid = Boolean(this.selectedStoreFilters.price === 'paid')
            }
            try {
                const response = await axios
                    .get(`/api/v2/market-products`, { params: params });
                if (response.data.data.length) {
                    that.storeTemplates.push(...response.data.data);
                    that.page += 1;
                } else {
                    that.finished = true;
                }
            } catch (error) {
                console.error('Failed to load more products:', error);
            } finally {
                that.shimmerFlagStore = false;
            }
            that.loading = false;
        },
        handleScroll(event) {
            const container = event.target;
            if (container.scrollTop + container.clientHeight >= container.scrollHeight - 100) {
                this.getStoreTemplates();
            }
        },
        openCustomPriceTextBox(){
            this.isEditingPrice =  true;
            this.openCustomPriceTextBoxTimeOut = setTimeout(() => {
            if (this.isEditingPrice) {
                    this.$refs.inputSiteNamePrice.focus()
                }
            }, 100);
        },
        setCurrentStore(item) {
            this.selectedStoreItem = item;
        },
        saveCustomPrice(){
            this.isEditingPrice =  false;
            if(this.price != ""){
                this.priceList.push(this.price)
                this.price = "";
            }
        },
        hideNameInput(){
        },
        toggle() {
            this.isOpen = !this.isOpen;
        },
        createTemplate(){
            this.$inertia.visit(route('templates'))
        },
        closeModal() {
            this.reset();
        },
        reset() {
            this.isOpen = false;
            this.form = {
                site_name: "",
                wp_version: "",
                php_version: "",
                configuration_id: "",
                template_slug: null,
                is_reserved: "",
                bypass_pool: false,
                server_group_id: "",
                suffix_domain_id: "",
            }
            this.selectedTab = 'scratch'
            //this.isFeaturesPageShow = false
            this.$emit("closeSiteCreateModal")
        },
        configChange(place ='') {
            const config_id = this.form.configuration_id;
            if(!this.userConfigs.length){
              console.error("userConfigs not found",{
                place,
                userConfigs: this.userConfigs,
                configurationLibrary: this.configurationLibrary,
                getUser: this.getUser,
              });
            }
            const configArr = this.userConfigs.concat(this.configurationLibrary).filter(function (e) {
                return e.id == config_id;
            });
            let wp = this.appStore.wp_versions.filter(el => el.wp == configArr[0].wp_version)
            if(wp.length){
                wp = wp[0]
            }else{
                wp = this.appStore.wp_versions.filter(el => el.wp == this.appStore.default_wp)[0]
            }
            this.form.wp_version = wp.wp
            this.form.php_version = wp.php.includes(configArr[0].php_version)?configArr[0].php_version:wp.php[0]

            const server_group = this.serverGroups.filter(el => el.group_id == configArr[0].ic_server_group_id)
            this.form.server_group_id = server_group.length?server_group[0].group_id:this.serverGroups[0].group_id
            if (this.teamCan?.reserve_sites) {
                this.form.is_reserved = configArr[0].is_site_reserved?true:false
            }
        },
        getWPName(version) {
            const that = this
                const wp =  that.appStore.wp_versions.filter(el => el.wp == version)
                return wp.length?wp[0].wp_name:version
        },
        setDefaultPHPVersion(wp_version){
            const wp = this.appStore.wp_versions.filter(el => el.wp == wp_version)[0]
            if (!(this.form.php_version && wp.php.includes(this.form.php_version))) {
                const default_php_v = this.appStore.default_php
                this.form.php_version = wp.php.includes(default_php_v)?default_php_v:wp.php[0]
            }
        },
        createSite(data = false) {
            const that = this;
            let post_url = '/api/v2/sites'
            let post_data = {}
            let source_type = ''

            if (data && data.hasOwnProperty('launch')) {
                source_type = data?.source
               if (data.hasOwnProperty('from_session') && data.from_session) {
                post_data.from_session = true
               }
            } else if (that.selectedTab == 'scratch') {
                post_url = '/api/v2/sites'
                post_data = Object.fromEntries(Object.entries(that.form).filter(([key, value]) => (['site_name', 'wp_version', 'php_version', 'configuration_id', 'is_reserved', 'bypass_pool', 'server_group_id','suffix_domain_id']).includes(key)))
                source_type = 'logged_in'
            } else if (that.selectedTab == 'template') {
                if (!that.form.template_slug) {
                    const message = {
                        heading: that.$t('failed') + ' !',
                        subHeading: that.$t('please_select_template_list') + '.',
                        type: "error",
                    };
                    that.appStore.setNotification(message);
                    return false;
                }
                post_url = '/api/v2/sites/template'
                post_data = Object.fromEntries(Object.entries(that.form).filter(([key, value]) => (['site_name', 'template_slug', 'is_reserved', 'php_version', 'suffix_domain_id']).includes(key)))
                source_type = 'logged_in_template'
            } else if (that.selectedTab == 'store') {
                if (!that.selectedStoreItem) {
                    const message = {
                        heading: that.$t('failed') + ' !',
                        subHeading: that.$t('please_select_template_list') + '.',
                        type: "error",
                    };
                    that.appStore.setNotification(message);
                    return false;
                }
                that.processing = true;
                that.createOrder();
                return true;
            }else if (that.selectedTab == 'ai') {
                if (!that.selectedAIItem) {
                    const message = {
                        heading: that.$t('failed') + ' !',
                        subHeading: that.$t('please_select_ai_provider_list') + '.',
                        type: "error",
                    };
                    that.appStore.setNotification(message);
                    return false;
                }
                post_data = Object.fromEntries(Object.entries(that.form).filter(([key, value]) => (['site_name', 'wp_version', 'php_version', 'configuration_id', 'is_reserved', 'bypass_pool', 'server_group_id','suffix_domain_id']).includes(key)))
                post_data.ai_provider = that.selectedAIItem
                source_type = 'logged_in_ai'
            }else {
                post_data = Object.fromEntries(Object.entries(that.form).filter(([key, value]) => (['site_name','wp_version','php_version','configuration_id','is_reserved','bypass_pool','server_group_id']).includes(key)))
                source_type = 'logged_in'
            }


            const plugins_themes = that.pluginAndTheme
            if (plugins_themes.length) {
                post_data.plugins_themes = []
                plugins_themes.forEach(item => {
                    post_data.plugins_themes.push({ 'product_type': item.product_type, 'source_type': item.source_type, 'source_location': item.source_location })
                });
            }
            that.launchSite(post_url, post_data, source_type);
        },
        launchSite(post_url, post_data, source_type) {
            const that = this;
            const formData = post_data && Object.keys(post_data).length? post_data:{}

            that.isOpen = false;
            that.processing = false;
            that.appStore.resetSiteInstallation()
            that.appStore.updateLaunchSiteStatus('waiting');
            const uri = window.location.search.substring(1);
            const params = new URLSearchParams(uri);
            if (params.get("launch_slug") && params.get('plugins')) {
                formData.plugin_slugs = params.get('plugins')
            }
            if (params.get("launch_slug") && params.get('themes')) {
                formData.theme_slugs = params.get('themes')
            }
            if (params.get("configuration_id")) {
                formData.configuration_id = params.get("configuration_id")
            }
            axios.post(post_url, formData)
                .then(response => {
                    that.appStore.updateLaunchSiteStatus('processing');
                    response.data.data.is_reserved = formData.is_reserved
                    that.appStore.updateLaunchSiteDetail(response.data.data);
                    if(response.data.data?.wp_redirect){
                        that.appStore.updateLaunchSiteCustomRedirect(encodeURIComponent(response.data.data.wp_redirect));
                    }
                    if (response.data.data.is_pool) {
                        that.appStore.updateLaunchSiteStatus('completed');
                        if (response.data.data?.install_content_task_id) {
                            this.appStore.install_content_message = "Plugin/Theme";
                            this.appStore.install_content_status = 'processing';
                            const installContentTaskID = response.data.data.install_content_task_id
                            window.Echo.channel(`site.plugin.theme.installation.progress.${installContentTaskID}`)
                                .listen('SitePluginThemeInstallationProgress', (e) => {
                                    const progress = e.hasOwnProperty('percentage_complete') ? e.percentage_complete : 0

                                    if (progress == 100) {
                                        this.appStore.install_content_status = 'completed';
                                        Echo.leave(`site.plugin.theme.installation.progress.${installContentTaskID}`);
                                    }

                                    if (e.hasOwnProperty('status') && e.status == 'error') {
                                        this.appStore.install_content_status = 'failed';
                                        Echo.leave(`site.plugin.theme.installation.progress.${installContentTaskID}`);
                                    }

                                });
                        }
                        that.$emit("refreshTable");
                    }else{
                        that.appStore.setSiteTaskId(response.data.data.task_id);
                        that.appStore.subscribeSiteInstallationProgressEvent();

                    }



                    this.appStore.getUserConfigs();


                }).catch(error => {


                    that.appStore.updateLaunchSiteMessage({
                        heading: that.$t('installation_failed') + ' !',
                        subHeading: error?.response?.data.message || error.message,
                    });

                    that.appStore.updateLaunchSiteStatus('failed');

                    // Send slack message
                    that.slackAnnouncement(formData);

                });
        },
        slackAnnouncement(formData) {
          axios.post(`/api/notify-error`, formData)
        .then(res => {

        })
        },
        async createOrder(){
            const that = this;
            try {
                await that.listenOrderEvent();
                const response = await axios
                .post(`/api/v2/store-orders`, {
                        template_id: that.selectedStoreProduct?.product.resource.id,
                        product_id: that.selectedStoreProduct?.product.id,
                        return_url: window.location.href,
                        store_type: 'In-app',
                    });
                that.orderToken = response.data.data.token;
                that.form.order_id = response.data.data.id;
            } catch (error) {

               Echo.leave(`order.confirmation`);
                that.processing = false;
                if(error.response.status == 400) {
                    that.purchaseStatus = 'failed';
                    const message = {
                        heading: that.$t('failed') + ' !',
                        subHeading: error.response.data.fail_message,
                        type: "error",
                    };
                    that.failReason = error.response.data.fail_message;
                    that.appStore.setNotification(message);
                } else {
                    that.purchaseStatus = '';
                    message.subHeading = error.response.data.response;
                    that.errors = error.response.data.errors;
                }
                return false;
            }
        },
        listenOrderEvent() {
            return new Promise((resolve, reject) => {
                try {
                    //Pusher.logToConsole = true;
                    window.Echo.channel(`order.confirmation`)
                    .listen('OrderConfirmed', (e) => {
                        if (e.token == this.orderToken) {
                            const status = e.hasOwnProperty('status') ? e.status : 'init';
                            this.isOpen3DModal = false;
                            if (status == 'completed') {
                                this.purchaseStatus = 'completed';
                                Echo.leave(`order.confirmation`);
                                if (this.selectedStoreProduct?.product.price > 0) {
                                    const message = {
                                        heading: this.$t('success') + ' !',
                                        subHeading: 'Payment of $'+ this.selectedStoreProduct?.product.price + ' is done successfully. Now moving to site installation.',
                                        type: "success",
                                    };
                                    this.appStore.setNotification(message);
                                }
                                this.listenOrderEventTimeOut = setTimeout(() => {
                                    const post_url = '/api/v2/sites/template';
                                    const post_data = Object.fromEntries(Object.entries(this.form).filter(([key, value]) => (['site_name','template_slug','is_reserved', 'php_version', 'order_id']).includes(key)));
                                    post_data.template_slug = this.selectedStoreProduct?.product.resource.slug;
                                    this.launchSite(post_url, post_data, 'in_app_store');
                                }, 3000);
                            } else if (status == 'init' && e.hasOwnProperty('confirmation_url') && e.confirmation_url.length > 0 ) {
                                this.isOpen3DModal = true;
                                this.verifyURL = e.confirmation_url;
                            } else if (status == 'error' || status == 'refunded') {
                                Echo.leave(`order.confirmation`);
                                this.processing = false;
                                this.purchaseStatus = 'failed';
                                this.failReason = e.hasOwnProperty('failReason') ? e.failReason : 'Your card has insufficient funds.';
                                const notification = {
                                    heading: this.$t("error"),
                                    subHeading: this.$t("purchase_failed"),
                                    type: "error",
                                };
                                this.appStore.setNotification(notification);
                            }
                        }
                    });
                    resolve();  // Resolve the promise when the listener is set up
                } catch (error) {
                    reject(error);  // Reject the promise if there's an error
                }
            });
        },
        closeNewSiteCreate() {
            this.$emit("close_new_site_create");
        },
        toggleCustomizeSiteName() {

            if (!this.teamCan?.advance_config) {
                return false
            }

            this.isEditingSiteName = !this.isEditingSiteName;
            this.toggleCustomizeSiteNameTimeOut = setTimeout(() => {
            if (!this.isEditingSiteName) {
                    this.$refs.inputSiteName.focus()
                }
            }, 100);
        },
        enterKey(){
            this.isEditingSiteName = !this.isEditingSiteName;
        },
        setCurrentAvailable(item){
            this.currentAvailable = item.id;
        },
        inappConfigChange(id) {
            this.form.configuration_id = id;
            this.configChange('inappConfigChange')
        },
        onUpgradeClick() {
            this.reset()
        },
        openNewTemplate() {
            // this.isOpen = false
            // this.appStore.toggleNewTemplate = !this.toggleNewTemplate
            this.$inertia.visit(route('templates'))
        }
    }
};
</script>

<style scoped>
@media (max-height: 800px) {
    .site-model {
        align-items: start !important;
    }
}
.modal-height {
    height: 260px;
    overflow: auto;
}

.information-modal {
    height: 330px;
    max-width: 1060px;
    width: 1020px;
    z-index: 50;
    margin-top: 200px;
}

@media screen and (max-width:1300px) and (max-height:700px) {
    .information-modal {
        margin-top: 270px;
    }
}
@media screen and (max-width:767px) {
    .information-modal {
        margin-top: 253px;
        width: 680px;
    }
}

@media screen and (max-width:576px) {
    .information-modal {
        margin-top: 73px;
        width: 500px;
    }
}

@media screen and (max-width:375px) {
    .information-modal {
        margin-top: 79px;
        width: 300px;
    }
}

</style>
