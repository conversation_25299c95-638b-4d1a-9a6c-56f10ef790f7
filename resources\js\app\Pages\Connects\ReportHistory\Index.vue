<template>
  <div class="w-full rounded-lg border border-grayCust-180 p-4 mt-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <p class="text-base font-medium text-grayCust-1520 mb-1.5">
          Record History
        </p>
        <p class="text-sm text-grayCust-500">
          Basic details about the website
        </p>
      </div>
      <div>
        <!-- <CButton
          btn-title="Schedule Report"
          btn-type="gray-outline-btn"
          icon-name="DocumentReportIcon"
        /> -->
        <ScheduleReports />
      </div>
    </div>

    <!-- Old CustomTable implementation - commented out -->
    <!-- <CustomTable
      :fields="fields"
      :items="items"
      :busy="isLoading"
      responsive
      hover
      bordered
      row-bordered
      @sort-changed="onSortChanged"
      @row-clicked="onRowClicked"
    > -->

    <!-- New CommonTable implementation -->
    <CommonTable
      :fields="fields"
      :items="items"
      :busy="isLoading"
      responsive
      hover
      bordered
      row-bordered
      @sort-changed="onSortChanged"
      @row-clicked="onRowClicked"
    >
      <!-- Custom cell for the 'status' column -->
      <template #cell_scheduled="{ item }">
        <p class="text-sm text-grayCust-980 font-medium">
          {{ item.scheduled }}
        </p>
      </template>
      <template #cell_method="{ item }">
        <p class="text-sm text-grayCust-910">
          {{ item.method }}
        </p>
      </template>
      <template #cell_inclusion="{ item }">
        <div class="flex flex-wrap gap-2 items-center">
          <span
            v-for="(inclusion, index) in item.inclusion.slice(0, 2)"
            :key="index"
            :class="['px-2 py-0.5 rounded-full text-xs font-medium', getInclusionClass(inclusion)]"
          >
            {{ inclusion }}
          </span>
          <span
            v-if="item.inclusion.length > 2"
            class="text-xs font-medium text-grayCust-660 bg-grayCust-280 px-2 py-0.5 rounded-full"
          >
            +{{ item.inclusion.length - 2 }}
          </span>
        </div>
      </template>
      <template #cell_sites="{ item }">
        <div class="flex flex-nowrap gap-2 items-center">
          <!-- v-for="(site, index) in item.sites.slice(0, 2)"
          :key="index" -->
          <span class="text-sm text-grayCust-910 max-w-[70px] truncate">
            {{ item.sites }}
          </span>
        </div>
      </template>

      <template #cell_customer="{ item }">
        <div class="flex items-center">
          <div class="flex -space-x-2">
            <template
              v-for="(customer, index) in item.customer.slice(0, 3)"
              :key="index"
            >
              <img
                v-lazy="cdn(customer)"
                :alt="`Customer ${index + 1}`"
                class="w-8 h-8 rounded-full border-2 border-white ring-1 ring-grayCust-180"
              >
            </template>
          </div>
        </div>
      </template>
      <template #cell_action="{ item }">
        <!-- Old icon-button implementation - commented out -->
        <!-- <icon-button
          btn-type="gray-outline-btn"
          icon-color="text-grayCust-980"
          :icon-name="item.action"
        /> -->

        <!-- New CommonIconButton implementation -->
        <CommonIconButton
          btn-type="gray-outline-btn"
          icon-color="text-grayCust-980"
          icon-type="outline"
          :icon-name="item.action"
        />
      </template>
    </CommonTable>

    <!-- Old closing tag - commented out -->
    <!-- </CustomTable> -->
  </div>
</template>

<script>
import ScheduleReports from '@/app/Pages/Connects/ScheduleReports/Index.vue';
// Old imports - commented out
// import IconButton from '@/components/New/Blocks/Buttons/IconButton.vue';
// import CustomTable from '@/components/New/Blocks/CustomTable/CustomTable.vue';

// New imports using Common components
import CommonIconButton from '@/app/Common/CommonIconButton.vue';
import CommonTable from '@/app/Common/CommonTable.vue';

export default {
  name: "ReportHistory",
  components: {
    // Old components - commented out
    // CustomTable,
    // IconButton,

    // New components
    CommonTable,
    CommonIconButton,
    ScheduleReports
  },
  data() {
    return {
      isLoading: false,
      fields: [
        { key: 'scheduled', label: 'SCHEDULED', class: 'py-4' },
        { key: 'method', label: 'METHOD', class: 'py-4' },
        { key: 'inclusion', label: 'INCLUSION', class: 'py-4' },
        { key: 'sites', label: 'SITES', headerClass: 'w-20', class: 'py-4' },
        { key: 'customer', label: 'CUSTOMER', class: 'py-4' },
        { key: 'action', label: 'ACTION', class: 'py-4' }
      ],
      items: [
        {
          id: 1,
          scheduled: '23/11/2023',
          method: 'Automatically',
          inclusion: ['Summary', 'Performance', 'Summary', 'Performance'],
          sites: 'instawp.com, instawp.com',
          customer: ['/images/ConnectSiteUpdate/Avatar-1.svg', '/images/ConnectSiteUpdate/Avatar-1.svg', '/images/ConnectSiteUpdate/Avatar-1.svg'],
          action: 'EyeIcon'
        },
        {
          id: 2,
          scheduled: '23/11/2023',
          method: 'Manually',
          inclusion: ['Uptime History', 'Security Scan', 'Activity Logs'],
          sites: 'instawp.com, instawp.com',
          customer: ['/images/ConnectSiteUpdate/Avatar-1.svg', '/images/ConnectSiteUpdate/Avatar-1.svg', '/images/ConnectSiteUpdate/Avatar-1.svg'],
          action: 'EyeIcon'
        }
      ]
    };
  },
  methods: {
    getInclusionClass(inclusion) {
      const classes = {
        'Summary': 'bg-purpleCust-360 text-purpleCust-1800',
        'Performance': 'bg-blueCust-2000 text-blueCust-1950',
        'Uptime History': 'bg-primary-350 text-primary-400',
        'Security Scan': 'bg-pinkCust-1200 text-pinkCust-1300',
        'Activity Logs': 'bg-blueCust-2100 text-blueCust-2200'
      };

      return classes[inclusion] || 'bg-purpleCust-360 text-purpleCust-1800';
    },
    onSortChanged(sortBy, sortDesc) {
      // Handle table sorting
      console.log('Sort changed:', sortBy, sortDesc);
    },
    onRowClicked(item) {
      // Handle row click
      console.log('Row clicked:', item);
    }
  }

}
</script>

<style></style>