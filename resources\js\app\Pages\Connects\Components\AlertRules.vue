<template>
  <Head>
    <title>Alert Rules - {{ setting.brandShortName }}</title>
  </Head>
  <div>
    <ConnectLayout
      :connect="$props.connect"
      :title="$t('alert_rules')"
      :sub-title="$t('alert_rules_description')"
    >
      <template #right>
        <div
          v-if="isActivityLogsEnabled"
          class="flex items-center gap-4 justify-end"
        >
          <div class="flex items-center gap-3">
            <CommonSwitch
              v-model="isActivityLogsEnabled"
              v-tooltip="{ text: $t('switch_off_activity_logs'), position: 'left' }"
            />
            <CButton
              :btn-title="$t('new_alert')"
              icon-name="OutlinePlusIcon"
              btn-type="gray-outline-btn"
              @click="addAlert"
            />
          </div>
        </div>
      </template>

      <div
        v-if="!isActivityLogsEnabled"
        class="min-h-[400px] flex flex-col justify-center card-main"
      >
        <div class="flex justify-center items-center h-[99px] w-[99px] mx-auto">
          <img
            v-lazy="cdn('images/ActivityLog/searching.gif')"
            class="w-full h-full"
            alt=""
          >
        </div>
        <div
          class="flex justify-center mt-6 items-center gap-2 py-3 px-4 rounded-lg border border-secondary-900 bg-warning-1170 w-fit mx-auto"
        >
          <CommonSwitch
            v-model="isActivityLogsEnabled"
            :disabled="isSwitchDisabled"
            @open-disabled-modal="showDisabledNotice"
          />
          <span class="text-sm font-medium text-grayCust-700">
            {{ $t('enable_activity_logs') }}
          </span>
        </div>
        <div class="flex flex-col items-center mt-3 space-y-2">
          <span class="text-sm font-medium text-grayCust-660 text-center">
            {{ $t('enable_activity_logs_description') }}
          </span>
          <span class="text-sm font-medium text-grayCust-660 text-center">
            {{ $t('do_you_wish_to_enable_it') }}
          </span>
        </div>
      </div>
      <CommonLoader
        v-else-if="initialLoading"
        class="py-24 card-main"
      />
      <EmptyState
        v-else-if="alertRulesList.length === 0"
        :title="$t('no_alert_rules')"
        :sub-title="$t('no_alert_rules_description')"
        class="py-24 card-main"
      >
        <template #mainIcon>
          <svg
            class="mx-auto size-12 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
            />
          </svg>
        </template>
        <template #action>
          <div class="flex flex-col items-center justify-center gap-3">
            <CButton
              icon-name="OutlinePlusIcon"
              :btn-title="$t('new_alert')"
              btn-type="gray-outline-btn"
              @click="addAlert()"
            />
            <Switch
              v-model="isActivityLogsEnabled"
              v-tooltip="{ text: $t('switch_off_activity_logs'), position: 'left' }"
              type="primary"
            />
          </div>
        </template>
      </EmptyState>
      <div
        v-else
        class="space-y-6"
      >
        <CTable
          :fields="tableHeaderData"
          :items="{ data: alertRulesList }"
          :responsive="true"
          :busy="initialLoading"
          row-bordered
          row-height="lg:h-[40px]"
          header-height="lg:h-10"
          bordered
          border-rounded
        >
          <template #cell_action_type="{ item }">
            <div class="flex items-center gap-2">
              <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
                {{ $t('action') }}:
              </p>
              <div
                v-tooltip="item.display_name"
                class="cursor-pointer truncate text-sm font-semibold text-grayCust-980"
              >
                {{ item.display_name }}
              </div>
            </div>
          </template>
          <template #cell_severity="{ item }">
            <div class="flex items-center justify-start gap-2">
              <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
                {{ $t('severity') }}:
              </p>
              <div>
                <label
                  class="rounded-full px-3 py-0.5 text-xs font-medium capitalize"
                  :class="item.severity === 'critical' ? 'bg-redCust-110 text-pinkCust-150' : item.severity === 'high' ? 'bg-warning-350 text-warning-650' : item.severity === 'medium' ? 'bg-primary-350 text-primary-400' : 'bg-grayCust-160 text-grayCust-800'"
                >
                  {{ item.severity }}
                </label>
              </div>
            </div>
          </template>
          <template #cell_send_to="{ item }">
            <div class="flex items-center justify-start gap-2">
              <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
                {{ $t('send_to') }}:
              </p>
              <div class="relative flex w-full gap-3">
                <div
                  v-for="trigger in item.triggers"
                  :key="item.id + trigger.id"
                  v-tooltip="trigger.integration_provider?.name"
                >
                  <!-- <img v-lazy="cdn(`images/ActivityLog/${image.imageName}`)" alt=""> -->
                  <img
                    v-lazy="getLogo(trigger)"
                    class="size-5"
                    :alt="trigger.integration_provider?.name"
                  >
                </div>
              </div>
            </div>
          </template>
          <template #cell_status="{ item }">
            <div class="flex items-center gap-2">
              <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
                {{ $t('status') }}:
              </p>
              <CommonSwitch
                v-model="item.triggers[0].status"
                @click="updateAlertRuleStatus(item.id, !item.triggers[0].status)"
              />
            </div>
          </template>
          <template #cell_action="{ item }">
            <CommonActionButtonGroup>
              <IconButton
                v-tooltip="$t('view_more')"
                icon-name="OutlineExternalLinkIcon"
                btn-type="gray-outline-btn"
              />
              <IconButton
                v-tooltip="$t('delete')"
                icon-name="OutlineTrashIcon"
                btn-type="danger-outline-btn"
                icon-extra-class="text-redCust-730"
                @click="deleteAlertRule(item.id)"
              />
            </CommonActionButtonGroup>
          </template>
        </CTable>
        <CommonPagination
          v-if="!initialLoading && alertRulesList.length > 0 && pagination.lastPage > 1"
          v-model:current-page="pagination.currentPage"
          v-model:per-page="pagination.perPage"
          :total="pagination.total"
          :resource-name="$t('alert_rules')"
          @page-change="getAlertRules"
          @per-page-change="handlePerPageChange"
        />
      </div>
      <advance-connect-site-modal
        :is-open="showAdvanceConnectModal"
        :plan-data="$props.planData"
        :connect="$props.connect"
        @refresh-connects="handleRefreshConnects"
        @close-modal="showAdvanceConnectModal = false"
      />
      <alert-model
        :is-alert="isAlert"
        :connect="$props.connect"
        :severity-list="severityList"
        :channel-list="$props.integrationProviders"
        :action-list="actionList"
        @close="closeCreateAlert"
      />
    </ConnectLayout>
  </div>
</template>

<script>
import CommonPagination from "@/app/Common/CommonPagination.vue";
import EmptyState from '@/app/Pages/Connects/Components/EmptyState.vue';
import CommonLoader from '@/app/Common/CommonLoader.vue';
import ConnectLayout from '@/app/Pages/Connects/ConnectLayout.vue';
import { API_SERVER_URL } from '@/const';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans, wTrans } from 'laravel-vue-i18n';
import { mapStores } from 'pinia';
import { defineAsyncComponent } from 'vue';

const AdvanceConnectSiteModal = defineAsyncComponent(() => import('@/components/Connect/AdvanceConnectSiteModal/Index.vue'));
const AlertModel = defineAsyncComponent(() => import('@/app/Pages/Connects/Components/AlertModel.vue'));

export default {
  name: "ConnectsAlertRules",
  components: {
    AdvanceConnectSiteModal,
    ConnectLayout,
    AlertModel,
    EmptyState,
    CommonLoader,
    CommonPagination,
  },
  props: {
    integrationProviders: {
      type: Array,
      required: true
    },
    connect: {
      type: Object,
      required: true,
    },
    pluginAndThemeUpdateAvailableCount: {
      type: Object,
      required: true
    },
    planData: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      setting: new Setting(),
      activeTabName: "AlertRules",
      isAdvanced: this.$props.connect.advanced ?? false,
      isActivityLogsEnabled: this.$props.connect.activity_logs ?? false,
      watchActivityLogsEnabled: true,
      showAdvanceConnectModal: false,
      isAlert: false,
      alertRulesList: [],
      tableHeaderData: [
        {
          key: 'action_type',
          label: wTrans('action_type'),
          //headerClass: 'w-1/2'
        },
        {
          key: 'severity',
          label: wTrans('severity')
        },
        {
          key: 'send_to',
          label: wTrans('send_to')
        },
        {
          key: 'status',
          label: wTrans('status')
        },
        {
          key: 'action',
          label: wTrans('action')
        },
      ],
      severityList: [
        {
          id: 1,
          name: 'Critical',
        },
        {
          id: 2,
          name: 'High',
        },
        {
          id: 3,
          name: 'Low',
        },
      ],
      actionList: [
      ],
      channelList: [],
      pagination: {
        perPage: '10', // String to match with dropdown values
        currentPage: 1,
        lastPage: 0,
        total: 0
      },
      selectedBaseOption: {},
      baseList: [
        {
          id: 1,
          name: '5',
        },
        {
          id: 2,
          name: '10',
        },
        {
          id: 3,
          name: '15',
        },
      ],
      initialLoading: true
    }
  },
  computed: {
    ...mapStores(useAppStore),
    isActivityLogsEnabledForPlan() {
      return this.$props.connect.plan_features.activity_logs ?? false;
    },
    isSwitchDisabled() {
      return !this.isAdvanced || !this.isActivityLogsEnabledForPlan;
    }
  },
  watch: {
    isActivityLogsEnabled(newValue, oldValue) {
      if (newValue !== oldValue && this.watchActivityLogsEnabled) {
        this.handleSwitchChange(newValue, oldValue);
      };
    }
  },
  created() {
    if (this.isActivityLogsEnabled) {
      this.getActivity();
      this.getAlertRules();
    }
  },
  methods: {
    addAlert() {
      this.isAlert = true;
    },
    closeCreateAlert(data) {
      this.isAlert = false;
      if (data) {
        this.getActivity();
        this.getAlertRules();
      }
    },
    getLogo(trigger) {
      const integration = this.integrationProviders.find(integration => integration.id === trigger.integration_provider_id);
      if (integration.logo_url) {
        return integration.logo_url;
      }
      return this.cdn(`images/ActivityLog/slack-icon.svg`);
    },
    setPagination(data) {
      this.pagination.total = data.total;
      this.pagination.lastPage = data.last_page;
      this.pagination.currentPage = data.current_page;
      if (data.per_page) {
        this.pagination.perPage = data.per_page.toString();
      }
    },
    handlePerPageChange(perPage) {
      this.pagination.perPage = perPage;
      this.pagination.currentPage = 1;
      this.getAlertRules();
    },
    getActivity() {
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.get(API_SERVER_URL + '/api/user/remaining/wordpress-activities?connect_id=' + this.$props.connect.id, { headers })
        .then(response => {
          if (response.data) {
            this.actionList = response.data.data.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
        })
    },
    getAlertRules(page = 1) {
      if (page && Number.isInteger(page)) {
        this.pagination.currentPage = page
      }
      const params = {
        page: this.pagination.currentPage,
        per_page: this.pagination.perPage,
        connect_id: this.$props.connect.id,
      }
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.get(API_SERVER_URL + '/api/wordpress-activity-triggers', { headers, params })
        .then(response => {
          if (response.data) {
            this.alertRulesList = response.data.data.data;

            this.alertRulesList.forEach(action => {
              action.triggers.forEach(trigger => {
                trigger.integration_provider = this.integrationProviders.find(integration => integration.id === trigger.integration_provider_id);
              });
            });
            this.setPagination(response.data.data)
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.initialLoading = false;
        })
    },
    updateAlertRuleStatus(id, status) {
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.post(API_SERVER_URL + '/api/wordpress-activities/' + id + '/change-status', { status }, { headers })
        .then(() => {
          this.alertRulesList.find(alert => alert.id === id).triggers.forEach(trigger => {
            trigger.status = status;
          });
        })
        .catch(error => {
          console.log(error);
        })
    },
    deleteAlertRule(id) {
      const headers = { Authorization: `Bearer ${this.$page.props.auth.user.token}` };
      axios.delete(API_SERVER_URL + '/api/wordpress-activity-triggers/' + id, { headers })
        .then(() => {
          const notification = {
            heading: trans('success'),
            subHeading: trans('alert_rule_deleted_successfully'),
            type: "success",
          };
          this.appStore.setNotification(notification);
          this.getAlertRules();
          this.getActivity();
        })
        .catch(error => {
          console.log(error);
        })
    },
    handleSwitchChange(value, oldValue) {
      this.watchActivityLogsEnabled = false;
      axios.post(`/api/v2/connects/${this.$props.connect.id}/features`, {
        activity_logs: value
      }).then(res => {
        if (res.data.status) {
          if (value) {
            this.getActivity();
            this.getAlertRules();
          }
          this.appStore.setNotification({
            heading: trans('success'),
            subHeading: res.data.message,
            type: "success",
          });
        }
      }).catch(() => {
        this.isActivityLogsEnabled = oldValue;
      }).finally(() => {
        this.watchActivityLogsEnabled = true;
      });
    },
    handleRefreshConnects() {
      this.$inertia.reload({
        only: ['connect'],
        onSuccess: () => {
          if (this.isActivityLogsEnabledForPlan) {
            this.isActivityLogsEnabled = true;
          } else {
            this.appStore.setNotification({
              heading: trans('error'),
              subHeading: trans('not_available_in_your_plan'),
              type: "error",
            });
          }
          this.isAdvanced = true;
          this.showAdvanceConnectModal = false;
        }
      });
    },
    showDisabledNotice() {
      if (!this.isAdvanced) {
        this.showAdvanceConnectModal = true;
        return;
      }
      if (!this.activityLogsEnabledForPlan) {
        this.appStore.setNotification({
          heading: trans('error'),
          subHeading: trans('not_available_in_your_plan'),
          type: "error",
        });
      }
    }
  }
};
</script>