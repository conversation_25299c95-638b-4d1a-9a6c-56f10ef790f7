<template>
  <div
    @dragleave="siteId = null"
  >
    <div class="">
      <div class="">
        <div
          style="display: none"
          v-bind="getRootProps()"
        >
          <input v-bind="getInputProps()">
          <p v-if="isDragActive">
            {{ $t('drop_the_file') }}
          </p>
          <p v-else>
            {{ $t('select_file') }}
          </p>
        </div>

        <div
          v-if="bulkSelection.length"
          class="px-4 py-2.5 flex flex-wrap gap-y-2 gap-x-4 justify-between transition-all duration-150 bg-white border-b border-grayCust-160"
        >
          <div class="bg-grayCust-100 w-full py-1.5 px-2.5 rounded-md flex justify-between flex-wrap gap-y-3">
            <CommonPopover>
              <template #popoverButton>
                <CButton
                  id="bulk_actions_btn"
                  btn-title="Bulk Actions"
                  btn-type="gray-outline-btn"
                  icon-name="OutlineChevronDownIcon"
                  icon-position="last"
                />
              </template>
              <template #popoverOptions>
                <CommonMenuItem
                  id="run_commands_bulk"
                  href="#"
                  label="Run Commands"
                  icon="OutlineCodeIcon"
                  @click="toggleCommandModal(site, true)"
                />
                <CommonMenuItem
                  id="delete_sites_bulk"
                  text-class="text-redCust-800"
                  href="#"
                  label="Delete"
                  icon="OutlineTrashIcon"
                  @click="bulkAction('bulk_delete')"
                />
              </template>
            </CommonPopover>
            <div class="flex flex-wrap items-center gap-4">
              <div class="text-grayCust-500 text-sm">
                {{ $tChoice('number_sites_selected', bulkSelection.length) }}
              </div>
              <div
                class="border-l border-grayCust-350 pl-4 text-grayCust-980 font-medium text-sm cursor-pointer"
                @click="deselectAllSites"
              >
                {{ $t('deselect_all') }}
              </div>
              <div
                v-if="sites.length > 1 && bulkSelection.length < sites.length"
                class="border-l border-grayCust-350 pl-4 text-secondary-800 font-medium text-sm cursor-pointer"
                @click="selectAllSites"
              >
                {{ $tChoice('select_all_number_sites', sites.length) }}
              </div>
            </div>
          </div>
        </div>
        <!-- <div
          v-if="site_count"
          class="hidden"
        >
          <multi-select-dropdown
            ref="multiSelect"
            :tags="tagItems"
          />
        </div> -->
        <div v-if="site_count">
          <CommonTable
            :fields="tableFields"
            :items="{ data: sites }"
            :busy="isSitesLoading"
            :show-empty="false"
            row-bordered
            :select-table="true"
            :responsive="true"
            :tr-class="getRowClass(sites)"
            @selection-changed="handleSelectionChange"
          >
            <template #head_name>
              <div class="flex items-center gap-2">
                <span class="text-grayCust-500 px-0">Site Name</span>
              </div>
            </template>
            <template #cell_name="{ item }">
              <TableLinkLabel
                :id="'sites_link_'+item.id"
                :label="item.label 
                  || item.sub_domain 
                  || item.url?.replace(/^(https?:\/\/)/, '')"
                :extra-class="checkSiteExpire(item) === 'Site Expired' ? '!cursor-not-allowed opacity-50' : ''"
                :href="checkSiteExpire(item) !== 'Site Expired' 
                  ? route('sites.edit.staging.dashboard', { site: item.id, tab: tab.id }) 
                  : ''"
              >
                <template #label-icons>
                  <a
                    :href="'https://' + item.sub_domain"
                    target="_blank"
                    @click="addSiteOpenEvent(item.id)"
                  >
                    <OutlineExternalLinkIcon
                      class="w-4 h-4 text-grayCust-550 cursor-pointer"
                      :class="{ '!cursor-not-allowed opacity-50': item.expiryType === 'Expired' }"
                    />
                  </a>
                  <a
                    v-if="atarimIntegration && (!(!item.is_reserved && checkSiteExpire(item) == 'Site Expired'))"
                    :href="getAtarimLink('https://' + item.sub_domain)"
                    target="_blank"
                  >
                    <img
                      :src="cdn('images/integration/atrim.svg')"
                      alt="Atarim"
                      class="w-4 h-4"
                      :class="{ '!cursor-not-allowed opacity-50': item.expiryType === 'Expired' }"
                    >
                  </a>

                  <OutlineMenuIcon
                    v-if="item.description !== '-'"
                    v-tooltip="item.description"
                    class="min-w-4 w-4 h-4 min-h-4 text-grayCust-550"
                  />
                </template>
                <template
                  v-if="user.id != item.user_id || item.template"
                  #bottom-text
                >
                  <div
                    class="flex items-center gap-1.5 mt-1.5"
                  >
                    <p
                      v-if="user.id != item.user_id"
                      :title="item.user.name"
                      class="flex items-center gap-1"
                      data-bs-placement="bottom"
                      data-bs-toggle="tooltip"
                    >
                      <OutlineUserIcon class="w-4 h-4 text-grayCust-550" />
                      <!-- :class="[checkSiteExpire(item) == 'Site Expired' ? 'text-grayCust-370' : 'text-grayCust-600']" -->
                      <span
                        class="cursor-pointer"
                      >{{ item.user.name }}</span>
                    </p>

                    <p
                      v-if="item.template"
                      :class="user.id != item.user_id ? 'ml-4' : ''"
                      class="flex items-center gap-1"
                    >
                      <!-- <TemplateIcon :fill-color="checkSiteExpire(item) == 'Site Expired' ? '#BDBDBD' : '#4B5563'" /> -->
                      <OutlineViewGridIcon class="w-4 h-4 text-grayCust-550" />

                      <!-- :class="checkSiteExpire(item) == 'Site Expired' ? 'text-grayCust-370' : 'text-grayCust-600 '" -->
                      <span
                        id="dsh_template_name"
                        class="cursor-pointer"
                      >{{
                        item.template.name }}</span>
                    </p>
                  </div>
                </template>
              </TableLinkLabel>
            </template>
            <template #cell_expiry="{ item }">
              <div class="flex items-center gap-2 text-xs text-gray-500 lg:text-sm">
                <template v-if="item.is_reserved">
                  <div
                    class="flex flex-col items-center bg-primary-50 text-gray-700  px-3 py-1.5 rounded-md w-fit"
                  >
                    <div class="flex items-center gap-1 text-sm font-medium">
                      <CommonIcon
                        image-path="/images/reserved_flag.svg"
                        class="w-4 h-4"
                      />
                      <span>{{ $t('permanent') }} </span>
                    </div>
                  </div>
                  <!-- <SiteReservedIcon />
                  <span class="whitespace-nowrap">{{ $t('permanent') }} </span> -->
                </template>
                <template v-else>
                  <template v-if="checkSiteExpire(item) == 'Site Expired' && !item.suspended_reason">
                    <div
                      v-tooltip="$t('site_will_be_deleted_after') + '\n' + item.deleted_at_format"
                      class="flex flex-col items-center bg-red-100 text-red-700 px-3 py-1.5 rounded-md w-fit"
                    >
                      <div class="flex items-center gap-1 text-sm font-medium">
                        <CommonIcon
                          image-path="/images/expired.svg"
                          class="w-4 h-4"
                        />
                        <span>{{ $t('expired') }}</span>
                      </div>
                      <div
                        class="w-full bg-red-200 rounded mt-1"
                        :class="{ 'text-redCust-800': checkSiteExpire(item) === 'Site Expired' }"
                      >
                        <ProgressBar
                          progress-height="!h-0.5"
                          :progress-bar-class="[checkSiteExpire(item) === 'Site Expired' ? 'bg-redCust-500' : expiryPercentage(item) > 60 ? 'bg-redCust-500' : 'bg-secondary-800']"
                          :progress="checkSiteExpire(item) === 'Site Expired' ? 100 : expiryPercentage(item)"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-else-if="item.suspended_reason && checkSiteExpire(item) === 'Site Expired'">
                    <div
                      v-tooltip="item.suspended_reason"
                      class="flex flex-col items-center bg-red-100 text-red-700 px-3 py-1.5 rounded-md w-fit"
                    >
                      <div class="flex items-center gap-1 text-sm font-medium">
                        <CommonIcon
                          image-path="/images/expired.svg"
                          class="w-4 h-4"
                        />
                        <span>{{ $t('site_suspended') }}</span>
                      </div>
                      <div
                        class="w-full bg-red-200 rounded mt-1 text-redCust-800"
                      >
                        <ProgressBar
                          progress-height="!h-0.5"
                          :progress-bar-class="['bg-redCust-500']"
                          :progress="100"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div
                      v-tooltip="checkSiteExpire(item)"
                      class="flex flex-col items-center bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md w-fit cursor-pointer"
                      @click="openExpiredAt(item)"
                    >
                      <div
                        class="flex items-center gap-1 text-sm font-medium"
                      >
                        <CommonIcon
                          image-path="/images/temporary.svg"
                          class="w-4 h-4"
                        />
                        <span>{{ $t('temporary') }}</span>
                      </div>
                      <div
                        
                        class="w-full bg-red-200 rounded mt-1"
                        :class="{ 'text-redCust-800': checkSiteExpire(item) === 'Site Expired' }"
                      >
                        <ProgressBar
                          progress-height="!h-0.5"
                          :progress-bar-class="[expiryPercentage(item) > 60 ? 'bg-redCust-500' : 'bg-secondary-800']"
                          :progress="expiryPercentage(item)"
                        />
                      </div>
                      <!-- </div>
                    <ProgressCircle
                      size="circle-xs"
                      :progress="expiryPercentage(item)"
                      :stroke-width="15"
                      :progress-color="expiryPercentage(item) > 60 ? 'red' : 'var(--primary)'"
                    /> -->
                      <!-- <div
                      v-tooltip="checkSiteExpire(item)"
                      class="cursor-pointer"
                      @click="openExpiredAt(item)"
                    >
                      <div
                        :class="checkSiteExpire(item) == 'Site Expired' ? 'text-redCust-800' : ''"
                        class="clock-text truncate"
                      >
                        {{ checkSiteExpire(item) }}
                      </div>
                    </div> -->
                    </div>
                  </template>
                  <div
                    v-if="checkSiteExpire(item) == 'Site Expired'"
                    class="has-tooltip ml-1 cursor-pointer"
                  >
                    <span
                      v-if="item.status == 6"
                      class="tooltip -mt-8 hidden rounded bg-gray-100 p-1 text-red-500 shadow-lg md:block"
                    >
                      {{ $t('site_will_not_delete') }}
                    </span>
                    <span
                      v-else
                      class="tooltip -mt-8 hidden rounded bg-gray-100 p-1 text-red-500 shadow-lg md:block"
                    >{{
                       $t('will_be_deleted_after')
                     }}
                      {{ item.deleted_at_format }}
                      <DeletedSiteInfoIcon />
                    </span>
                  </div>
                </template>
              </div>
            </template>
            <template #cell_space="{ item }">
              <div
                :class="checkSiteExpire(item) == 'Site Expired' ? 'text-grayCust-370' : ''"
                class="table-text-muted"
              >
                {{
                  item.disk_usage_mb != 0
                    ? `${item.disk_usage_mb} MB`
                    : "NA"
                }}
              </div>
              <span
                :class="{ 'cursor-not-allowed opacity-50': item.expiryType === 'Expired' }"
                class="table-text-muted whitespace-nowrap"
              >{{ item.space }}</span>
            </template>

            <!-- Tag Start-->
            <template #cell_tags="{ item }">
              <div
                :class="{ '!cursor-not-allowed opacity-50': item.expiryType === 'Expired' }"
                class="flex items-center justify-start gap-1.5 flex-wrap w-fit"
              >
                <!-- v-for="(tag, index) in item.tags.slice(0, 2)" -->
                <Tag
                  v-for="selectedTags in tagItems.filter((tagItem) => item.tag_id_array.indexOf(tagItem.id) != -1).slice(0, 2)"
                  :key="selectedTags.id + Math.random()"
                  :text="selectedTags.name"
                  :bg-color="selectedTags.color_code + '1a'"
                  :color="selectedTags.color_code"
                />
                <!-- <div
                  v-if="showAllTags && showAllTags == item.id && item.tag_id_array.length > 2"
                  class="flex items-center gap-1.5 flex-wrap"
                > -->
                
                <Tag
                  v-for="selectedTag2 in tagItems.filter((tagItem) => item.tag_id_array.includes(tagItem.id)).slice(2)"
                  v-if="showAllTags && showAllTags == item.id && item.tag_id_array.length > 2"
                  :key="selectedTag2.id + Math.random()"
                  :text="selectedTag2.name"
                  :bg-color="selectedTag2.color_code + '1a'"
                  :color="selectedTag2.color_code"
                />
                <!-- </div> -->
                <Tag
                  v-if="item.tag_id_array.length > 2 && (showAllTags != item.id || !showAllTags)"
                  :key="item.id + Math.random()"
                  class="cursor-pointer"
                  :text="item.tag_id_array.length - 2"
                  type="gray"
                  icon-left="OutlinePlusIcon"
                  @click="showAllTags = item.id"
                />
                <TagDropdown
                  ref="tagDropdown"
                  :key="item.id"
                  :disabled="item.expiryType === 'Expired'"
                  :tags="tagItems"
                  :site-id="item.id"
                  :selected-values="item.tag_id_array || []"
                  @new-tag="createCustomTag"
                  @checked="syncTags"
                >
                  <template #popoverButton>
                    <Tag
                      v-if="item.tag_id_array.length !== 0 || item.tag_id_array.length === 0"
                      :key="item.id + Math.random()"
                      type="gray"
                      icon-left="OutlinePlusIcon"
                      :text="item.tag_id_array.length === 0 ? 'Add Tag' : ''"
                      @click="toggleSiteTags(item)"
                    />
                  </template>
                </TagDropdown>
              </div>
            </template>
            <template #cell_action="{ item }">
              <div class="flex items-center text-sm text-gray-500 justify-center">
                <ActionButtonGroup
                  v-if="
                    item.is_reserved ||
                      checkSiteExpire(item) != 'Site Expired'"
                  class="action-btn-shadow  inline-flex rounded-md"
                >
                  <IconButton
                    :id="sitePrefix + 'magic_login_' + item.id"
                    v-tooltip="$t('magic_login')"
                    image-path="/images/Login.svg"
                    btn-type="gray-outline-btn"
                    size="btn-md"
                    @click="autoLogin(item)"
                  />
                  <IconButton
                    v-if="$page.props.auth.user.id == item.user_id || $page.props.auth.user.current_team?.user_role?.key === 'owner' || $page.props.auth.user.current_team?.user_role?.key === 'admin'"
                    :id="sitePrefix + (item.is_reserved ? 'unreserve_' : 'reserve_') + item.id"
                    v-tooltip="item.is_reserved ? $t('unreserve') : $t('reserve')"
                    :icon-name="item.is_reserved ? 'SolidFlagIcon' : 'OutlineFlagIcon'"
                    btn-type="gray-outline-btn"
                    :icon-color="item.is_reserved ? '!text-secondary-900' : ''"
                    size="btn-md"
                    @click="reserveSiteConfirm(item)"
                  />
                  <IconButton
                    :id="sitePrefix + 'save_template_' + item.id"
                    v-tooltip="$t('save_template_snapshot')"
                    image-path="/images/v2grid.svg"
                    btn-type="gray-outline-btn"
                    size="btn-md"
                    @click="saveAsTemplate(item)"
                  />
                  <IconButton
                    :id="sitePrefix + 'go_live_' + item.id"
                    v-tooltip="golive"
                    image-path="/images/rocket-gray.svg"
                    btn-type="gray-outline-btn"
                    size="btn-md"
                    @click="openGoLiveModal(item)"
                  />
                  <CommonPopover
                    auto-close
                    button-class="!p-0 rounded-lg overflow-hidden"
                    panel-class="max-h-[300px]"
                  >
                    <!-- panel-class="lg:left-auto lg:right-0 left-0" -->
                    <template #popoverButton>
                      <IconButton
                        :id="sitePrefix + 'more_actions_' + item.id"
                        v-tooltip="{ text: $t('more_actions'), position: 'left' }"
                        icon-name="OutlineDotsHorizontalIcon"
                        btn-type="gray-outline-btn"
                        size="btn-md"
                        extra-class="!rounded-lg"
                      />
                    </template>
                    <template #popoverOptions>
                      <CommonMenuItem
                        :id="sitePrefix + 'versions_' + item.id"
                        :label="$t('versions')"
                        icon="OutlineClockIcon"
                        @click="openNewVersion(item)"
                      />
                      <CommonMenuItem
                        :id="sitePrefix + 'php_config_' + item.id"
                        :label="$t('php_config')"
                        image="/images/Php-config-gray.svg"
                        @click="$inertia.get(route('sites.edit.php.config', { site: item.id }))"
                      />
                      <CommonMenuItem
                        :id="sitePrefix + 'login_details_' + item.id"
                        label="Login Details"
                        icon="SolidDocumentTextIcon"
                        @click="openViewCred(item)"
                      />
                      <CommonMenuItem
                        :id="sitePrefix + 'commands_' + item.id"
                        :label="$t('commands')"
                        icon="OutlineCodeIcon"
                        @click="redirectToCommandPage(item)"
                      />
                      <!-- <div class="relative submenu-trigger"> -->
                      <CommonMenuItem
                        :id="sitePrefix + 'export_as_' + item.id"
                        href="#"
                        label="Export As"
                        icon="OutlineDocumentDownloadIcon"
                        collapsed
                      >
                        <template #hover-content>
                          <CommonMenuItem
                            :id="sitePrefix + 'local_wp_' + item.id"
                            :label="$t('local_wp')"
                            :image="cdn('images/ConnectSiteUpdate/wordpress.svg')"
                            @click="exportSiteConfirm('local_wp',item)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'insta_wp_' + item.id"
                            :label="$t('instawp')"
                            :image="cdn('images/import_providers/instawp.svg')"
                            @click="exportSiteConfirm('insta_wp',item)"
                          /> 
                          <CommonMenuItem
                            :id="sitePrefix + 'wp_studio_' + item.id"
                            :label="$t('wordpress_studio')"
                            :image="cdn('images/ConnectSiteUpdate/wordpress.svg')"
                            @click="exportSiteConfirm('wp_studio',item)"
                          />
                        </template>
                      </CommonMenuItem>

                      <CommonMenuItem
                        :id="sitePrefix + 'integrations_' + item.id"
                        label="Integrations"
                        icon="OutlinePuzzleIcon"
                        collapsed
                      >
                        <template #hover-content>
                          <CommonMenuItem
                            v-if="$page.props.TESTING_ENV"
                            :id="sitePrefix + 'mail_chimp_' + item.id"
                            label="Mail Chimp"
                            image="/images/Site/featured-icon1.png"
                            @click="show = true"
                          />
                          <CommonMenuItem
                            v-if="$page.props.TESTING_ENV"
                            :id="sitePrefix + 'atarim_' + item.id"
                            label="Atarim"
                            image="/images/Site/featured-icon2.png"
                            @click="show = true"
                          />
                          <CommonMenuItem
                            v-if="$page.props.TESTING_ENV"
                            :id="sitePrefix + 'active_campaign_' + item.id"
                            label="Active Campaign"
                            image="/images/Site/featured-icon3.png"
                            @click="show = true"
                          />
                          <CommonMenuItem
                            v-if="isMailTrapIntegrated(item)"
                            :id="sitePrefix + 'mailtrap_' + item.id"
                            label="Mailtrap"
                            image="/images/Site/featured-icon4.png"
                            @click="promptRemoveMailTrapIntegration(item)"
                          />
                          <CommonMenuItem
                            v-else
                            :id="sitePrefix + 'mailtrap_' + item.id"
                            label="Mailtrap"
                            image="/images/Site/featured-icon4.png"
                            @click="promptMailTrapIntegration(item)"
                          />
                          <CommonMenuItem
                            v-if="$page.props.TESTING_ENV"
                            :id="sitePrefix + 'page_speed_insight_' + item.id"
                            label="Page Speed Insight"
                            image="/images/Site/featured-icon5.png"
                            @click="show = true"
                          />
                        </template>
                      </CommonMenuItem>
                      <CommonMenuItem
                        :id="sitePrefix + 'tools_' + item.id"
                        href="#"
                        :label="$t('tools')"
                        icon="OutlineAdjustmentsIcon"
                        collapsed
                      >
                        <template #hover-content>
                          <CommonMenuItem
                            :id="sitePrefix + 'db_editor_' + item.id"
                            :label="$t('db_editor')"
                            icon="SolidDatabaseIcon"
                            @click="openDb(item.id)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'view_log_' + item.id"
                            :label="$t('view_log')"
                            icon="OutlineExclamationCircleIcon"
                            @click="viewLog(item.id, item.cloud_type)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'code_editor_' + item.id"
                            :label="$t('code_editor')"
                            icon="OutlineCodeIcon"
                            @click="showCodeEditorDialog(item)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'password_protect_' + item.id"
                            :label="$t('password_protect')"
                            icon="SolidKeyIcon"
                            @click="openHtpassword(item)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'site_usage_' + item.id"
                            :label="$t('site_usage')"
                            icon="OutlineServerIcon"
                            @click="openSiteUsageSidebar(item)"
                          />
                          <CommonMenuItem
                            :id="sitePrefix + 'install_plugins_' + item.id"
                            :label="$t('install_plugins')"
                            icon="SolidPuzzleIcon"
                            @click="$inertia.get(route('sites.edit.install.content', { site: item.id }))"
                          />
                          <CommonMenuItem
                            v-if="!item.is_reserved && ($page.props.auth.user.id == item.user_id || $page.props.auth.user.current_team?.user_role?.key === 'owner' || $page.props.auth.user.current_team?.user_role?.key === 'admin')"
                            :label="$t('set_expiry_date')"
                            icon="SolidClockIcon"
                            @click="openExpiredAt(item)"
                          />
                        </template>
                      </CommonMenuItem>
                      <CommonMenuItem
                        v-if="item.cloud_type == 'instacloud'"
                        :id="sitePrefix + 'deployments_' + item.id"
                        :label="$t('deployments')"
                        image="/images/deployment.svg"
                        @click=" $inertia.get(route('sites.edit.git.deployment', { site: item.id }))"
                      />
                      <CommonMenuItem
                        v-if="item.cloud_type == 'instacloud'"
                        :id="sitePrefix + 'suffix_domain_' + item.id"
                        :label="$t('suffix_domain')"
                        icon="OutlineSwitchHorizontalIcon"
                        @click="changeDomain(item)"
                      />
                      <CommonMenuItem
                        v-if="!(item.hasOwnProperty('site_meta') && item.site_meta.hasOwnProperty('order_id') && item.site_meta?.order_id)"
                        :id="sitePrefix + 'clone_' + item.id"
                        :label="$t('clone')"
                        icon="SolidDuplicateIcon"
                        @click="cloneSite(item)"
                      />
                      <CommonMenuItem
                        v-if="($page.props.auth.user.id == item.user_id || $page.props.auth.user.current_team?.user_role?.key === 'owner' || $page.props.auth.user.current_team?.user_role?.key === 'admin')"
                        :id="sitePrefix + 'delete_' + item.id"
                        :label="$t('delete')"
                        icon="SolidTrashIcon"
                        @click="onDeleteSite(item)"
                      />
                      <CommonMenuItem
                        :id="sitePrefix + 'sftp_ssh_' + item.id"
                        :label="$t('sftp_ssh')"
                        icon="SolidTerminalIcon"
                        @click=" $inertia.get(route('sites.edit.sftp.ssh', { site: item.id }))"
                      />
                      <CommonMenuItem
                        :id="sitePrefix + 'map_domain_' + item.id"
                        :label="$t('map_domain')"
                        icon="OutlineGlobeAltIcon"
                        @click="$inertia.get(route('sites.edit.map.domain', { site: item.id }))"
                      />
                    </template>
                  </CommonPopover>
                </ActionButtonGroup>
                <ActionButtonGroup 
                  v-if="
                    !item.is_reserved &&
                      checkSiteExpire(item) === 'Site Expired'"
                >
                  <CButton
                    v-tooltip="$t('restore_sites')"
                    btn-title="Restore"
                    btn-type="gray-outline-btn"
                    @click="restoreSite(item.id)"
                  >
                    <template v-if="item.status == 3">
                      <CommonLoader />
                    </template>
                    <template v-else>
                      {{ $t('restore') }}
                    </template>
                  </CButton>
                  <IconButton
                    v-if="($page.props.auth.user.id == item.user_id || $page.props.auth.user.current_team?.user_role?.key === 'owner' || $page.props.auth.user.current_team?.user_role?.key === 'admin')"
                    v-tooltip="{ text: $t('delete'), position: 'left' }"
                    icon-name="OutlineTrashIcon"
                    icon-color="!text-red-500"
                    btn-type="gray-outline-btn"
                    size="btn-md"
                    @click="onDeleteSite(item)"
                  />
                </ActionButtonGroup>
              </div>
            </template>
          </CommonTable>
        </div>
        <div
          v-if="sites.length === 0 && site_count && !isSitesLoading "
          class="flex flex-col items-center justify-center mt-16"
        >
          <CommonIcon
            :image-path="'/images/TemplateSite/filter-icon.svg'"
          />

          <h3 class="mt-2 text-sm font-medium text-gray-900">
            {{ $t('no_result_found') }}
          </h3>
        </div>
        <div v-if="!site_count && !isSitesLoading">
          <EmptyState
            image-path="/images/staging.svg"
            :title="$t('you_dont_have_any_site')"
            :description="$t('start_building_or_hosting_wordpress_site_by_clicking_on_new_site')"
          >
            <template #actions>
              <div class="flex items-center gap-3">
                <CButton
                  :id="'import-site-btn'"
                  icon-name="OutlineDownloadIcon"
                  btn-title="Import Site"
                  btn-type="gray-outline-btn"
                  @click="$emit('ImportSitePopup')"
                />
                <CButton
                  :id="'new-staging-site-btn'"
                  icon-name="OutlinePlusIcon"
                  btn-title="New Site"
                  btn-type="secondary"
                  @click="openNewSiteModel()"
                />
              </div>
            </template>
          </EmptyState>
        </div>
      </div>
      <div
        class="my-4 px-4"
      >
        <CommonPagination
          v-if="sites.length > 0"
          v-model:current-page="pagination.currentPage"
          v-model:per-page="perPageValue"
          :total="pagination.total"
          resource-name="Staging Sites"
          @page-change="fetchData"
          @per-page-change="perPageItem"
        />
      </div>
    </div>

    <!-- :option-list="baseList" -->


    <!-- new version side bar design -->
    <new-version-side-bar
      ref="siteVersionsModal"
      :site="site"
      @close="site = {};"
    />
    <!-- new version side bar design -->

    <!-- commands modal design -->
    <TransitionRoot
      :show="isOpenCommandModal"
      as="template"
    >
      <Dialog
        as="div"
        class="relative z-10"
        @close="toggleCommandModal"
      >
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </TransitionChild>

        <div class="fixed inset-0 z-10 overflow-y-auto">
          <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center">
            <TransitionChild
              as="template"
              enter="ease-out duration-300"
              enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enter-to="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leave-from="opacity-100 translate-y-0 sm:scale-100"
              leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <DialogPanel
                class="relative overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transform transition-all sm:my-8 sm:w-full sm:p-6"
                style="max-width: 60rem"
              >
                <div class="mb-5 items-center justify-between border-b border-grayCust-900 pb-5 sm:flex">
                  <div class="flex flex-wrap items-center lg:flex-wrap">
                    <h2 class="text-xl font-semibold text-grayCust-800">
                      {{ $t('executing_commands_on') }}
                    </h2>
                    <a class="ml-1.5 cursor-pointer text-xl font-semibold text-primary-900">{{ executeCommandTitle }}</a>
                    <p
                      v-if="doExecuteCmdOnBulkSites && bulkSelection.length > 1"
                      class="ml-1.5 cursor-pointer text-xl font-semibold text-grayCust-500"
                    >
                      +
                      {{ bulkSelection.length - 1 }} {{ $t('more_sites') }}
                    </p>
                  </div>
                  <button
                    class="absolute right-6 top-6 focus:outline-none focus:ring-0 focus:ring-offset-0"
                    type="button"
                    @click="toggleCommandModal"
                  >
                    <svg
                      fill="none"
                      height="14"
                      viewBox="0 0 14 14"
                      width="14"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 13L13 1M1 1L13 13"
                        stroke="#111827"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                  </button>
                </div>
                <inertia-link
                  v-if="commandsList.length == 0"
                  :href="route('commands', { addNew: true })"
                  class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none"
                  type="button"
                  @click="showAddNewCommands = true"
                >
                  <svg
                    class="mx-auto size-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 28 28"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span class="mt-2 block text-sm font-medium text-gray-900">{{ $t("add_new_commands")
                  }}</span>
                </inertia-link>
                <div
                  v-else
                  class="flex flex-wrap items-end justify-between lg:flex-nowrap"
                >
                  <div class="search-input flex items-start">
                    <Listbox
                      v-model="selectedCommand"
                      as="div"
                      class="w-full"
                    >
                      <ListboxLabel class="block text-sm font-medium text-gray-700">
                        {{ $t('choose_command') }}
                      </ListboxLabel>
                      <div class="relative mt-1">
                        <ListboxButton
                          class="relative h-10 w-full cursor-pointer rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-primary-900 focus:outline-none focus:ring-1 focus:ring-primary-900 sm:text-sm"
                        >
                          <span
                            v-if="selectedCommand"
                            class="block truncate"
                          >{{ selectedCommand.name }}</span>
                          <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <svg
                              aria-hidden="true"
                              class="size-5 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                              x-description="Heroicon name: mini/chevron-up-down"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </span>
                        </ListboxButton>

                        <transition
                          leave-active-class="transition ease-in duration-100"
                          leave-from-class="opacity-100"
                          leave-to-class="opacity-0"
                        >
                          <ListboxOptions
                            class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                          >
                            <ListboxOption
                              v-for="item in commandsList"
                              :key="item.id"
                              v-slot="{ active, selectedCommand }"
                              :value="item"
                              as="template"
                            >
                              <li
                                :class="[active ? 'bg-primary-900 text-white' : 'text-gray-900', 'relative cursor-pointer select-none py-2 pl-3 pr-9']"
                              >
                                <span :class="[selectedCommand ? 'font-semibold' : 'font-normal', 'block truncate']">{{
                                  item.name
                                }}</span>
                              </li>
                            </ListboxOption>
                          </ListboxOptions>
                        </transition>
                      </div>
                    </Listbox>
                  </div>
                  <button
                    :disabled="executingCommand"
                    class="mt-3 flex items-center rounded-md bg-primary-900 px-5 py-2.5 text-sm font-medium text-white hover:bg-primary-200 lg:ml-2.5 lg:mt-0"
                    @click="checkCommandArgumentOrExecute(site)"
                  >
                    <div class="flex items-center">
                      <svg
                        v-if="executingCommand"
                        class="text-primary inline animate-spin"
                        fill="none"
                        height="14"
                        role="status"
                        viewBox="0 0 100 101"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                          fill="#E5E7EB"
                        />
                        <path
                          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                          fill="currentColor"
                        />
                      </svg>
                      <svg
                        v-else
                        fill="none"
                        height="14"
                        viewBox="0 0 18 14"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M7.5 13L10.5 1M13.5 4L16.5 7L13.5 10M4.5 10L1.5 7L4.5 4"
                          stroke="white"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1.5"
                        />
                      </svg>
                    </div>
                    <span class="ml-1">{{ $t('run_command') }}</span>
                  </button>
                </div>
                <div
                  v-if="commandsList.length > 0"
                  class="relative mt-6"
                >
                  <div class="relative">
                    <textarea
                      id="cmd_output"
                      v-model="commandOutput"
                      class="block w-full rounded-md bg-black p-4 text-white shadow-sm focus:border-black focus:ring-black sm:text-sm"
                      disabled
                      name="cmd_output"
                      placeholder="output of the command...."
                      rows="25"
                    />
                    <input
                      ref="cmdOutputTextArea"
                      :value="commandOutput"
                      class="absolute top-0 h-1 cursor-pointer p-0 opacity-0"
                      readonly
                      type="text"
                    >

                    <div>
                      <div
                        v-if="showCommandArgumentModal"
                        class="absolute left-2/4 top-2/4 z-50 w-full -translate-x-1/2 -translate-y-1/2 p-6"
                        style="max-height: 495px"
                      >
                        <div class="provide-modal site-table-shadow w-full rounded-t-lg bg-grayCust-50 p-6 pb-1">
                          <h4 class="text-sm font-medium text-grayCust-500">
                            {{ $t('provide_values_for_following_commands') }}
                          </h4>

                          <div
                            class="overflow-y-scroll pt-3.5"
                            style="max-height: 385px"
                          >
                            <div
                              v-for="(command, index) in commandArguments"
                              :key="index"
                              class="mb-3.5 rounded-lg bg-white p-4"
                            >
                              <h5 class="cursor-pointer text-sm font-medium text-warning-1300">
                                {{ command.title }}
                              </h5>
                              <div class="mt-3 flex flex-row flex-wrap items-center justify-between md:flex-wrap">
                                <div
                                  v-for="(item, i) in command.parameters"
                                  :key="i"
                                  class="mb-2 w-60 sm2:w-96 md:w-72 lg:w-96"
                                >
                                  <input
                                    v-model="item.replaceValue"
                                    :placeholder="item.searchValue"
                                    class="mr-6 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-900 focus:ring-primary-900 sm:mt-4 sm:text-sm md:mt-0"
                                    type="text"
                                  >
                                  <jet-input-error
                                    v-if="item.replaceValueError"
                                    :message="item.replaceValueError"
                                    class="mt-2"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="command-modal-footer flex items-center justify-end rounded-b-lg bg-white px-6 py-3">
                          <button
                            class="rounded-md border border-grayCust-350 bg-white px-4 py-2 text-sm font-medium text-grayCust-700 shadow-sm hover:bg-gray-100"
                            @click="showCommandArgumentModal = false;"
                          >
                            {{ $t('cancel') }}
                          </button>
                          <button
                            :disabled="executingCommand"
                            class="ml-4 rounded-md bg-primary-900 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-200"
                            @click="executeCommand(site)"
                          >
                            <svg
                              v-if="executingCommand"
                              class="text-primary inline animate-spin"
                              fill="none"
                              height="14"
                              role="status"
                              viewBox="0 0 100 101"
                              width="18"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="#E5E7EB"
                              />
                              <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentColor"
                              />
                            </svg>
                            <div v-else>
                              {{ $t('continue') }}
                            </div>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-tooltip="cmdOutputCopied ? $t('copied') : $t('copy')"
                    class="absolute bottom-6 right-6 cursor-pointer rounded-lg border border-grayCust-950 bg-grayCust-1600 p-3 text-white"
                    style="position: absolute;"
                    @click="copyCmdOutputToClipboard()"
                  >
                    <svg
                      v-if="cmdOutputCopied"
                      fill="currentColor"
                      height="18"
                      viewBox="0 0 20 20"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        fill-rule="evenodd"
                      />
                    </svg>
                    <svg
                      v-else
                      fill="none"
                      height="18"
                      viewBox="0 0 18 18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5 13H3C1.89543 13 1 12.1046 1 11V3C1 1.89543 1.89543 1 3 1H11C12.1046 1 13 1.89543 13 3V5M7 17H15C16.1046 17 17 16.1046 17 15V7C17 5.89543 16.1046 5 15 5H7C5.89543 5 5 5.89543 5 7V15C5 16.1046 5.89543 17 7 17Z"
                        stroke="white"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
    <!-- commands modal design -->

    <!-- view Log model -->
    <SiteLog
      ref="siteLogModal"
      :current-cloud-type-site="currentCloudTypeSite"
      :site_id="currentLogSite"
      @close="currentLogSite = false"
    />

    <change-suffix-domain
      ref="changeSuffixDomainModal"
      :is-mapped-domain="Boolean(site.domain)"
      :open="openSuffixDomain"
      :site-details="site"
      :suffix-domains="suffixDoamins"
      @close="site = {}"
      @refresh-table="fetchData"
    />
    <save-template
      ref="siteTemplateModal"
      mode="staging"
      :site_id="site_id"
      @close-template-form="site_id = false;"
      @refresh-table="fetchData"
    />

    <site-clone
      ref="siteCloneModal"
      :site_id="site_id"
      @close-clone-form="site_id = false; site = {}"
      @refresh-table="fetchData"
      @site-clone-done="site_id = false; site = {}"
    />

    <site-htpassword
      ref="siteHtpasswordModal"
      :site="site"
      @close="site = {}"
      @refresh-table="fetchData(); site = {}"
    />

    <SiteExpiredAt
      ref="siteExpiredAtModal"
      :site="site"
      @close="site = {}"
      @refresh-table="fetchData(); site = {}"
    />


    <!-- view cred dialog -->
    <view-cred-modal
      ref="viewCredModal"
      :site="site"
      @close="site = {};"
      @on-refresh-change="refresh = true"
    />
    <!-- view cred dialog over -->

    <!-- delete site dialog -->
    <site-delete-modal
      :show="showDeleteSiteModal"
      :site="siteDetail"
      @on-delete-site-modal-close="onDeleteSiteModalClose"
      @on-site-deletion="onSiteDeletion"
    />
    <site-bulk-delete-modal
      :bulk-selection="bulkSelection"
      :show="showBulkDeleteSiteModal"
      :sites="sites"
      @on-bulk-delete-site="onBulkDeleteSite"
      @on-bulk-delete-site-modal-close="onBulkDeleteSiteModalClose"
    />

    <site-disconnect-modal
      :show="showDisconnectSiteModal"
      :site="siteDetail"
      @on-disconnect-site="onDisconnectSite"
      @on-disconnect-site-modal-close="onDisconnectSiteModalClose"
    />

    <!-- delete site dialog over -->
    <!-- site push modal design -->
    <site-push-modal
      v-if="isOpenSitePushModal"
      :connected-sites="connectedSites"
      :connection_id="connection_id"
      :is-open-site-push-modal="isOpenSitePushModal"
      :parent_url="parent_url"
      :site-detail="siteDetail"
      :sitepushstatus="sitepushstatus"
      @close_site_push_modal="toggleSitePushModal"
      @close_site_push_modal_outside="isOpenSitePushModal = false"
      @fetch-connected-sites="getConnectedSites($event)"
      @site-push-connect="sitePushConnect($event)"
      @update-site-push-status="updateSitePushStatus($event)"
    />
    <!-- site push modal design -->

    <!-- Reserve site modal design -->
    <CommonModal
      v-model="confirmingReserveSite"
      size="2xl"
      position="top"
      extra-footer-class="bg-gray-100"
    >
      <template #content>
        <div class="flex items-center  space-x-4 mb-3">
          <CommonIcon
            :image-path="'/images/info-image.svg'"
            class="w-16 h-16"
          />
          <div>
            <h4 class="text-lg font-medium">
              {{ site.is_reserved ? $t('unreserve') : $t('reserve') }}
              {{ $t('site') }}
            </h4>
            <template v-if="!site.is_reserved">
              {{ $t('your_website') }}
              {{ site.sub_domain }} {{ $t('will_be_marked_as_reserved') }}
            </template>
            <template v-else-if="site.store_products_count">
              {{ $t('your_website') }}
              {{ site.sub_domain }} {{ $t('store_template_will_be_unmarked_as_reserved') }}
            </template>
            <template v-else>
              {{ $t('your_website') }}
              {{ site.sub_domain }} {{ $t('will_be_unmarked_as_reserved') }}
            </template>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <CButton
            :btn-title=" $t('cancel')"
            btn-type="gray-outline-btn"
            @click="confirmingReserveSite = false"
          />
          <CButton
            :btn-title=" $t('confirm')"
            btn-type="secondary"
            :disabled="reserveSiteProcessing"
            @click="reserveSite"
          />
        </div>
      </template>
    </CommonModal>

    <!-- end site modal design -->

    <!-- export site modal design -->
    <CommonModal
      v-model="confirmingExportSite"
      size="2xl"
      position="top"
      extra-footer-class="bg-gray-100"
    >
      -2
      <template #header>
        <h4 class="text-lg font-medium mb-2">
          {{ $t('' + (exportType == 'local_wp' ? 'site_export_for_localwp' : exportType == 'insta_wp' ? 'site_export_for_instawp' : exportType == 'wp_studio' ? 'site_export_for_wp_studio' : 'site_export')) }}
        </h4>
      </template>

      <template #content>
        <div class="flex  space-x-4 mb-3">
          <CommonIcon
            :image-path="'/images/info-image.svg'"
            class="w-16 h-16"
          />
          <div>
            <template v-if="true">
              {{ $t('are_you_sure_export') }}
              <strong>{{
                site.sub_domain
              }}</strong>
              {{ $t('' + (exportType == 'local_wp' ? 'site_for_localwp_will_create_zip_file' : exportType == 'insta_wp' ? 'site_for_instawp_will_create_zip_file' : exportType == 'wp_studio' ? 'site_for_wp_studio_will_create_zip_file' : 'site_will_create_zip_file')) }}
            </template>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <CButton
            :btn-title=" $t('cancel')"
            btn-type="gray-outline-btn"
            @click="
              confirmingExportSite = false;
              site = {};"
          />
          <CButton
            :btn-title=" $t('confirm')"
            btn-type="secondary"
            :disabled="exportSiteProcessing"
            @click="handleExport"
          />
        </div>
      </template>
    </CommonModal>

    <!-- upload-modal-design -->
    <MigrateModelVue
      :is-migrate-modal="isMigrateModal"
      @close-migrate-modal="toggleMigrateModal"
    />

    <AtarimModel
      :is-chat-modal="isChatModal"
      @close_chat_modal="toggleChatModal"
      @open_configure_modal="openConfigureModal"
    />
    <ConfigureModel
      :is-configure-modal="isConfigureModal"
      @close_configure_modal="toggleConfigureModal"
    />

    <SiteUsageSidebar
      :is-site-usage-modal-open="isSiteUsageModalOpen"
      :site="selectedSiteForSiteUsage"
      @close_site_usage_sidebar="closeSiteUsageSidebar"
    />
    <SelectMailTrapIntegration
      :show="showSelectMailTrapIntegrationModal"
      :site="selectedSiteForMailTrapIntegration"
      @close="onSelectMailTrapIntegrationClose"
    />
    <DeleteMailTrapIntegration
      :show="showDeleteMailTrapIntegrationModal"
      :site="selectedSiteForMailTrapDeletion"
      @close="onRemoveMailTrapIntegrationClose"
    />

    <SiteCodeEditorModal
      :show="showCodeEditorModal"
      :site="siteDetail"
      @on-cancel-modal="hideCodeEditorDialog"
    />
  </div>
</template>

<script>
import CommonMenuItem from '@/app/Common/ActionDropdown/CommonMenuItem.vue';
import ActionButtonGroup from '@/app/Common/CommonActionButtonGroup.vue';
import CButton from '@/app/Common/CommonButton.vue';
import CommonIcon from '@/app/Common/CommonIcon.vue';
import IconButton from '@/app/Common/CommonIconButton.vue';
import CommonLoader from "@/app/Common/CommonLoader.vue";
import CommonModal from '@/app/Common/CommonModal.vue';
import CommonPagination from "@/app/Common/CommonPagination.vue";
import CommonPopover from '@/app/Common/CommonPopover.vue';
import ProgressBar from '@/app/Common/CommonProgressBar.vue';
import CommonTable from "@/app/Common/CommonTable.vue";
import Tag from '@/app/Common/CommonTag.vue';
import EmptyState from '@/app/Components/EmptyState.vue';
import TableLinkLabel from '@/app/Components/TableLinkLabel.vue';
import TagDropdown from '@/app/Components/TagDropdown.vue';
import SaveTemplate from "@/app/Components/Template/SaveTemplate.vue";
import DeletedSiteInfoIcon from '@/app/Icons/DeletedSiteInfoIcon.vue';
import SiteExpiredIcon from '@/app/Icons/SiteExpiredIcon.vue';
import SiteReservedIcon from '@/app/Icons/SiteReservedIcon.vue';
import ChangeSuffixDomain from "@/components/ChangeSuffixDomain.vue";
import SiteBulkDeleteModal from '@/components/Dashboard/SiteBulkDeleteModal.vue';
import SiteDeleteModal from '@/components/Dashboard/SiteDeleteModal.vue';
import SiteDisconnectModal from '@/components/Dashboard/SiteDisconnectModal.vue';
import ViewCredModal from '@/components/Dashboard/ViewCredModal.vue';
import DeleteMailTrapIntegration from '@/components/DeleteMailTrapIntegrationModal.vue';
import MigrateModelVue from "@/components/MigrateModel.vue";
import ProgressCircle from '@/components/New/Blocks/ProgressCircle/ProgressCircle.vue';
import NewVersionSideBar from "@/components/NewVersionSideBar.vue";
import SelectMailTrapIntegration from '@/components/SelectMailTrapIntegrationModal.vue';
import SiteClone from "@/components/SiteClone.vue";
import SiteCodeEditorModal from '@/components/SiteCodeEditor/SiteCodeEditorModal.vue';
import SiteExpiredAt from "@/components/SiteExpiredAt.vue";
import SiteHtpassword from "@/components/SiteHtpassword.vue";
import AtarimModel from "@/components/SiteIntegration/AtarimModel.vue";
import ConfigureModel from "@/components/SiteIntegration/ConfigureModel.vue";
import SiteLog from "@/components/SiteLog.vue";
import SitePushModal from "@/components/SitePushModal.vue";
import SiteUsageSidebar from '@/components/SiteUsageSidebar.vue';
import MultiSelectDropdown from "@/components/UpdatedDesignVersion/MultiSelect/MultiSelectDropdown.vue";
import { ATARIM_APP_URL } from "@/const.js";
import { getExpiryPercentage, tagColours } from "@/helpers.js";
import JetInputError from '@/Jetstream/InputError.vue';
import { useAppStore } from '@/store/index';
import {
  Dialog,
  DialogPanel,
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
  TransitionChild,
  TransitionRoot
} from '@headlessui/vue';
import axios from "axios";
import { trans, wTrans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import { ref } from 'vue';
import { useDropzone } from "vue3-dropzone";
import { route } from 'ziggy-js';

export default {
  components: {
    CommonTable,
    Tag,
    TransitionRoot,
    TransitionChild,
    IconButton,
    DialogPanel,
    Dialog,
    EmptyState,
    MigrateModelVue,
    MultiSelectDropdown,
    CommonPagination,
    SiteLog,
    SaveTemplate,
    SiteClone,
    ChangeSuffixDomain,
    SiteHtpassword,
    NewVersionSideBar,
    Listbox,
    ListboxButton,
    ListboxLabel,
    ListboxOption,
    ListboxOptions,
    JetInputError,
    SitePushModal,
    ViewCredModal,
    SiteDeleteModal,
    SiteBulkDeleteModal,
    SiteUsageSidebar,
    SelectMailTrapIntegration,
    DeleteMailTrapIntegration,
    AtarimModel,
    ConfigureModel,
    SiteDisconnectModal,
    SiteCodeEditorModal,
    SiteExpiredAt,
    ProgressCircle,
    CommonIcon,
    SiteReservedIcon,
    SiteExpiredIcon,
    CButton,
    DeletedSiteInfoIcon,
    CommonMenuItem,
    ActionButtonGroup,
    TagDropdown,
    CommonPopover,
    CommonLoader,
    ProgressBar,
    TableLinkLabel,
    CommonModal
  },

  props: {
    site_count: {
      required: true
    },

    searchTerm: {
      default: '',
      type: String
    },

    filterTag: {
      type: Array,
      default: () => []
    },

    selectedTypeOption: {
      default: '',
      type: String
    },

    selectedSitesOption: {
      default: '',
      type: String
    },

    selectedUsers: {
      type: Array,
      default: () => []
    },
    tab: {
      type: Object,
      default: () => {}
    },
    isLoading: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  emits: ['fetchTags','refreshTable','addNewSite','import-site-popup', 'refreshHostingConnections', 'go-live'],
  setup() {
    function onDrop() {
    }

    const { getRootProps, getInputProps, ...rest } = useDropzone({ onDrop });
    //

    const toolTipStatus = ref(null)

    return {
      getRootProps,
      getInputProps,
      ...rest,
      copyurl: 0,
      toolTipStatus,
      serverErrors: ref([]),
      selectedSite: ref(false),
      isSitesLoading: ref(false),
      commandsList: ref([]),
      commandOutput: ref(''),
      executingCommand: ref(false),
      cmdOutputCopied: ref(false),
      doExecuteCmdOnBulkSites: ref(false),
      selectedCommand: ref({}),
      showCommandArgumentModal: ref(false),
      commandArguments: ref([]),
      showCodeEditorModal: ref(false)
    };
  },
  data() {
    return {
      tableFields: [
        { key: "name", label: wTrans('name'), width: '300px' },
        { key: "expiry", label: wTrans('expiry'), width: '190px' },
        { key: "space", label: wTrans('disk_space'), width: '140px' },
        { key: "tags", label: wTrans('tags'), width: '240px' },
        { key: "action", label: wTrans('actions'), width: '210px' },
      ],
      golive: wTrans('go_live'),
      show: false,
      isChatModal: false,
      isConfigureModal: false,
      isMigrateModal: false,
      deleteSitePermanently: false,
      showTotalSitesToDelete: 2,
      validSite: true,
      siteDetail: {},
      isUserNameEditing: true,
      editableUserName: "",
      usernamePwdErorrs: [],
      isSiteUsageModalOpen: false,
      selectedSiteForSiteUsage: null,
      isPasswordEditing: true,
      classNameHover: 'site-url',
      isReserveSite: false,
      hoverSiteId: null,
      isHovered: false,
      isShowAllTags: false,
      selectedPropsValue: [],
      selected: [],
      options: [],
      selectedFilterTags: [],
      source: '',
      showAllTags: null,
      sites: [],
      refresh: false,

      pagination: {
        currentPage: parseInt(localStorage.getItem('pagination.sites.current_page') || '1', 10),
        lastPage: 1,
        total: 0,
      },
      warning: false,
      message: {
        heading: wTrans('are_you_sure'),
        subHeading: wTrans('the_site_will_be_removed_from_server'),
        sitesToDelete: []
      },
      accept: false,
      wp: [],
      site_id: null,
      confirmingDomainDeletion: false,
      deleteDomainProcessing: false,
      confirmingReserveSite: false,
      reserveSiteProcessing: false,
      exportSiteProcessing: false,
      site: {},
      openTemplateModal: false,
      selectedFilter: "all",
      revealPassword: false,
      processing: false,
      currentLogSite: false,
      currentCloudTypeSite: '',
      error: false,
      refreshDataInterval: null,
      restoreSiteProcessing: false,
      restoreSiteId: null,
      confirmingExportWPLocal: false,
      confirmingExportSite: false,
      bulkSelection: [],
      opening_db: false,
      processing_site: null,
      opening_mg_guru: false,
      showUploadSection: false,
      siteId: null,
      uploadingTheme: null,
      uploadingPlugin: null,
      selectedCopyText: '',
      link_copied: false,
      link_copy_timer: null,
      copy_type: '',
      selectedTagSite: null,
      showMoreSiteId: null,
      selectedCopySite: '',
      cloud_type: '',
      rc_templates: [],
      openSuffixDomain: false,
      selectedType: null,
      selectedTag: [],
      selectedUser: [],
      numberSelected: 12,
      term: "",
      delete_reserved_warning: false,
      isOpenNewVersion: false,
      isOpenCommandModal: false,
      isAllSelectedSite: false,
      isOpenSitePushModal: false,
      connectedSites: [],
      connect_form: {
        site_id: "",
        username: "",
        connect_id: "",
        new_url: "",
      },
      parent_url: '',
      connection_id: '',
      callback_window: Object,
      site_push_connect_data: [],
      sitepushstatus: "pending",
      atarimAppUrl: ATARIM_APP_URL,
      showViewCredDialog: false,
      showDeleteSiteModal: false,
      showBulkDeleteSiteModal: false,
      selectedSiteForMailTrapIntegration: null,
      showSelectMailTrapIntegrationModal: false,
      showDeleteMailTrapIntegrationModal: false,
      selectedSiteForMailTrapDeletion: null,
      deleteSiteTimeOut: null,
      copyToClipboardTimeOut: null,
      copySiteToClipboardTimeOut: null,
      copyWebhookToClipboardTimeOut: null,
      showDisconnectSiteModal: false,
      showMigrateV3Message: false,
      preferredHostingProviders: [],
      otherHostingProviders: [],
      selectedBaseOption: {},
      defaultItemsPerPage: "10",
      perPageValue:parseInt(localStorage.getItem('pagination.sites.per_page') || '10', 10),
      tagInputValue: "",
      userInputValue: "",
      sitePrefix: 'sites_' // Store the common string here
    };
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, [
      "user",
      "userActivePlan",
      "teamCan",
      "teamAllow",
      "getLaunchSiteStatus",
      "getRefreshTable",
      "featureAvailableFrom",
      "userBaseActivePlan",
      "isAtarimIntegrated",
      "getUserIntegrationResources",
      "getUserMailTrapIntegrations",
      "featureAvailableFromIntervalWise",
      "atarimIntegration",
      "suffixDoamins",
      "launch_site_status",
      "tagItems"
    ]),
    queryData() {
      const that = this;
      const data = {};
      data.request_type = "site_list";
      if (this.selectedFilter && this.selectedFilter != "all") {
        data.sites = this.selectedFilter;
      }
      if (that.pagination.currentPage > 1) {
        data.page = that.pagination.currentPage;
      }
      if (that.selectedFilterTags.length) {
        data.tags = that.selectedFilterTags.join(",");
      }

      // data.per_page = that.numberSelected;
      data.type = that.selectedType;

      if (that.userInputValue) {
        data.term = that.term.trim();
      }
      if (that.tagInputValue) {
        data.term = that.tagInputValue.trim();
      }
      if (that.selectedUser.length) {
        data.members = that.selectedUser.join(",");
      }
      if (that.searchTerm) {
        data.term = that.searchTerm.trim();
      }
      if (that.source==='filterData') {
        data.term = '';
      }
      data.tab = this.tab.id
      data.per_page = parseInt(that.perPageValue ?? that.defaultItemsPerPage);
      return data;
    },
    executeCommandTitle() {
      const arr = Object.values(this.bulkSelection)
      if (arr.length > 0) {
        const site = this.sites.find(i => i.id == arr[0])
        return (site.sub_domain)
      }
      return this.selectedSite.sub_domain
    },
  },
  watch: {
  
    selected(val) {
      this.selectedPropsValue = val
    },
    launch_site_status(newStatus) {
      if (newStatus === 'completed') {
        this.fetchData();
        this.$inertia.get(
          route("staging"),
          {},
          { preserveState: true, only: ["site_count"] },
        );
      }
    },
    getLaunchSiteStatus(value) {
      const that = this;
      if (value == "completed" && that.restoreSiteProcessing) {
        that.restoreSiteProcessing = false;
        that.refresh = true;
      }
    },
    refresh() {
      if (this.refresh) {
        this.fetchData();
        this.refresh = false;
      }
    },
    tab(oldVal, newVal) {
      if (oldVal == newVal) {
        return;
      }
      this.pagination.currentPage = 1;
      this.fetchData();
      this.refresh = false;
    },
    warning() {
      if (!this.warning) {
        this.isUserNameEditing = true
        this.isPasswordEditing = true
        //   this.wp = [];
      }
    },
    searchTerm() {
      this.filterSites()
    },
    tagItems() {
      this.filterSites()
    },
    getRefreshTable() {
      this.refresh = true;
    },
    'callback_window': {
      handler(value) {
        const timer = setInterval(() => {
          if (value?.closed) {

            if (this.appStore.callback != undefined) {
              if (this.appStore.callback.is_completed && this.appStore.callback.domain != '') {
                this.fetchConnection(this.site_push_connect_data.new_url);
              } else {
                this.sitepushstatus = 'failed';
              }
            } else {
              this.sitepushstatus = 'failed';
            }
            clearInterval(timer);
          }
        }, 1000);
      },
    },
    'pagination.currentPage'(newPage) {
    localStorage.setItem('pagination.sites.current_page', newPage);
  }
  },
  unmounted() {
    clearTimeout(this.refreshDataInterval);
    clearTimeout(this.link_copy_timer)
    clearTimeout(this.searchTimeout)
    clearTimeout(this.deleteSiteTimeOut)
    clearTimeout(this.copyToClipboardTimeOut)
    clearTimeout(this.copySiteToClipboardTimeOut)
    window.removeEventListener("keyup", this.keyUpEventHandler)
  },

  created() {
    this.selectedPropsValue = this.selected
    //this.getHostingProviders()
    // Get term parameter from URL and set term value if it exists
    const urlParams = new URLSearchParams(window.location.search);
    const termParam = urlParams.get('term');
    if (termParam) {
      this.term = termParam;
    }
    // document.addEventListener('keyup', function (evt) {
    //     if (evt.keyCode === 27) {
    //     that.selectedTagSite = null
    //     }
    // });

  },

  mounted() {
    this.selectedFilterTags = this.filterTag;
    this.fetchData();
    // this.perPageItem();
    this.resetRoot();
    this.getCommandsList();
    this.appStore.fetchSuffixDomains();

    this.appStore.fetchUserIntegrations();
    window.addEventListener("keyup", this.keyUpEventHandler);
  },
  methods: {
    onFilterTagChange(data) {
      this.selectedFilterTags = data.selectedTags;
      this.selectedType = data.selectedTypeOption;
      this.selectedUser = data.selectedUsers;
      this.term = data.userInputValue;
      this.tagInputValue = data.tagInputValue;
      this.userInputValue = data.userInputValue;
      this.source = data.source;
      this.fetchData();
    },
    getRowClass(items) {
      items.forEach(item => {
        if (item.expiryType === 'Expired') {
          item.trClass = 'bg-grayCust-50 cursor-not-allowed '; // Apply the class for 'Expired' items
        }
      });
    },

    openConfigureModal() {
      this.isConfigureModal = true;
      this.isChatModal = false;
    },
    keyUpEventHandler(event) {
      if (event.keyCode === 27) {
        // try close your dialog
        this.warning = false;
        this.selectedTagSite = null;
      }
    },
    deselectAllSites() {
      this.bulkSelection = [];
      this.isAllSelectedSite = false;
      // Uncheck all checkboxes manually
      this.sites.forEach((item)=>{
        item.selected = false;
      })
    },
    selectAllSites() {
      if (this.isAllSelectedSite) {
        this.bulkSelection = [];
        this.isAllSelectedSite = false;
      } else {
        this.bulkSelection = [];
        for (const site in this.sites.filter(
          (el) =>
            this.$page.props.auth.user.id == el.user_id ||
            this.$page.props.auth.user.current_team?.user_role?.key ===
            "owner" ||
            this.$page.props.auth.user.current_team?.user_role?.key ===
            "admin",
        )) {
          this.bulkSelection.push(this.sites[site].id);
          this.sites[site].selected = true;
        }
        this.isAllSelectedSite = true;
      }
    },
    copyToClipboardBtn() {
      const ele = this.$refs.shareLinkRef;
      const that = this;
      if (ele) {
        ele.select();
        ele.setSelectionRange(0, 99999); /* For mobile devices */

        /* Copy the text inside the text field */
        document.execCommand("copy");
        that.link_copied = true;
        that.link_copy_timer = setTimeout(() => {
          that.link_copied = false;
        }, 2000);
      }
    },
    selectSite(event) {
      if (event.target.checked) {
        this.bulkSelection.push(event.target.value);
      } else {
        this.bulkSelection = this.bulkSelection.length && this.bulkSelection.filter(item => item !== event.target.value);
      }

      if (this.bulkSelection.length !== this.sites.length) {
        this.isAllSelectedSite = false;
      } else {
        this.isAllSelectedSite = true;
      }
    },
    toggleNewVersionSidebar() {
      this.isOpenNewVersion = !this.isOpenNewVersion;
    },

    ViewMoreSitesToDelete() {
      this.showTotalSitesToDelete = this.message.sitesToDelete.length;
    },
    ViewLessSitesToDelete() {
      this.showTotalSitesToDelete = 2;
    },
    openHtpassword(site) {
      this.site = site;
      const child = this.$refs.siteHtpasswordModal;
      child.toggle();
    },
    openExpiredAt(site) {
      this.site = site;
      const child = this.$refs.siteExpiredAtModal;
      child.toggle();
    },
    filterDropdown() {
      this.filterDropdownShow = !this.filterDropdownShow;
    },
    toggleSiteTags(site) {
      // if (!this.selectedTagSite) {
      //   this.selectedTagSite = site;
      // } else {
      //   this.selectedTagSite = null;
      // }
      this.selectedTagSite = site;
    },
    removeTableTag(tags, siteId) {
      const that = this;
      that.processing = true;
      const tagData = {
        ids: tags,
      };
      axios
        .post(`/api/v2/sites/${siteId}/sync-tags`, tagData)
        .then(function (response) {
          const siteIndex = that.sites.findIndex(
            (obj) => obj.id == siteId,
          );
          that.sites[siteIndex].tag_id_array = response.data.data;
          if (response.data.data.length < 2)
            that.isShowAllTags = false;
          return true;
        })
        .then(function () {
          if (that.selectedTag.length) {
            that.pagination.currentPage = 1;
            that.fetchData();
          }
          return true;
        })
        .catch(function () { });
      that.processing = false;
    },
    onCheck(val) {
      const that = this;
      const siteIndex = that.sites.findIndex(
        (obj) => obj.id == val.site_id,
      );
      that.sites[siteIndex].tag_id_array = val.selected_val;
      // if (val.selected_val.length < 2) that.isShowAllTags = false;
    },
    getRandomItem() {
      const randomIndex = Math.floor(
        Math.random() * tagColours.length,
      );
      const item = tagColours[randomIndex];
      return item;
    },
    createCustomTag(tag) {
        const that = this;
        const tagData = {
          tag_name: tag,
          color_code: that.getRandomItem(),
        };
        axios
          .post(`/api/v2/sites/${that.selectedTagSite.id}/tags`, tagData)
          .then(function (response) {
            const siteIndex = that.sites.findIndex(
              (obj) => obj.id == that.selectedTagSite.id
            );
            // Add new tag ID once
            that.sites[siteIndex].tag_id_array.unshift(response.data.data.id);

            response.data.data.checked = true;
            that.tagItems.unshift(response.data.data);
            that.$refs.tagDropdown.allTags = that.tagItems;
            // Ensure selected tags reflect the correct list
            that.$refs.tagDropdown.selected = [...that.sites[siteIndex].tag_id_array];

        })
        .catch(function () { });
        that.processing = false;
    },
    async syncTags(ids = [], addTag = null) {
      const requestData = {};

      if (ids) {
        requestData.ids = ids.selected_val;
      }
      if (addTag) {
        requestData.tag_name = addTag.tag_name;
        requestData.tag_color = addTag.color_code;
      }

      if (this.selectedTagSite?.id) {
        await axios
          .post(
            `/api/v2/sites/${this.selectedTagSite.id}/sync-tags`,
            requestData,
          )
          .then((res) => {
            this.selectedTagSite.tags = res.data.data.tags;
            this.sites.forEach(site => {
              if(site.id == this.selectedTagSite.id) {
                site.tag_id_array = requestData.ids;
              }
            });
            this.$emit('fetchTags');
          });
      }
    },

    searchTag(value) {
      let filteredTag = [];
      if (value) {
        filteredTag = this.tagItems.filter((item) => {
          return value
            .toLowerCase()
            .split(" ")
            .every((v) => item.name.toLowerCase().includes(v));
        });
      } else {
        return this.tagItems;
      }
      this.options = filteredTag;
    },
    openNewSiteModel() {
      this.$emit("addNewSite");
    },
    showCodeEditorDialog(site) {
      this.siteDetail = site;
      this.showCodeEditorModal = true;
    },
    hideCodeEditorDialog() {
      this.showCodeEditorModal = false;
    },
    fetchCodeEditor(siteId) {
      const that = this;
      that.appStore.getCodeEditor(siteId).then(() => {
        that.processing = false;
      });
    },
    openDb(siteId) {
      const that = this;
      that.opening_db = true;
      that.processing_site = siteId;
      that.appStore.getDbEditor(siteId).then(() => {
        that.warning = false;
        that.opening_db = false;
        that.processing_site = null;
      });
    },
    autoLogin(site) {
      const autoLoginUrl = route("autologin", { site: site.hash });
      window.open(autoLoginUrl, "_blank");
    },

    viewLog(siteId, cloud_type) {
      const child = this.$refs.siteLogModal;

      this.currentLogSite = siteId;
      this.currentCloudTypeSite = cloud_type;
      child.toggle();
    },
    filterSites() {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
      }, 700);
    },
    showNotification(notification) {
      this.appStore.setNotification(notification);
    },
    deleteDomainConfirm(site) {
      this.site_id = site.id;
      this.confirmingDomainDeletion = true;
    },
    saveAsTemplate(site) {
      if (!this.teamCan?.templates) {
        const subHeading = this.user.is_team_owner
          ? trans(
            "created_maximum_number_template_register",
          )
          : trans("team_quota_exceed_subheading");
        const msg = {
          subHeading: subHeading,
          planMessage: this.featureAvailableFrom["templates"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom["templates"],
              },
            )
            : null,
          feature: "templates",
          triggerRef: "staging_list_button_save_template"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      this.site_id = site.id;
      //this.site_templates = site.cloud_type == 'instacloud'?site.templates.concat(this.rc_templates):site.templates;
      const child = this.$refs.siteTemplateModal;
      child.toggle();
      this.openTemplateModal = true;
    },
    openNewVersion(site) {
      if (!this.teamCan?.site_versions) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("site_version_not_allow")
            : trans(
              "site_version_not_allow_team",
            ),
          planMessage: this.featureAvailableFrom["site_versions"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "site_versions"
                  ],
              },
            )
            : null,
          feature: "site_versions",
          triggerRef: "staging_list_menu_versions"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      this.site = site;
      const child = this.$refs.siteVersionsModal;
      child.toggle();
    },
    cloneSite(site) {

      if (!this.teamCan?.cloning && site.cloud_type == "instacloud") {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("user_clone_upgrade_subheading")
            : trans(
              "team_clone_upgrade_subheading",
            ),
          planMessage: this.featureAvailableFrom["cloning"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom["cloning"],
              },
            )
            : null,
          feature: "cloning",
          triggerRef: "staging_list_menu_clone_site"
        };
        this.appStore.setUpgradeWarning(msg);

        return false;
      } else if (!this.teamCan?.sites) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans('you_have_created_maximum_number')
            : trans(
              "team_site_quota_exceed",
            ),
          planMessage: this.featureAvailableFrom["sites"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "sites"
                  ],
              },
            )
            : null,
          feature: "sites",
          triggerRef: "staging_list_menu_clone_site"
        };
        this.appStore.setUpgradeWarning(msg);
        return false
      } else if (!this.teamCan?.disk_storage) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans('you_have_used_maximum_disk', { plan_name: this.userBaseActivePlan.name, storage_quota: this.userActivePlan.description.disk_storage })
            : trans(
              "the_maximum_disk_quota_of_sites_allowed",
            ),
          planMessage: this.featureAvailableFrom["disk_storage"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "disk_storage"
                  ],
              },
            )
            : null,
          feature: "disk_storage",
          triggerRef: "staging_list_menu_clone_site"
        };
        this.appStore.setUpgradeWarning(msg);
        return false
      }
      this.site_id = site.id;
      const child = this.$refs.siteCloneModal;
      child.$data.form.is_reserved = Boolean(site.is_reserved);
      child.toggle();
    },
    reserveSiteConfirm(site) {
      let heading = null;
      let subHeading = null;
      if (!this.teamCan?.reserve_sites && !site.is_reserved) {
        if (this.user.is_team_owner) {
          subHeading =
            this.user.active_plan == null ||
              this.user.active_plan.type == "free"
              ? trans(
                "reserve_site_confirm_active_plan",
              )
              : trans(
                "reserve_site_please_upgrade_plan_register",
              );
        } else {
          subHeading = trans(
            "reserve_feature_not_available_contact_team_owner",
          );
        }

        const msg = {
          subHeading: subHeading,
          planMessage: this.featureAvailableFrom["reserve_sites"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "reserve_sites"
                  ],
              },
            )
            : null,
          feature: "reserve_sites",
          triggerRef: "staging_list_button_reserve_site"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }

      this.site = site;
      this.site_id = site.id;
      this.confirmingReserveSite = true;
    },
    reserveSite() {
      const that = this;
      that.reserveSiteProcessing = true;
      const siteId = that.site_id;
      this.appStore
        .toggleReserveSite(siteId)
        .then((response) => {
          that.fetchData();
          const notification = {
            heading: response.data.is_reserved
              ? that.$t("site_reserve")
              : that.$t("site_unreserve"),
            subHeading: response.message,
            type: "success",
          };
          that.showNotification(notification);
        })
        .finally(() => {
          that.confirmingReserveSite = false;
          that.reserveSiteProcessing = false;
        });
    },
    exportSiteConfirm(type,site) {
      const that = this;
      that.site = site;
      that.exportType = type;
      that.confirmingExportSite = true;
    },
    exportWPLocal() {
      const that = this;
      this.exportSiteProcessing = true;
      const siteId = that.site.id;
      if (siteId) {
        axios
          .post(`api/v1/site/export-localwp-file/${siteId}`)
          .then(function (response) {
            that.fetchData();
            const notification = {
              heading: that.$t("site_export"),
              subHeading: response.data.message,
              type: "success",
            };
            that.showNotification(notification);
            that.site = {};
          })
          .catch(function () { })
          .finally(() => {
            that.exportSiteProcessing = false;
            that.confirmingExportSite = false;
          });
      }
    },
    exportInstaWP(){
      const that = this;
      this.exportSiteProcessing = true;
      const siteId = that.site.id;
      if (siteId) {
        axios
          .post(`api/v1/site/export-instawp-file/${siteId}`)
          .then(function (response) {
            that.fetchData();
            const notification = {
              heading: that.$t("site_export"),
              subHeading: response.data.message,
              type: "success",
            };
            that.showNotification(notification);
            that.site = {};
          })
          .catch(function () { })
          .finally(() => {
            that.exportSiteProcessing = false;
            that.confirmingExportSite = false;
          });
      }
    },
    exportStudio() {
      const that = this;
      this.exportSiteProcessing = true;
      const siteId = that.site.id;
      if (siteId) {
        axios
          .post(`api/v1/site/export-studio-file/${siteId}`)
          .then(function (response) {
            that.fetchData();
            const notification = {
              heading: that.$t("site_export"),
              subHeading: response.data.message,
              type: "success",
            };
            that.showNotification(notification);
            that.site = {};
          })
          .catch(function () { })
          .finally(() => {
            that.exportSiteProcessing = false;
            that.confirmingExportSite = false;
          });
      }
    },
    handleExport() {
      const that = this;
      if (that.exportType == 'local_wp') {
        that.exportWPLocal();
      } else if (that.exportType == 'insta_wp') {
        that.exportInstaWP();
      } else if (that.exportType == 'wp_studio') {
        that.exportStudio();
      } else {
        that.exportWPLocal();
      }
    },
    deleteDomain() {
      const that = this;
      this.deleteDomainProcessing = true;
      const siteId = that.site_id;
      axios
        .delete(`/api/v1/site/delete-domain/${siteId}`)
        .then((response) => {
          that.fetchData();
          const notification = {
            heading: that.$t("site_domain_deleted"),
            subHeading: response.data.message,
            type: "success",
          };
          that.showNotification(notification);
          that.confirmingDomainDeletion = false;
          that.deleteDomainProcessing = false;
        })
        .catch(function () { })
        .finally(() => {
          that.deleteDomainProcessing = false;
        });
    },
    changeDomain(site) {
      if (!this.teamCan?.advance_config) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("advance_config_not_allow")
            : trans(
              "advance_config_not_allow_team",
            ),
          planMessage: this.featureAvailableFrom["advance_config"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "advance_config"
                  ],
              },
            )
            : null,
          feature: "advance_config",
          triggerRef: "staging_list_menu_suffix_domain"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      this.site = site;
      const child = this.$refs.changeSuffixDomainModal;
      child.toggle();
      this.openSuffixDomain = true;
    },
    resetRoot() {
      this.appStore.updateLaunchSiteDetail({});
    },
    fetchData(page) {
      if (page) {
        this.pagination.currentPage = page;

      }
      this.processing = true;
      this.isSitesLoading = true;
      const that = this;
      that.restoreSiteProcessing = false;
      that.restoreSiteId = null;
      axios
        .get("/api/v2/sites", { params: this.queryData })
        .then((response) => {
          that.sites = response.data.data;
          that.setPagination(response);
        })
        .catch(() => {
          that.error = true;
        })
        .finally(() => {
          that.bulkSelection = [];
          that.processing = false;
          this.isSitesLoading = false;
        });
    },
    onDeleteSite(site) {
      if (site.is_reserved) {
        const notification = {
          heading: trans("alert"),
          subHeading: trans(
            "reserved_sites_cannot_be_deleted_to_delete_this_site_please_Unreserve_it_first",
          ),
          type: "warning",
        };
        this.showNotification(notification);
        return false;
      }

      this.site_id = site.id;
      this.bulkSelection = [];
      this.siteDetail = site;
      this.validSite = true;
      this.wp = [];
      this.showDeleteSiteModal = true;
    },
    deleteSite() {
      this.resetRoot();
      if (this.accept) {
        const that = this;
        axios
          .delete("/api/v1/site/" + that.site_id)
          .then(function (response) {
            const notification = {
              heading: that.$t("site_deleted"),
              subHeading: that.$t(
                "your_site_remove_server",
              ),
              type: "success",
            };
            that.showNotification(notification);

            that.pagination.currentPage = 1;
            return response;
          })
          .then(function () {
            that.deleteSiteTimeOut = setTimeout(() => {
              that.fetchData();
              that.$inertia.get(
                route("staging"),
                {},
                { preserveState: true, only: ["site_count"] },
              );
            }, 1000);
          })
          .catch(() => { })
          .finally(() => {
            that.warning = false;
          });
      }
    },
    matchSiteToDelete(element) {
      if (element.target.value == this.siteDetail.sub_domain) {
        this.validSite = false;
      } else {
        this.validSite = true;
      }
    },
    mapDomain(site_id) {
      if (!this.teamCan?.custom_domain) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("custom_domain_not_allow")
            : trans(
              "custom_domain_not_allow_team",
            ),
          planMessage: this.featureAvailableFrom["custom_domain"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "custom_domain"
                  ],
              },
            )
            : null,
          feature: "custom_domain",
          triggerRef: "staging_list_menu_map_domain"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      this.$inertia.get(
        route(
          'sites.edit.map.domain',
          {
            site: site_id,
          },
        ),
      )

    },
    restoreSite(site_id) {
      let heading = null;
      let subHeading = null;
      const index = this.sites.findIndex((item) => item.id == site_id);
      //console.log(this.sites[index]);
      // Abuse Suspended
      if (this.sites[index].status == 1) {
        this.showNotification({
          heading: this.$t(
            "restore_sites",
          ),
          subHeading: this.$t(
            "abuse_suspended_description",
          ),
          type: "error",
        });
        return;
      } else if (this.sites[index].status == 6) {
        // Overuse Suspended
        if (!this.teamCan?.sites) { 
          this.showNotification({
            heading: this.$t(
              "restore_sites",
            ),
            subHeading: this.$t(
              "overuse_suspended_description",
            ),
            type: "error",
          });
          return;
        }
      }
      if (!this.teamCan?.restore_sites) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("restore_sites_not_allow")
            : trans(
              "restore_sites_not_allow_team",
            ),
          planMessage: this.featureAvailableFrom["restore_sites"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom[
                  "restore_sites"
                  ],
              },
            )
            : null,
          feature: "restore_sites",
          triggerRef: "staging_list_button_restore"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }

      this.resetRoot();
      const that = this;
      
      that.sites[index].status = 3;
      axios
        .post("api/v1/site/restore/" + site_id)
        .then((response) => {
          that.showNotification({
            heading: that.$t("restore_sites"),
            subHeading: response.data.message,
            type: "success",
          });
          const id = response.data.data.task_id;
          window.Echo.channel(
            `site.restoration.progress.${id}`,
          ).listen("SiteRestorationProgress", (e) => {
            const progress = e?.percentage_complete || 0;
            if (
              progress == 100 ||
              (e?.status && e.status == "error")
            ) {
              window.Echo.leave(`site.restoration.progress.${id}`);
              that.fetchData();
              if (progress == 100) {
                that.showNotification({
                  heading: that.$t(
                    "restore_sites",
                  ),
                  subHeading: that.$t(
                    "site_restored",
                  ),
                  type: "success",
                });
              }
              if (
                e?.status &&
                e.status == "error"
              ) {
                that.showNotification({
                  heading: that.$t(
                    "restore_sites",
                  ),
                  subHeading: that.$t(
                    "error_in_site_restore",
                  ),
                  type: "error",
                });
              }
            }
          });
        })
        .catch(() => {
          that.sites[index].status = 0;
          that.warning = false;
        })
        .finally(() => { });
    },
    setPagination(response) {
      this.pagination.total = response.data.meta.total;
      this.pagination.lastPage = response.data.meta.last_page;
      this.pagination.currentPage = response.data.meta.current_page;
    },
    openMigrateUrl(site) {
      const that = this;
      const siteId = site.id;
      that.opening_mg_guru = true;
      that.processing_site = siteId;
      axios
        .get(route("site.migrateguru.api", { site: site.id }))
        .then((response) => {
          window.open(response.data.data.migrateguru_url, "_blank");
        })
        .catch(() => {
          that.warning = false;
        })
        .finally(() => {
          that.opening_mg_guru = false;
          that.processing_site = null;
        });
    },
    checkSiteExpire(site) {
      return site.is_reserved ? false : site.expired_at_format;
    },
    acceptAction() {
      this.accept = true;
      this.warning = false;
      this.deleteSite();
    },
    bulkAction() {
      const that = this;
      if (
        this.sites.filter(
          (item) =>
            that.bulkSelection.includes(item.id) &&
            item.is_reserved,
        ).length
      ) {
        const notification = {
          heading: trans("alert"),
          subHeading: that.$t(
            "please_uncheck_reserve_sites",
          ),
          type: "warning",
        };
        that.showNotification(notification);
        return false;
      } else {
        this.showBulkDeleteSiteModal = true;
      }
    },

    generateAxiosRequest(url, file, siteId, type) {
      let axiosRequest = null;
      if (url) {
        url =
          type == "theme"
            ? url
              .replace("https://wordpress.org/themes/", "")
              .replace("/", "")
            : url
              .replace("https://wordpress.org/plugins/", "")
              .replace("/", "");
        axiosRequest = axios.post(
          `/api/v1/site/upload-theme-or-plugin/${siteId}`,
          {
            url,
            type,
          },
        );
      } else {
        const formData = new FormData();
        formData.append("zip", file);
        formData.append("type", type);
        axiosRequest = axios.post(
          `/api/v1/site/upload-theme-or-plugin/${siteId}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          },
        );
      }
      return axiosRequest;
    },

    handleDropPluginOrTheme(e, siteId, type) {
      e.preventDefault(); // Prevent default to stop file from being opened
      const dataTransfer = e.dataTransfer;
      const url =
        dataTransfer.getData("url") || dataTransfer.getData("text");
      const file = dataTransfer.files[0];
      if (type == "theme") {
        this.uploadingTheme = true;
      }
      if (type == "plugin") {
        this.uploadingPlugin = true;
      }

      if (file && !this.validateFileExtension(file.name, ["zip"])) {
        const notification = {
          heading: trans("data_invalid"),
          subHeading: trans("file_of_zip_type"),
          type: "error",
        };
        this.showNotification(notification);
        this.hideUploaderSlate();
        return false;
      }

      if (url) {
        if (
          type == "plugin" &&
          !url.startsWith("https://wordpress.org/plugins/")
        ) {
          const notification = {
            heading: trans("data_invalid"),
            subHeading: trans(
              "the_plugin_url_must_be_start_with_https_wordpress_org_plugins_",
            ),
            type: "error",
          };
          this.hideUploaderSlate();
          this.showNotification(notification);
          return false;
        }
        if (
          type == "theme" &&
          !url.startsWith("https://wordpress.org/themes/")
        ) {
          const notification = {
            heading: trans("data_invalid"),
            subHeading: trans(
              "the_theme_url_must_be_start_with_https_wordpress_org_themes_",
            ),
            type: "error",
          };
          this.hideUploaderSlate();
          this.showNotification(notification);
          return false;
        }
      }
      const that = this;
      if (url || file) {
        this.generateAxiosRequest(url, file, siteId, type)
          .then(function (response) {
            const jsonData = response.data;
            const notification = {
              heading: that.$t("success"),
              subHeading: jsonData.message,
              type: "success",
            };
            that.showNotification(notification);
          })
          .catch(function (error) {
            const errorMessage =
              error.response &&
                error.response.data &&
                error.response.data.message
                ? error.response.data.message
                : "An error occurred during upload. Please try again.";
            const notification = {
              heading: that.$t("error"),
              subHeading: errorMessage,
              type: "error",
            };
            that.showNotification(notification);
          })
          .finally(() => {
            that.hideUploaderSlate();
          });
      } else {
        that.hideUploaderSlate();
      }
    },

    validateFileExtension(filename, allowedExtensions) {
      const ext = filename.substring(filename.lastIndexOf(".") + 1);
      return allowedExtensions.includes(ext);
    },

    hideUploaderSlate() {
      this.siteId = null;
      this.showUploadSection = false;
      this.uploadingTheme = false;
      this.uploadingPlugin = false;
    },
    handleDragLeave(e, siteId) {
      e.preventDefault(); // Prevent default
      if (siteId === null) {
        //this.siteId = siteId;
      }
    },
    handleDragOver(e, siteId) {
      e.preventDefault(); // Prevent default to allow drop
      this.showUploadSection = true;
      this.siteId = siteId;
    },
    copyToClipboard(text) {
      const that = this;
      that.selectedCopyText = text;
      this.copyToClipboardTimeOut = setTimeout(() => {
        const ele = that.$refs.selectedCopyText;

        if (ele) {
          ele.select();
          ele.setSelectionRange(0, 99999); /* For mobile devices */

          /* Copy the text inside the text field */
          document.execCommand("copy");
          that.link_copied = true;
          that.link_copy_timer = setTimeout(() => {
            that.link_copied = false;
          }, 1000);
        }
      }, 100);
    },
    copySiteToClipboard(text) {
      const that = this;
      that.selectedCopySite = text;
      this.copySiteToClipboardTimeOut = setTimeout(() => {
        const ele = document.getElementById("selectedCopySite");

        if (ele) {
          ele.select();
          ele.setSelectionRange(0, 99999); /* For mobile devices */

          /* Copy the text inside the text field */
          document.execCommand("copy");
          that.link_copied = true;
          that.link_copy_timer = setTimeout(() => {
            that.link_copied = false;
          }, 1000);
        }
      }, 100);
    },
    updateUserName() {
      if (this.editableUserName) {
        axios
          .post(
            `api/v1/sites/${this.site_id}/username/update-credentials`,
            { wp_username: this.editableUserName },
          )
          .then(() => {
            this.isUserNameEditing = !this.isUserNameEditing;
            this.usernamePwdErorrs = [];
          })
          .then(() => {
            this.refresh = true;
          })
          .catch(
            (error) =>
            (this.usernamePwdErorrs =
              error.response.data.data.errors.wp_username),
          );
      }
    },
    redirectToCommandPage(site) {
      if (!this.teamCan?.run_commands) {
        const msg = {
          subHeading: this.user.is_team_owner
            ? trans("run_command_not_allow")
            : trans("run_command_not_allow_team"),
          planMessage: this.featureAvailableFrom["run_commands"]
            ? trans(
              "this_feature_is_available_in_plan_and_above",
              {
                planName:
                  this.featureAvailableFrom["run_commands"],
              },
            )
            : null,
          feature: "run_commands",
          triggerRef: "staging_list_menu_commands"
        };
        this.appStore.setUpgradeWarning(msg);
        return false;
      }
      this.$inertia.get(
        route("sites.edit.run.commands", { site: site.id }),
      );
    },
    toggleCommandModal(site, doExecuteCmdOnBulkSites = false) {
      if (!this.isOpenCommandModal) {
        if (!this.teamCan?.run_commands) {
          const msg = {
            subHeading: this.user.is_team_owner
              ? trans("run_command_not_allow")
              : trans(
                "run_command_not_allow_team",
              ),
            planMessage: this.featureAvailableFrom["run_commands"]
              ? trans(
                "this_feature_is_available_in_plan_and_above",
                {
                  planName:
                    this.featureAvailableFrom[
                    "run_commands"
                    ],
                },
              )
              : null,
            feature: "run_commands",
            triggerRef: "staging_list_menu_bulk_commands"
          };
          this.appStore.setUpgradeWarning(msg);
          return false;
        }
        this.selectedSite = site;
      } else {
        this.selectedSite = false;
        this.showCommandArgumentModal = false;
      }
      this.isOpenCommandModal = !this.isOpenCommandModal;
      this.commandOutput = "";
      this.doExecuteCmdOnBulkSites = doExecuteCmdOnBulkSites;
    },
    getCommandsList() {
      const that = this;
      that.processing = true;
      this.appStore.fetchCommands().then((data) => {
        that.commandsList = data;
        that.selectedCommand = that.commandsList[0];
      });
    },
    executeCommand() {
      if (this.doExecuteCmdOnBulkSites) {
        this.bulkExecuteCommand();
        return;
      }
      const that = this;
      that.executingCommand = true;
      const siteId = that.selectedSite.id;
      const data = {
        command_id: that.selectedCommand.id,
      };
      const cmdArgs = [];
      if (that.commandArguments.length > 0) {
        for (const commandArgument of that.commandArguments) {
          for (const parameter of commandArgument.parameters) {
            const item = {};
            item[parameter["searchValue"]] = parameter.replaceValue;
            cmdArgs.push(item);
            // delete parameter.replaceValueError
            // delete parameter.searchValueError
          }
        }
        data.commandArguments = cmdArgs;
      }
      axios
        .post(`/api/v2/sites/${siteId}/execute-command`, data)
        .then((response) => {
          that.commandOutput = response.data.data;
          that.showCommandArgumentModal = false;
        })
        .catch((error) => {
          that.commandOutput =
            error?.response?.data.message || error.message;

          for (const err in error.response.data.errors) {
            if (err.includes("commandArguments")) {
              const errorArray = err.split(".");
              const errorForIndex =
                errorArray[4] == "replaceValue"
                  ? "replaceValueError"
                  : "searchValueError";
              that.commandArguments[errorArray[1]].parameters[
                errorArray[3]
              ][errorForIndex] =
                error.response.data.errors[err][0];
            }
          }
        })
        .finally(() => {
          that.executingCommand = false;
        });
    },
    async bulkExecuteCommand() {
      const that = this;
      that.executingCommand = true;
      const sites = this.sites.filter((item) =>
        this.bulkSelection.includes(item.id),
      );
      that.commandOutput = "";
      let errorOnExecutingCommands = false;
      for (let index = 0; index < sites.length; index++) {
        const site = sites[index];
        const data = {
          command_id: that.selectedCommand.id,
        };
        const cmdArgs = [];
        if (that.commandArguments.length > 0) {
          for (const commandArgument of that.commandArguments) {
            for (const parameter of commandArgument.parameters) {
              const item = {};
              item[parameter["searchValue"]] =
                parameter.replaceValue;
              cmdArgs.push(item);
              // delete parameter.replaceValueError
              // delete parameter.searchValueError
            }
          }
          data.commandArguments = cmdArgs;
        }
        await axios
          .post(`/api/v2/sites/${site.id}/execute-command`, data)
          .then((response) => {
            that.commandOutput += site.sub_domain + ":\n";
            that.commandOutput += response.data.data;
            that.commandOutput += "\n\n";
          })
          .catch((error) => {
            errorOnExecutingCommands = true;
            that.commandOutput += site.sub_domain + ":\n";
            that.commandOutput +=
              error?.response?.data.message || error.message;
            that.commandOutput += "\n\n";

            for (const err in error.response.data.errors) {
              if (err.includes("commandArguments")) {
                const errorArray = err.split(".");
                const errorForIndex =
                  errorArray[4] == "replaceValue"
                    ? "replaceValueError"
                    : "searchValueError";
                that.commandArguments[errorArray[1]].parameters[
                  errorArray[3]
                ][errorForIndex] =
                  error.response.data.errors[err][0];
              }
            }
          });
      }
      if (!errorOnExecutingCommands) {
        that.showCommandArgumentModal = false;
      }
      this.executingCommand = false;
    },
    checkCommandArgumentOrExecute(site) {
      if (
        this.selectedCommand.command_payload &&
        this.selectedCommand.command_payload.includes("{{")
      ) {
        this.commandArguments = [];
        const cmd = this.selectedCommand.command_payload;
        const commandPayloadLines = cmd.split("\n");
        for (const commandPayloadLine of commandPayloadLines) {
          const matches = commandPayloadLine.match(/[^{{\}]+(?=}})/g);
          if (matches) {
            this.commandArguments.push({
              title: commandPayloadLine,
              parameters: matches.map((i) => {
                return { searchValue: i, replaceValue: "" };
              }),
            });
          }
        }
        this.showCommandArgumentModal = true;
      } else {
        this.commandArguments = [];
        this.showCommandArgumentModal = false;
        this.executeCommand(site);
      }

      // if (this.commandArguments.length > 0) {
      //   this.showCommandArgumentModal = true;
      // } else {
      //   this.showCommandArgumentModal = false;
      //   this.executeCommand(site)
      // }
    },
    copyCmdOutputToClipboard() {
      const ele = this.$refs.cmdOutputTextArea;
      if (ele) {
        ele.select();
        ele.setSelectionRange(0, 99999);

        document.execCommand("copy");
        this.cmdOutputCopied = true;
        this.link_copy_timer = setTimeout(() => {
          this.cmdOutputCopied = false;
        }, 2000);
      }
    },
    toggleSitePushModal() {
      this.isOpenSitePushModal = !this.isOpenSitePushModal;
    },
    toggleMigrateModal() {
      this.isMigrateModal = !this.isMigrateModal;
    },
    toggleChatModal() {
      this.isChatModal = !this.isChatModal;
    },
    toggleConfigureModal() {
      this.isConfigureModal = !this.isConfigureModal;
    },

    toggleMailTrapModal() {
      this.show = !this.show;
    },
    openSiteUsageSidebar(site) {
      this.appStore.setSiteUsage({});
      this.isSiteUsageModalOpen = true;
      this.selectedSiteForSiteUsage = site;
    },
    closeSiteUsageSidebar() {
      this.isSiteUsageModalOpen = false;
      this.selectedSiteForSiteUsage = null;
    },

    async connectSiteModal(site) {
      this.siteDetail = site;
      await this.checkStagingSite(site.url);
      await this.getConnectedSites();
    },
    async getConnectedSites(page = 1) {
      this.process = true;
      await axios
        .get(
          "/api/v1/get-connected-sites" +
          "?page=" +
          page +
          "&limit=5",
        )
        .then((response) => {
          this.connectedSites = response.data.data.data;
        })
        .catch(() => { })
        .finally(() => {
          this.process = false;
        });
    },
    sitePushConnect(data) {
      const that = this;
      that.sitepushstatus = "running";
      that.connect_form.site_id = that.siteDetail.id;
      that.connect_form.username = that.siteDetail.site_meta.wp_username;
      that.connect_form.connect_id = data.connect_id;
      that.connect_form.new_url = data.new_url;
      that.site_push_connect_data = data;

      axios
        .post(
          "/api/v1/site-push-connect/" + data.randomStr,
          that.connect_form,
        )
        .then((response) => {

          if (response.data.status == 0) {
            that.sitepushstatus = "connect-failed";
            const message = {
              heading: that.$t("failed"),
              subHeading: response.data.data.message,
              type: "error",
            };
            that.appStore.setNotification(message);
          }

          if (
            response.data.status &&
            response.data.data.id != undefined
          ) {
            that.appStore.closewindow({
              status: true,
              domain: "",
              is_completed: true,
            });
          }

          if (
            response.data.status &&
            response.data.data.data != undefined
          ) {
            if (that.connect_form.new_url != "") {
              if (that.appStore.callback != undefined) {
                if (
                  !that.appStore.callback.is_completed &&
                  that.appStore.callback.domain == ""
                ) {
                  that.callback_window = window.open(
                    response.data.data.data,
                    "_blank",
                    "width=1440,height=720",
                  );
                }
              }
            }

            that.appStore.closewindow({
              status: true,
              domain: data.new_url,
              is_completed: true,
            });
          }

          that.parent_url = response.data.data.parent_url;
          that.connection_id = response.data.data.id;
        })
        .catch(() => {

          that.sitepushstatus = "connect-failed";
        })
        .finally(() => { });
    },
    updateSitePushStatus(status) {
      this.sitepushstatus = status;
    },
    fetchConnection(url) {
      const that = this;
      axios
        .post("/api/v1/fetch-connection", { url: url })
        .then((response) => {
          if (response.data.data.connection_status) {
            that.sitePushConnect(that.site_push_connect_data);
          } else {
            this.sitepushstatus = "failed";
          }
        })
        .catch(() => { })
        .finally(() => { });
    },
    async checkStagingSite(url, type = "migrate") {
      await axios
        .post("/api/v1/check-is-site-staging", { url: url })
        .then((response) => {
          if (response.data.status == "0") {
            this.sitepushstatus = "already_exists";
            //   let message = {
            //     heading: url,
            //     subHeading: trans('already_staging_site'),
            //     type: "info",
            //   };
            //   this.appStore.setNotification(message);
            this.siteDetail.connect = response.data.data;
            this.showDisconnectSiteModal = true;
          } else {
            if (type === "migrate") {
              this.sitepushstatus = "pending";
              this.isOpenSitePushModal = true;
            } else {
              return true;
            }
          }
        })
        .catch(() => { })
        .finally(() => { });
    },
    onCommonDialogClose() {
      this.warning = false;
      this.deleteSitePermanently = false;
    },
    getAtarimLink(url) {
      let email = this.user.email;
      if (this.atarimIntegration) {
        email = this.atarimIntegration.input_values["email"];
      }
      const names = this.user.name.split(" ");
      return (
        this.atarimAppUrl +
        "?_from=instawp&email=" +
        `${email}&url=${url}&first_name=${names[0]}&last_name=${names[1] ?? names[0]}&type=one-click`
      );
    },
    // onAtarimLinkClick(site) {
    //   axios.post(`/api/v2/on-atarim-collobration/${site.id}`, {})
    // },
    onViewCredClose() {
      this.showViewCredDialog = false;
    },
    onRefreshChange(value) {
      this.refresh = value;
    },
    onDeleteSiteModalClose() {
      this.showDeleteSiteModal = false;
    },
    onSiteDeletion() {
      this.showDeleteSiteModal = false;
      this.pagination.currentPage = 1;

      this.fetchData();
      this.$inertia.get(
        route("staging"),
        {},
        { preserveState: true, only: ["site_count"] },
      );
    },
    onBulkDeleteSiteModalClose() {
      this.showBulkDeleteSiteModal = false;
    },
    onBulkDeleteSite() {
      this.showBulkDeleteSiteModal = false;
      this.bulkSelection = [];
      this.pagination.currentPage = 1;
      this.fetchData();
      this.$inertia.get(
        route("staging"),
        {},
        { preserveState: true, only: ["site_count"] },
      );
    },
    promptMailTrapIntegration(site) {
      this.selectedSiteForMailTrapIntegration = site;
      this.showSelectMailTrapIntegrationModal = true;
    },
    onSelectMailTrapIntegrationClose() {
      (this.showSelectMailTrapIntegrationModal = false),
        (this.selectedSiteForMailTrapIntegration = null);
    },
    isMailTrapIntegrated(site) {
      return this.getUserIntegrationResources.findIndex(
        (i) =>
          i.resource_type == `App\\Models\\Site` &&
          i.resource_id == site.id,
      ) == -1
        ? false
        : true;
    },
    promptRemoveMailTrapIntegration(site) {
      this.selectedSiteForMailTrapDeletion = site;
      this.showDeleteMailTrapIntegrationModal = true;
    },
    onRemoveMailTrapIntegrationClose() {
      (this.showDeleteMailTrapIntegrationModal = false),
        (this.selectedSiteForMailTrapDeletion = null);
    },
    onDisconnectSiteModalClose() {
      this.showDisconnectSiteModal = false;
    },
    onDisconnectSite() {
      this.showDisconnectSiteModal = false;
    },
    addSiteOpenEvent(siteId) {
      this.appStore.addSiteOpenedEvent(
        siteId,
        window.location.href,
        "site",
      );
    },
    getHostingProviders() {
      axios
        .get("/api/v2/hosting-providers")
        .then((response) => {
          this.preferredHostingProviders = response.data.data.preferred;
          this.otherHostingProviders = response.data.data.others;
        })
        .catch((error) => {
          const message = {
            heading: trans('failed'),
            subHeading: error?.response?.data.message || error.message,
            type: "error",
          };
          this.appStore.setNotification(message);
        })
    },
    openViewCred(site) {
      this.site = site;
      const child = this.$refs.viewCredModal;
      child.openModal();
    },
    expiryPercentage(site) {
      if (site.is_reserved) {
        return false;
      } else {
        return getExpiryPercentage(
          site.expired_at,
          this.teamAllow?.expiry_days,
        ).toFixed(0);
      }
    },
    perPageItem(value) {
      this.perPageValue = value;
      localStorage.setItem('pagination.sites.per_page', value);
      this.selectedBaseOption = { name: value };
      this.pagination.currentPage = 1;
      this.fetchData();
    },
    handleSelectionChange(selectedItems) {
      this.bulkSelection = selectedItems.map((item) => item.id);
      this.sites.forEach((item)=>{
        if(this.bulkSelection.includes(item.id)){
          item.selected = true;
        }else{
          item.selected = false;
        }
      })
    },
    openGoLiveModal(site) {
      this.$emit('go-live', site)
    },
  }
};
</script>


<style src="@suadelabs/vue3-multiselect/dist/vue3-multiselect.css"></style>
<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Kanit&display=swap');

.td-drag svg {
  width: 14px;
  margin-right: 15px;
  display: inline-block;
  margin-top: -4px;
}

.td-drag {
  display: flex;
  content: "";
  z-index: 3;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #c8dadf;
  outline: 2px dashed #92b0b3;
  outline-offset: -5px;
  -webkit-transition: outline-offset 0.15s ease-in-out,
    background-color 0.15s linear;
  transition: outline-offset 0.15s ease-in-out, background-color 0.15s linear;
  align-items: center;
  justify-content: center;
}

.mw-164 {
  min-width: 164px;
}

.show-tag-animation-enter-from,
.show-tag-animation-leave-to {
  opacity: 0;
  transform: translateY(-4px);
}

.show-tag-animation-enter-to,
.show-tag-animation-leave-from {
  opacity: 1;
  transform: translateY(0px);
}

.show-tag-animation-enter-active,
.show-tag-animation-leave-active {
  transition: all 0.5s;
}

.customhover:hover .tag-hover {
  opacity: 1
}


.badge-close-font {
  font-family: 'Kanit', sans-serif;
  background: transparent;
}

.c-form-input {
  border-radius: 40px;
}

.site-url {
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.2s linear;
}

.site-url-hover {
  visibility: visible;
  opacity: 1;
}

.data-v-tooltip::after {
  top: -5px !important;
}

.tag_txt {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.info-url {
  border-bottom: 1px dashed theme('colors.secondary.800');
}

.info-url:hover {
  color: theme('colors.secondary.800');
}

.site-table-shadow {
  box-shadow: none;
}

.custom-width {
  width: 160px !important;
}

.action-btn-shadow {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
}


@media (min-width: 500px) {
  .custom-width {
    width: 285px !important;
  }
}

@media (min-width: 620px) {
  .custom-width {
    width: 380px !important;
  }
}

@media (min-width: 744px) {
  .custom-width {
    width: 295px !important;
  }
}

@media (min-width: 992px) {
  .site-table-shadow {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px rgba(0, 0, 0, 0.06);
  }

  .custom-width {
    width: 210px !important;
  }

  .url-custom-width {
    max-width: 277px;
  }

  .time-expire-custom-width {
    min-width: 255px !important;
  }

  .disk-usage-custom-width {
    min-width: 144px !important;
  }
}

::-webkit-scrollbar {
  width: 3px;
  height: 0px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: theme('colors.primary.900');
  border-radius: 8px;
}

:focus-visible {
  outline: none !important;
}

.data-v-tooltip::after {
  top: -5px !important;
  display: flex;
  flex-wrap: wrap;
  word-break: break-all;
  max-width: 250px;
}

.bg-shadow:after {
  content: "";
  width: 53%;
  height: 60%;
  position: absolute;
  top: 50px;
  left: 117px;
  z-index: -1;
  --webkit-filter: blur(40px);
  filter: blur(40px);
  background: linear-gradient(90deg, #44FF9A -0.55%, #44B0FF 22.86%, #8B44FF 48.36%, #FF6644 73.33%, #EBFF70 99.34%);
}

tr.sites_tr input.unchecked {
  opacity: 0;
}

tr.sites_tr:hover input.unchecked {
  opacity: 1;
}

tr.sites_tr input.checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
  background-color: theme('colors.primary.900');
  border-color: theme('colors.primary.900');
}

tr.sites_tr input.unchecked {
  border-color: #D1D5DB;
}

tr.sites_tr input {
  border-color: #D1D5DB;
}

.command-modal-footer {
  box-shadow: 0px 9.80669px 86.8593px rgba(0, 0, 0, 0.13);
}

.provide-modal ::-webkit-scrollbar-thumb {
  background: #BCC4DA !important;
  border-radius: 8px;
}

.search-input {
  min-width: 730px;
  max-width: 730px;
}

.clock-text {
  max-width: 150px;
}

.copy-btn {
  padding: 10px 24px;
  border: 1px solid #D1D5DB;
  border-radius: 0px 6px 6px 0px;
  display: flex;
  position: absolute;
  top: 24px;
  right: 0;
  cursor: pointer;
}

input[type="search"]::-webkit-search-cancel-button {
  cursor: pointer !important;
}

@media screen and (max-width: 1200px) {

  /* .tableSite-width{
        width:100%;
        overflow-x: auto;
      } */
  .rotate-icon {
    transform: rotate(90deg);
  }

}

@media screen and (max-width: 991px) {
  .table-width {
    width: 100%;
    overflow-x: auto;

  }

  .version-modal {
    max-height: 600px;
    overflow: auto;
  }

  .tableSite-width {
    width: unset;
    overflow-x: unset;
  }

  .search-input {
    min-width: 100%;
  }


}

@media (max-width: 475px) {
  .site-url {
    overflow: hidden;
    display: none;
  }

  .copy-btn {
    position: unset;
    margin-top: 8px;
    width: 116px;
    border-radius: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
  background-color: #bebebe;
}
</style>
