<template>
  <CPopover
    :auto-close="autoClose"
    panel-class="w-[254px]"
  >
    <template #popoverButton>
      <IconButton
        btn-type="gray-outline-btn"
        :icon-name="selectedTags.length > 0 || selectedUsers.length > 0 ? 'SolidFilterIcon' : 'OutlineFilterIcon'"
        icon-color="text-grayCust-800"
        size="btn-md"
        extra-class="px-2"
      >
        <template
          v-if="selectedTags.length > 0 || selectedUsers.length > 0"
          #extra-content
        >
          <span class="text-sm font-medium text-primary-800 ml-2 bg-primary-275 px-2 py-0.5 rounded-full">
            {{ totalSelectedCount }}
          </span>
        </template>
      </IconButton>
    </template>
    <template #popoverOptions>
      <div class="mx-4 my-2 border-b border-grayCust-160">
        <CommonTab
          v-if="isConnectedHosting"
          :tab-list="hostingTabs"
          :selected-tab="selectedTab"
          btn-type="underline"
          @update:selected-tab="updateSelectedTab"
        />
        <CommonTab
          v-else
          :tab-list="tabs"
          :selected-tab="selectedTab"
          btn-type="underline"
          @update:selected-tab="updateSelectedTab"
        />
      </div>

      <div v-if="selectedTab === 1">
        <div class="px-4 my-3">
          <CommonInput
            id="search"
            placeholder="Search tag"
            image-left="/images/search.svg"
            extra-class="w-full"
            :model-value="tagInputValue"
            @input="updateTagInputValue"
          />
        </div>
        <div class="overflow-y-auto max-h-[200px] custom-scrollbar-v2">
          <div
            v-for="tag in filteredTags"
            :key="tag.id"
            class="flex items-center gap-3 py-1.5 px-4 cursor-pointer hover:bg-grayCust-100"
            @click="handleTagClick(tag)"
          >
            <Checkbox
              :checked="selectedTags.includes(tag.id)"
              type="primary"
            />
            <Tag
              :text="tag.name"
              :bg-color="tag.color_code + '1a'"
              :color="tag.color_code"
            />
          </div>
        </div>
      </div>
      <div v-if="selectedTab === 2">
        <div class="px-4 my-3">
          <CommonInput
            id="search"
            placeholder="Search user"
            image-left="/images/search.svg"
            extra-class="w-full"
            :model-value="userInputValue"
            @input="updateUserInputValue"
          />
        </div>
        <div class="overflow-y-auto max-h-[200px] custom-scrollbar-v2">
          <div
            v-for="user in filteredUserList"
            :key="user.id"
            class="flex items-center gap-3 py-1.5 px-4 cursor-pointer hover:bg-grayCust-100"
            @click="handleUserClick(user)"
          >
            <Checkbox
              :checked="selectedUsers.includes(user.id)"
              type="primary"
            />
            <div class="flex items-center gap-2">
              <img
                v-lazy="user.avatar"
                alt="user avatar"
                class="w-6 h-6 rounded-full"
              >
              <p class="truncate max-w-[180px] text-grayCust-660 text-sm">
                {{ user.name }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div v-if="selectedTab === 3">
        <div class="px-4 my-3">
          <p class="text-sm font-medium text-grayCust-800 mt-4 mb-2">
            Type
          </p>
          <ListboxCommon
            v-model="selectedTypeOption"
            :options="typeOptions"
            placeholder="All"
            label-key="label"
            value-key="value"
            :button-class="'w-full'"
            :options-class="'w-full'"
            @update:value="handleTypeInput"
          />
        </div>
        <div class="px-4 my-3">
          <p class="text-sm font-medium text-grayCust-800 mt-4 mb-2">
            Number of Sites
          </p>
          <ListboxCommon
            v-model="selectedSitesOption"
            :options="sitesOptions" 
            label-key="label"
            placeholder="All"
            value-key="value"
            :button-class="'w-full'"
            :options-class="'w-full'"
            @update:value="handleSitesInput"
          />
        </div>
      </div>

      <div class="px-4 mt-4 mb-2 grid grid-cols-2 gap-2">
        <CButton
          btn-type="danger-outline-btn"
          btn-title="Clear filters"
          @click="clearFilter"
        />
        <CButton
          btn-type="secondary"
          btn-title="Apply"
          @click="filterData"
        />
      </div>
    </template>
  </CPopover>
</template>

<script>
import CButton from '@/app/Common/CommonButton.vue';
import Checkbox from '@/app/Common/CommonCheckbox.vue';
import IconButton from '@/app/Common/CommonIconButton.vue';
import CommonInput from '@/app/Common/CommonInput.vue';
import ListboxCommon from '@/app/Common/CommonListbox.vue';
import CPopover from '@/app/Common/CommonPopover.vue';
import CommonTab from '@/app/Common/CommonTab.vue';
import Tag from '@/app/Common/CommonTag.vue';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { wTrans } from 'laravel-vue-i18n';
import { mapState } from 'pinia';

export default {
    components: {
        CommonTab,
        CPopover,
        IconButton,
        Checkbox,
        Tag,
        CommonInput,
        ListboxCommon,
        CButton
    },

    props: {
        tabType: {
            type: String,
        }
    },
    emits: ['filterData'],
    data() {
        return {
            autoClose: false,
            tabs: [
                { id: 1, title: wTrans("tags") },
                { id: 2, title: wTrans("users") },
                { id: 3, iconName: 'OutlineCogIcon' },
            ],
            hostingTabs: [
                { id: 1, title: wTrans("tags") },
            ],
            tagInputValue: '',
            userInputValue: '',
            selectedTab: 1,
            sitesOptions: [
                { id: 1, label: '10 Sites', value: 10 },
                { id: 2, label: '20 Sites', value: 20 },
                { id: 3, label: '50 Sites', value: 50 },
                { id: 4, label: '100 Sites', value: 100 },
            ],

            typeOptions: [
                { id: 1, label: 'All', value: '' },
                { id: 2, label: 'Active', value: 'active' },
                { id: 3, label: 'Expired', value: 'expired' },
                { id: 4, label: 'Reserved', value: 'reserved' },
            ],

            userList: [],
            selectedTypeOption: '',
            selectedSitesOption: '',
            selectedTags: [],
            selectedUsers: [],
        }
    },
    computed: {
        ...mapState(useAppStore, {
            tags: "tagItems"
        }),
        filteredTags() {
            if (!this.tagInputValue) return this.tags; // Show all tags if no search input

            const lowercasedInput = this.tagInputValue.toLowerCase();
            return this.tags.filter(tag =>
                tag.name.toLowerCase().includes(lowercasedInput)
            );
        },
        filteredUserList() {
            if (!this.userInputValue) return this.userList;
            
            const lowercasedInput = this.userInputValue.toLowerCase();
            return this.userList.filter((user) => {
                return user.name.toLowerCase().indexOf(lowercasedInput) >= 0;
            });
        },
        totalSelectedCount() {
          return this.selectedTags.length + this.selectedUsers.length;
        },

        isConnectedHosting() {
            return this.tabType === 'connected_hosting';
        }
    },
    mounted() {
        this.fetchUsers();
    },

    methods: {
        updateSelectedTab(newTabId) {
            this.selectedTab = newTabId;
        },
       
        handleTypeInput(value) {
            this.selectedTypeOption = value.value;
        },
        handleSitesInput(value) {
            this.selectedSitesOption = value.value;
        },

        handleTagClick(tag) {
            tag.checked = !tag.checked;
            if (tag.checked) {
                this.selectedTags.push(tag.id);
            } else {
                this.selectedTags = this.selectedTags.filter(item => item !== tag.id);
            }
        },
        handleUserClick(user) {
            user.checked = !user.checked;
            if (user.checked) {
                this.selectedUsers.push(user.id);
            } else {
                this.selectedUsers = this.selectedUsers.filter(item => item !== user.id);
            }
        },
        filterData() {
          if (localStorage.getItem('pagination.sites.per_page')) {
            localStorage.removeItem('pagination.sites.per_page');          
          } 
          if (localStorage.getItem('pagination.sites.current_page')) {
            localStorage.removeItem('pagination.sites.current_page');          
          } 
            this.$emit('filterData', { selectedTypeOption: this.selectedTypeOption, selectedSitesOption: this.selectedSitesOption, selectedTags: this.selectedTags, selectedUsers: this.selectedUsers, tagInputValue: this.tagInputValue, userInputValue: this.userInputValue });
            this.autoClose = true;
            setTimeout(() => {
              this.autoClose = false;
            }, 300);
        },
        clearFilter() {
            // Reset arrays and input values
            this.selectedTags = [];
            this.selectedUsers = [];
            this.tagInputValue = '';
            this.userInputValue = '';
            this.selectedTypeOption = '';
            this.selectedSitesOption = '';
            this.term = '';
            
            // Uncheck all tags
            this.tags.forEach(tag => {
                tag.checked = false;
            });
            
            // Uncheck all users
            this.userList.forEach(user => {
                user.checked = false;
            });

            // Emit filter data
            this.$emit('filterData', { 
                selectedTypeOption: this.selectedTypeOption, 
                selectedSitesOption: this.selectedSitesOption, 
                selectedTags: this.selectedTags, 
                selectedUsers: this.selectedUsers,
                tagInputValue: '',
                userInputValue: '',
            });
            this.autoClose = true;
            setTimeout(() => {
              this.autoClose = false;
            }, 300);

        },
        updateTagInputValue(event) {
            this.tagInputValue = event.target.value;
        },
        updateUserInputValue(event) {
            this.userInputValue = event.target.value;
            this.$emit('filterInputValue', { userInputValue: this.userInputValue });
        },
        fetchUsers() {
            let that = this;
            if (that.$page.props.auth.user.current_team.user_id == that.$page.props.auth.user.id || that.$page.props.auth.user.current_team.user_is_admin) {

                this.processing = true;
                let teamId = that.$page.props.auth.user.current_team_id;
                axios
                    .get("/api/v2/teams/" + teamId + "/members")
                    .then((response) => {
                        that.userList = response.data.data;
                        that.userList.forEach((user) => {
                        user.id = user.id;
                        user.name = user.name;
                        user.avatar = user.profile_pic;
                        });
                    })
                    .finally(() => {
                        that.processing = false;
                    });
            } else {
                that.tabs[1].hide = true;
                that.selectedUser = [that.$page.props.auth.user.id];
            }
        },
    }
}
</script>
<style scoped>

::-webkit-scrollbar {
  width: 4px;
  background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
  background-color: #bebebe;
}
</style>