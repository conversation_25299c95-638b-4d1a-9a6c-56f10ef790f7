<template>
  <div class="bg-grayCust-170">
    <Head>
      <title>
        {{ $t("permission_dialog") }} - {{ setting.brandShortName }}
      </title>
    </Head>
    <div class="flex justify-center items-center h-screen">
      <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-center custom-shadow transition-all sm:my-8 sm:w-full sm:max-w-xl sm:p-6 sm:mx-5 lg:mx-0">
        <div>
          <div class="mx-auto flex items-center justify-center">
            <!-- <permission-dialog-logo class="w-full" /> -->
            <img
              :src="cdn(`images/insta-logo.svg`)"
              alt="connect-callback-logo"
            >
          </div>
          <div class="mt-3 text-center sm:mt-5">
            <div class="mt-6 lg:mt-8">
              <p class="text-sm text-grayCust-550 font-medium">
                {{ $t("plugins_installation_in_progress") }}<br>
              </p>
            </div>
            <div
              v-if="!errorMessage"
              class="mt-8 lg:mt-11 flex items-center justify-center"
            >
              <h3 class="text-grayCust-800 text-sm lg:text-lg font-medium flex items-center">
                <span class="text-primary-900 text-xs lg:text-lg font-medium flex items-center cursor-pointer ml-1.5 lg:ml-2">
                  {{ $t("installing_instaWP_connect_plugin") }}
                </span>
              </h3>
            </div>
            <div
              v-else
              class="mt-8 lg:mt-11 flex items-center justify-center"
            >
              <h3 class="text-grayCust-800 text-sm lg:text-lg font-medium flex items-center">
                <span class="text-redCust-900 text-xs lg:text-lg font-medium flex items-center cursor-pointer ml-1.5 lg:ml-2">
                  {{ errorMessage }}
                </span>
              </h3>
            </div>
          </div>
        </div>
        <div
          v-if="!errorMessage"
          class="mt-6 sm:mt-8 lg:mt-11 sm:grid-flow-row-dense sm:gap-3"
        >
          <button
            type="button"
            class="inline-flex w-64 justify-center items-center"
            :class="defaultClass"
          >
            <svg
              v-if="installingplugin"
              role="status"
              class="inline mr-3 w-4 h-4 animate-spin"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="#E5E7EB"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentColor"
              />
            </svg>
            {{ installingplugin ? $t("installing") + '...' : $t("installed") }}
          </button>
        </div>
        <div
          v-else
          class="mt-6 sm:mt-8 lg:mt-11 sm:grid-flow-row-dense sm:gap-3"
        >
          <button
            type="button"
            class="inline-flex w-64 justify-center items-center rounded-md border border-gray-300 bg-white px-4 py-3 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 sm:col-start-1 sm:mt-0 sm:text-sm"
            @click="forceClose"
          >
            {{ $t("close") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import PermissionDialogLogo from "@/components/UpdatedDesignVersion/ImageComponents/PermissionDialog/PermissionDialogLogo.vue";
import AppLayout from '@/Layouts/AppLayout.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { mapStores } from 'pinia';
export default {
    name: "ProfileCom",
    components: {
        // PermissionDialogLogo,
    },
    layout: AppLayout,
    data() {
        return {
            setting: new Setting(),
            process: false,
            installingplugin: true,
            CurrentURLObject: [],
            domain: '',
            defaultClass: 'rounded-md border border-gray-300 bg-white px-4 py-3 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-secondary-800 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm',
            getCurrentURLTimeOut: null,
            status: false,
            errorMessage: ''
        }
    },
    computed: {
        ...mapStores(useAppStore),
    },
    created() {
        window.addEventListener('beforeunload', (event) => {
            event.preventDefault();
            this.closeWindow();
        });
    },
    mounted() {
        this.getCurrentURL(window.location.search);
    },
    unmounted() {
        clearTimeout(this.getCurrentURLTimeOut)
    },
    methods: {
        getCurrentURL(data) {

            let that = this;
            that.process = true;

            axios
                .post("/api/v1/wp-connect-callback-response", data)
                .then((response) => {

                    if (response.data.status) {
                        this.defaultClass = 'rounded-md border border-transparent bg-secondary-800 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-secondary-800 focus:outline-none focus:ring-2 focus:ring-secondary-800 focus:ring-offset-2 sm:col-start-2 sm:text-sm';
                        // that.CurrentURLObject = response.data.data;
                        that.installingplugin = false;
                        that.domain = response.data.data.domain;
                        this.status = true
                        this.getCurrentURLTimeOut = setTimeout(function() { window.close(); }, 2000);

                    } else {
                        this.status = false
                        this.errorMessage = response.data.message
                    }


                })
                .catch(() => {

                })
                .finally(() => {
                    that.process = false;
                });
        },
        closeWindow() {
            this.appStore.closewindow({
                'status': this.status,
                'domain': this.domain,
                'is_completed': this.status
            })
        },
        forceClose() {
            this.getCurrentURLTimeOut = setTimeout(function() { window.close(); }, 1);
        }
    },
};
</script>

<style scoped>
.custom-shadow {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px rgba(0, 0, 0, 0.06);
}
</style>
