<template>
  <div>
    <Head>
      <title>
        {{ $t('pricing') }} - {{ setting.brandShortName }}
      </title>
    </Head>
    <TemplateLayout
      ref="snapshotLayout" 
      :template="template" 
      title="Pricing"
      sub-title="Decide whether to monetize your template and set a suitable price"
    >
      <div>
        <div
          class="card-main"
        >
          <div>
            <div
              v-if="template.team.owner.user_payouts.length == 0 && false"
              class="card-shadow rounded-m mb-6 overflow-y-auto overflow-x-hidden rounded-2xl border border-grayCust-900 bg-grayCust-50 p-4 lg:p-12"
            >
              <EmptyData
                subtitle-class="text-grayCust-500 text-sm font-normal"
                title-class="text-warning-1300 text-sm font-medium"
                button-text="Become a Seller"
                icon="store-empty-image.svg"
                title="Please register as a seller to publish a template to the store."
                subtitle="You have not created any templates. Start by saving a site as a Template"
                @on_button_click="openNewTab"
              />
            </div>
            <template v-else>
              <div class="">
                <div class="p-4">
                  <div v-if="form.is_monetized">
                    <div class="mt-5">
                      <fieldset class="">
                        <div class="space-y-4 sm:flex sm:items-center sm:space-x-5 sm:space-y-0">
                          <div class="group p-0.5 rounded-lg flex bg-gray-100 hover:bg-gray-200">
                            <button
                              v-for="tab in monetizeTypes"
                              :key="tab.id"
                              :class="[
                                'py-1.5 px-3 focus:outline-none text-sm hover:text-gray-900 rounded whitespace-nowrap',
                                tab.value == form.is_paid ? 'bg-white shadow-sm ring-1 ring-black ring-opacity-5' : ''
                              ]"
                              @click="changeMonetizeType(tab)"
                            >
                              <component
                                :is="tab.logo"
                                v-if="tab.logo"
                                class="w-[16px] h-[16px] inline-block mr-1"
                                :class="tab.value == form.is_paid ? 'text-secondary-800 bg-green-50' : 'text-gray-500'"
                                aria-hidden="true"
                              />
                              {{ tab.title }}
                            </button>
                          </div>
                        </div>
                      </fieldset>
                    </div>

                    <div v-if="form.is_paid">
                      <div class="mt-5 flex items-center">
                        <p class="form-label">
                          {{ $t('price') }} :
                        </p>
                        <div class="ml-5">
                          <input
                            v-model="form.price"
                            type="text"
                            class="form-control w-28"
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <TemplateFooter
                    v-if="!form.product_id"
                    :disabled="processing"
                    extra-class="py-2 bg-primary-900 text-white text-sm font-medium focus:ring-2 focus:ring-primary-900 focus:ring-offset-2"
                    :title="$t('save')"
                    @click-event="Save()"
                  />
                  <TemplateFooter
                    v-else
                    :disabled="processing"
                    extra-class="py-2 px-4 bg-primary-900 text-white text-sm font-medium focus:ring-2 focus:ring-primary-900 focus:ring-offset-2"
                    :title="$t('update')"
                    @click-event="Update()"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <ModalPayoutRequired
        :is-open-modal="isOpenModal"
        @cancel="isOpenModal = false"
        @setup-payout="openNewTab"
      />
    </TemplateLayout>
  </div>
</template>

<script>
import EmptyData from "@/Common/EmptyData.vue";
import ModalPayoutRequired from "@/app/Components/Template/ModalPayoutRequired.vue";
import TemplateFooter from "@/app/Components/Template/Edit/Footer.vue";
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import { TagIcon } from "@heroicons/vue/outline";
import axios from 'axios';
import { trans, wTrans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import { route } from 'ziggy-js';
import TemplateLayout from './TemplateLayout.vue';

export default {
  name: "TemplateMonetize",
  components: {
    TemplateLayout,
    TemplateFooter, EmptyData, ModalPayoutRequired
  },

  props: ["template", "user"],

  data() {
    return {
      setting: new Setting(),
      form: {
        price: "",
        is_paid: false,
        is_monetized: true,
        template_id: this.template.id,
        payment_type: "",
        payment_interval: "",
        product_id: null,
      },
      processing: false,
      errors: {
        price: "",
        payment_type: "",
        payment_interval: "",
      },
      activeTabName: "monetize",
      resourceType: "template",
      monetizeTypes: [
        {
          value: false,
          id: "free",
          title: wTrans('free'),
          logo: TagIcon
        },
        {
          value: true,
          id: "paid",
          title: wTrans('paid'),
          logo: 'OutlineCurrencyDollarIcon'
        },
      ],
      isPublic: this.template.mark_as_public,
      isOpenModal: false,

    };
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, ["teamCan", "featureAvailableFrom"]),
  },
  mounted() {
    
    if (this.template.is_monetized) {
      this.form.is_monetized = true;
      this.getProductDetails();
    }
  },
  methods: {
    Save() {
      const that = this;
      const resourceType = that.resourceType;
      const formURL = `/api/v2/${resourceType}/products`;
      this.saveUpdateProduct(formURL, 'post');
    },
    openNewTab() {
      window.open('https://instawp.com/become-a-seller/', '_blank', 'noreferrer');
    },
    Update() {
      const that = this;
      const productId = that.form.product_id;
      const resourceType = that.resourceType;
      const formURL = `/api/v2/${resourceType}/products/` + productId;
      this.saveUpdateProduct(formURL, 'patch');
    },
    getProductDetails() {
      const that = this;
      const resourceType = that.resourceType;
      axios
        .get(`/api/v2/${resourceType}/product/${that.form.template_id}`)
        .then((response) => {
          that.form.is_paid = Boolean(response.data.data.is_paid);
          that.form.payment_interval =
            response.data.data.payment_interval;
          that.form.payment_type = response.data.data.payment_type;
          that.form.price = response.data.data.price;
          that.form.product_id = response.data.data.id;
        })
        .catch(() => {
          that.form.is_monetized = false;
        });
    },
    saveUpdateProduct(formURL, method) {
      const that = this;
      that.processing = true;
      that.errors = [];
      if (!that.form.is_monetized || !that.form.is_paid) {
        that.form.payment_type = "";
        that.form.payment_interval = "";
        that.form.price = "";
      }
      axios({
        method: method,
        url: formURL,
        data: that.form
      })
        .then((response) => {
          const notification = {
            heading: trans("success"),
            subHeading: response.data.message,
            type: "success",
          };
          that.showNotification(notification);
          if (that.form.is_monetized) {
            that.getProductDetails();
          } else {
            that.form.product_id = null;
          }
          this.$refs.snapshotLayout.handleMonetizationChange(that.form.is_monetized);
          this.$emit('update:template', {...that.template, is_monetized: that.form.is_monetized});
        })
        .catch((error) => {
          that.errors = error.response.data.errors;
        })
        .finally(() => {
          that.processing = false;
        });
    },
    changeMonetizeType(item) {
      this.form.is_paid = item.value;
      this.isOpenModal = false;
      if (this.form.is_paid) {
        this.form.payment_type = 'one_time';
        if (this.template.team.owner.user_payouts.length == 0) {
          this.isOpenModal = true;
          this.form.is_paid = false;
        }
      } else {
        this.form.payment_interval = "";
        this.form.price = "";
        this.form.payment_type = "";
      }
    },
    changeIsMonetize() {
      if (!this.form.is_monetized) {
        this.form.is_paid = false;
        this.form.payment_type = "";
        this.form.payment_interval = "";
        this.form.price = "";
      }
    },
    showNotification(notification) {
      this.appStore.setNotification(notification);
    },
    changeStatus(status) {
      this.isPublic = status;
      this.$inertia.visit(route("template.edit.setup", this.template));
    },
    setupPayout() {
      this.isOpenModal = false;
    },
    showUpgradeWarning(feature) {
      const msg = {
        subHeading: '',
        planMessage: this.featureAvailableFrom[feature] ? trans('this_feature_is_available_in_plan_and_above', { planName: this.featureAvailableFrom[feature] }) : null,
        feature,
        triggerRef: `template_edit_monetize_page_${feature}`
      }
      this.appStore.setUpgradeWarning(msg);
    },
  },
};
</script>

<style scoped>
.fav-width {
  width: 900px;
}

.shadow {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
}

@media screen and (max-width:991px) {
  .fav-width {
    width: 100%;
  }
}
</style>