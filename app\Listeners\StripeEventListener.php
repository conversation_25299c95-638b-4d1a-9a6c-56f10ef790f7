<?php

namespace App\Listeners;

use App\Events\GroupOrderConfirmed;
use App\Events\Order3DPayment;
use App\Events\Order3DSubPayment;
use App\Events\OrderConfirmed;
use App\Interfaces\CommonConstants;
use App\Jobs\LogUserActivity;
use App\Jobs\RefundCardVerificationAmount;
use App\Jobs\UserPlanUsageScan;
use App\Mail\BeforeTrialEnds;
use App\Mail\OrderCompleted;
use App\Mail\OrderCreatedSeller;
use App\Mail\TrialStarted;
use App\Models\ConversionUser;
use App\Models\CreditHistory;
use App\Models\HostingConnection;
use App\Models\Order;
use App\Models\Plan;
use App\Models\StoreProductVersion;
use App\Models\Subscription;
use App\Models\User;
use App\Services\StripeService;
use App\Traits\UserAuthorization;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Events\WebhookReceived;
use Stripe\StripeClient;
use Symfony\Component\HttpFoundation\Response;

class StripeEventListener
{
    use UserAuthorization;

    /**
     * Handle received Stripe webhooks.
     *
     * @return void
     */
    public function handle(WebhookReceived $event)
    {
        if ($event->payload['type'] === 'customer.subscription.trial_will_end') {
            $subscription = $event->payload['data']['object'];
            $user = User::where('stripe_id', $subscription['customer'])->first();
            $plan = Plan::where('stripe_plan_id', $subscription['plan']['id'])->first();

            if (!empty($user)) {
                $data = [];
                $data['user_id'] = $user->id;
                $data['name'] = $user->name;
                $data['pm_type'] = $user->pm_type;
                $data['pm_last_four'] = $user->pm_last_four;
                $data['trial_end'] = gmdate('Y-m-d H:i', $subscription['trial_end']);
                $data['plan_amount'] = $subscription['plan']['amount'] / 100;
                $data['plan_name'] = (!empty($plan)) ? $plan->name : '';
                Mail::to($user->email)->queue((new BeforeTrialEnds($data))->onQueue('emails'));
                Log::channel('payment')->info('stripe event customer.subscription.trial_will_end: mail sent.', ['customer' => $subscription['customer']]);
            } else {
                Log::channel('payment')->error('stripe event customer.subscription.trial_will_end: user not found.', ['customer' => $subscription['customer']]);
            }
        } elseif ($event->payload['type'] === 'customer.subscription.deleted') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('customer.subscription.deleted', [$data]);
            $subscription = $event->payload['data']['object'];
            if (!empty($subscription['metadata']['Type']) && in_array($subscription['metadata']['Type'], CommonConstants::OtherSubscriptionTypes)) {
                return new Response($subscription['metadata']['Type'], 200);
            }
            if (empty($subscription['plan'])) {
                return new Response('', 200);
            }
            $user = User::where('stripe_id', $subscription['customer'])->first();
            $plan = Plan::where('stripe_plan_id', $subscription['plan']['id'])->withTrashed()->first();
            $reason = '';
            if (isset($data['cancellation_details']) && isset($data['cancellation_details']['reason'])) {
                $reason = $data['cancellation_details']['reason'];
            }
            $subscription = Subscription::where('stripe_id', $data['id'])->first();
            $eventData = [
                'event' => 'cancel_subscription',
                'current_plan' => $plan->name,
                'subscription_end_date' => date('M d, Y', strtotime($subscription->ends_at)) ?? 'end of current billing cycle',
                'resume_subscription_link' => route('subscription.resume'),
                'reason' => $reason,
            ];
            dispatch(new LogUserActivity($user, $eventData))->onQueue('user_engagement');
            if ($user) {
                $user->payment_due_warn_count = 0;
                $user->save();
            }
        } elseif ($event->payload['type'] === 'invoice.payment_failed') {
            $data = $event->payload['data']['object'];
            Log::channel('payment')->debug('invoice.payment_failed', [$data]);
            if (!empty($data['payment_intent'])) {
                $stripe = new \Stripe\StripeClient(config('cashier.secret'));
                $intent_status = $stripe->paymentIntents->retrieve($data['payment_intent'])->status;
                if ($intent_status == 'requires_action') {
                    return $intent_status;
                }
            }
            $billing_reason = $data['billing_reason'];
            $customer_id = $data['customer'];
            $user = User::where('stripe_id', $customer_id)->first();
            $plan_data = $data['lines']['data'];
            $revert_plan = true;
            $paid = $data['paid'];
            $status = $data['status'];
            if (count($plan_data) > 1) {
                $revert_plan = false;
            }
            if (!$paid && $billing_reason == 'subscription_create' && $revert_plan && (!empty($status) && $status == 'open')) {
                $stripe = new \Stripe\StripeClient(config('cashier.secret'));
                $subscription = $stripe->subscriptions->retrieve(
                    $data['subscription'],
                    []
                );
                if (!empty($subscription['metadata']['Type']) && in_array($subscription['metadata']['Type'], CommonConstants::OtherSubscriptionTypes)) {
                    return new Response($subscription['metadata']['Type'], 200);
                }
                try {
                    $subscription = $user->active_subscription;
                    if ($subscription && $subscription->stripe_id == $data['subscription']) {
                        // Get invoices from Stripe
                        $AllInvoices = $user->invoicesIncludingPending();
                        // Filter to get only unpaid invoices
                        $unpaidInvoices = $AllInvoices->filter(function ($invoice) {
                            return !$invoice->paid;
                        });
                        if ($unpaidInvoices->count() > 0) {
                            // Cancel the subscription immediately without proration
                            $stripe->subscriptions->cancel($subscription->stripe_id, ['prorate' => false]);
                            // Update in Laravel Cashier (optional, if needed)
                            // $subscription->markAsCanceled();
                        } else {
                            // Default cancel (proration applies)
                            $subscription->cancelNow();
                        }

                        if ($user->payment_due_warn_count) {
                            $user->payment_due_warn_count = 0;
                            $user->save();
                        }
                        Log::channel('payment')->info('stripe event invoice.payment_failed: Subscription canceled.', ['customer' => $data['customer']]);
                    }
                } catch (Exception $ex) {
                    Log::channel('payment')->error('stripe event invoice.payment_failed: ', [$ex->getMessage()]);
                    report($ex);
                }
            }
        } elseif ($event->payload['type'] === 'invoice.paid') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('invoice.paid', [$data]);
            $this->invoicePaid($data);
        } elseif ($event->payload['type'] === 'charge.succeeded') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('charge.succeeded', [$data]);
            $this->chargeSucceeded($data);
        } elseif ($event->payload['type'] === 'charge.failed') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('charge.failed', [$data]);
            $this->chargeFailed($data);
        } elseif ($event->payload['type'] === 'payment_intent.requires_action') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('payment_intent.requires_action', [$data]);
            $order = Order::where(['stripe_txn_id' => $data['id'], 'status' => CommonConstants::INIT])->first();
            if ($order) {
                if (!empty($order->order_meta['group_token'])) {
                    if ($order->product->payment_type == CommonConstants::RECURRING) {
                        Order3DSubPayment::dispatch($order, $data['next_action']['redirect_to_url']['url']);
                    } else {
                        Order3DPayment::dispatch($order, $data['next_action']['redirect_to_url']['url']);
                    }
                } else {
                    OrderConfirmed::dispatch($order, '', $data['next_action']['redirect_to_url']['url']);
                }
            }
        } elseif ($event->payload['type'] === 'payment_intent.payment_failed') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('payment_intent.payment_failed', [$data]);
            $order = Order::where(['stripe_txn_id' => $data['id'], 'status' => CommonConstants::INIT])->first();
            if ($order) {
                $order->update(['status' => CommonConstants::ERROR]);
                OrderConfirmed::dispatch($order, $data['last_payment_error']['message']);
            }
        } elseif ($event->payload['type'] === 'payment_intent.succeeded') {
            $data = $event->payload['data']['object'];
            \Log::channel('payment')->debug('payment_intent.succeeded', [$data]);
            if (isset($data['metadata']['type']) && $data['metadata']['type'] === 'card_verification' && $data['status'] === 'succeeded') {
                $user = User::where('stripe_id', $data['customer'])->first();
                dispatch(new RefundCardVerificationAmount($user, $data['id'], tenant()));
            }
        } elseif ($event->payload['type'] === 'customer.subscription.updated') {
            $subscription = $event->payload['data']['object'];
            $user = User::where('stripe_id', $data['id'] ?? 'na')->first();
            $tenant = tenant();
            if ($tenant && $user) {
                UserPlanUsageScan::dispatch($tenant, $user);
            }
            \Log::channel('payment')->debug('customer.subscription.updated', [$subscription]);
        } elseif ($event->payload['type'] === 'customer.subscription.created') {
            $subscription = $event->payload['data']['object'];

            \Log::channel('payment')->debug('customer.subscription.created', [$subscription]);
            $plan = Plan::where('stripe_plan_id', $subscription['plan']['id'])->first();
            if ($plan != null) {
                $user = User::where('stripe_id', $subscription['customer'])->first();

                if (!empty($user)) {
                    if (empty($subscription['trial_end'])) {
                        Log::channel('payment')->info('stripe event customer.subscription.created without trial program.', ['customer' => $subscription['customer']]);
                    } else {
                        $data = [];
                        $data['user_id'] = $user->id;
                        $data['name'] = $user->name;
                        $data['pm_type'] = $user->pm_type;
                        $data['pm_last_four'] = $user->pm_last_four;
                        $data['trial_start'] = gmdate('Y-m-d', $subscription['trial_start']);
                        $data['trial_end'] = gmdate('Y-m-d', $subscription['trial_end']);
                        $data['plan_amount'] = $subscription['plan']['amount'] / 100;
                        $data['plan_name'] = (!empty($plan)) ? $plan->name : '';
                        $data['interval'] = (!empty($plan)) ? ($plan->interval == 2 ? 'year' : 'month') : '';
                        Mail::to($user->email)->queue((new TrialStarted($data))->onQueue('emails'));
                        Log::channel('payment')->info('stripe event customer.subscription.created with trial program: mail sent.', ['customer' => $subscription['customer']]);
                    }
                } else {
                    Log::channel('payment')->error('stripe event customer.subscription.created with trial program: user not found.', ['customer' => $subscription['customer']]);
                }
            }
        } elseif ($event->payload['type'] === 'invoice.payment_succeeded') {
            $data = $event->payload['data']['object'];
            Log::channel('payment')->debug('invoice.payment_succeeded', [$data]);
            $order = Order::where(['stripe_txn_id' => $data['subscription']])->first();
            if ($order && $order->invoices()->where('invoice_id', $data['id'])->count() == 0) {
                $this->connectTransferEntry($order, $data);
            }
        } elseif ($event->payload['type'] === 'payment_method.attached') {
        } elseif ($event->payload['type'] === 'payment_method.detached') {
        } elseif ($event->payload['type'] === 'customer.updated') {
            $data = $event->payload['data']['object'];
            $previous_attributes = $event->payload['data']['previous_attributes'] ?? null;

            $user = User::where('stripe_id', $data['id'] ?? 'na')->first();
            if ($user) {
                // Check if it's Customer Balance Transaction
                if ($previous_attributes && isset($previous_attributes['balance'])) {
                    $metadata = $data['metadata'] ?? [];
                    $status = $user->user_metas()->updateOrCreate(
                        ['name' => 'credit_balance'],
                        ['value' => $data['balance'] / (-100)]
                    );
                    Log::channel('payment')->info('credit_balance customer.updated', ['webhook_data' => $data, 'status' => $status]);
                    // Credited value will be in minus.
                    if ($previous_attributes['balance'] > $data['balance']) {
                        // Credit Transaction
                        Log::channel('payment')->info('Credit Transaction', ['user' => $user->id]);
                    } else {
                        if (isset($metadata['last_balance_update']) && $metadata['last_balance_update'] == 'Expire credit') {
                            // Remove fields: last_balance_update
                            $newMetadata = Arr::except($metadata, ['last_balance_update']);
                            $stripe = new StripeClient(config('cashier.secret'));
                            $stripe->customers->update(
                                $data['id'],
                                [
                                    'metadata' => $newMetadata,
                                ]
                            );
                        } else {
                            $credit_histories = CreditHistory::where('user_id', $user->id)
                                ->whereNotNull('stripe_credit_id')
                                ->whereColumn('used_amount', '<', 'amount')
                                ->where('credit_status', 'credited')
                                ->orderby('id', 'ASC')
                                ->get();
                            if ($credit_histories->count()) {

                                // calculate used balance in this transaction in positive amount.
                                $usedBalance = ($data['balance'] - $previous_attributes['balance']) / 100;
                                Log::channel('payment')->info('Debit Transaction usedBalance', ['user' => $user->id, 'usedBalance' => $usedBalance]);
                                foreach ($credit_histories as $credit_history) {
                                    // Calculate the available amount for this record
                                    $availableAmount = $credit_history->amount - $credit_history->used_amount;
                                    // Determine the amount to be allocated
                                    $allocateAmount = min($availableAmount, $usedBalance);
                                    // Update the used_amount
                                    $credit_history->used_amount += $allocateAmount;
                                    $credit_history->save();
                                    Log::channel('payment')->info('Debit Transaction credit history updated', ['user' => $user->id, 'credit_history' => $credit_history]);
                                    if ($credit_history->credit_note === 'plan_purchase' && $credit_history->used_amount >= $credit_history->amount) {
                                        // add credit to reference user. (referred by user)
                                        $reference_credit_history = CreditHistory::where('referral_user_id', $user->id)->whereNull('stripe_credit_id')->where('credit_status', 'pending')->first();
                                        if ($reference_credit_history) {
                                            $stripeService = new StripeService;
                                            $status = $stripeService->createUserCredit(['credit_history_id' => $reference_credit_history->id]);
                                            if ($status) {
                                                Log::channel('payment')->info('signup conversion credit', ['credit_history_id' => $reference_credit_history->id]);
                                            }
                                        }
                                    }
                                    // Decrease the total balance
                                    $usedBalance -= $allocateAmount;
                                    // Stop if the used balance is depleted
                                    if ($usedBalance <= 0) {
                                        break;
                                    }
                                }
                            }
                        }
                        // Debit Transaction
                        Log::channel('payment')->info('Debit Transaction', ['user' => $user->id]);
                    }
                }
            }
        } elseif ($event->payload['type'] == 'invoice.finalized' && config('const.misc.billing_email_on_another_email', true)) {
            $invoice = $event->payload['data']['object'];
            $user = User::where('stripe_id', $invoice['customer'])->first();
            if (!empty($user->billing_address?->email)) {
                $email = $user->billing_address->email;
                $data = [
                    'user_id' => $user->id,
                    'customer_name' => $user->name,
                    'invoice_id' => $invoice['id'],
                    'amount' => $invoice['amount_due'] / 100,
                    'currency' => $invoice['currency'],
                    'status' => $invoice['status'],
                    'created' => $invoice['created'],
                    'link' => $invoice['hosted_invoice_url'] ?? '',
                    'invoice_pdf' => $invoice['invoice_pdf'] ?? '',
                ];
                Mail::to($email)->send(new \App\Mail\InvoiceMail($data));
            }
        }
    }

    /**
     * Connect transfer entry for an order using Stripe API.
     *
     * @param  Order  $order  The order object.
     * @param  array  $data  An array of data containing charge, amount_paid, id, and status.
     * @return void
     */
    public function connectTransferEntry(Order $order, $data = [])
    {
        try {
            $stripe = new \Stripe\StripeClient(config('cashier.secret'));

            $charge = $stripe->charges->retrieve($data['charge'], []);
            $transaction = $stripe->balanceTransactions->retrieve(
                $charge->balance_transaction,
                []
            );
            $commissionPercentage = 0;
            if (!empty(config('const.commission.template_percentage'))) {
                $commissionPercentage = config('const.commission.template_percentage');
            }

            if (!empty($order->product) && !empty($order->product->user)) {
                $productOwner = $order->product->user;
                $commission = $productOwner->user_metas()->where('name', 'commission_per')->first();
                if ($commission) {
                    $commissionPercentage = $commission->value;
                }
            }
            $amount = $data['amount_paid'] / 100;
            $finalAmount = $amount - ($amount * $commissionPercentage) / 100;
            $dataArr = [
                'amount' => $amount,
                'invoice_id' => $data['id'],
                'status' => $data['status'],
                'pg_amount' => $transaction->fee / 100,
                'status' => CommonConstants::COMPLETED,
                'commission_iwp' => ($amount - $finalAmount - $transaction->fee / 100),
                'final_amount' => $finalAmount,
                'date' => Carbon::createFromTimestamp($data['created']),
                'transfer' => CommonConstants::PENDING,
            ];
            $invoice = $order->invoices()->create($dataArr);
            $invoiceMeta = [];
            $payoutSettings = $order->product->user->userDefaultPayout();
            if ($payoutSettings && $payoutSettings->account_details['capability'] == CommonConstants::ACTIVE) {
                $invoiceMeta['account_id'] = $payoutSettings->account_details['account_id'];
                // Create a Transfer to a connected account (later):
                $stripe->transfers->create([
                    'amount' => $dataArr['final_amount'] * 100,
                    'currency' => 'usd',
                    'destination' => $invoiceMeta['account_id'],
                    'transfer_group' => $order->token,
                ]);
                $invoice->transfer = CommonConstants::COMPLETED;
                $invoice->meta = json_encode($invoiceMeta);
                $invoice->save();
            }
        } catch (Exception $ex) {
            report($ex);
        }
    }

    public function chargeFailed($data)
    {
        try {
            if (!empty($data['payment_intent']) && $data['captured'] == false) {
                $order = Order::where(['stripe_txn_id' => $data['payment_intent'], 'status' => CommonConstants::INIT])->first();
                if ($order) {
                    $order->update(['status' => CommonConstants::ERROR]);
                    OrderConfirmed::dispatch($order, $data['failure_message']);
                    Log::channel('payment')->info('stripe event charge.failed: order updated.', ['payment_intent' => $data['payment_intent']]);
                }
            }
        } catch (Exception $ex) {
            Log::channel('payment')->error('stripe event charge.failed:', ['payment_intent' => $data['payment_intent']]);
            report($ex);
        }
    }

    public function chargeSucceeded($data)
    {
        try {
            if (!empty($data['payment_intent']) && $data['amount_refunded'] == 0 && $data['captured'] == true) {
                $order = Order::where(['stripe_txn_id' => $data['payment_intent'], 'status' => CommonConstants::INIT])->first();
                if ($order) {
                    $stripe = new \Stripe\StripeClient(config('cashier.secret'));
                    $balance_transaction = $data['balance_transaction'];
                    $transaction = $stripe->balanceTransactions->retrieve(
                        $balance_transaction,
                        []
                    );
                    $commissionPercentage = 0;
                    if (!empty(config('const.commission.template_percentage'))) {
                        $commissionPercentage = config('const.commission.template_percentage');
                    }

                    if (!empty($order->product) && !empty($order->product->user)) {
                        $productOwner = $order->product->user;
                        $commission = $productOwner->user_metas()->where('name', 'commission_per')->first();
                        if ($commission) {
                            $commissionPercentage = $commission->value;
                        }
                    }
                    $amount = $data['amount'] / 100;
                    $finalAmount = $amount - ($amount * $commissionPercentage) / 100;
                    $dataArr = [
                        'pg_amount' => $transaction->fee / 100,
                        'status' => CommonConstants::COMPLETED,
                        'commission_iwp' => ($amount - $finalAmount - $transaction->fee / 100),
                        'final_amount' => $finalAmount,
                    ];
                    $order->update($dataArr);
                    $orderMeta = $order->order_meta;
                    if (empty($orderMeta['group_token'])) {
                        OrderConfirmed::dispatch($order);
                        if (!empty($orderMeta['type']) && $orderMeta['type'] == 'store') {
                            StoreProductVersion::find($orderMeta['version_id'])->increment('number_installations');
                        }
                    } else {
                        if (Order::whereJsonContains('order_meta', ['group_token' => $orderMeta['group_token']])->where('status', '!=', CommonConstants::COMPLETED)->count() == 0) {
                            GroupOrderConfirmed::dispatch($order);
                        } else {
                            if ($order->product->payment_type == CommonConstants::RECURRING) {
                                Order3DSubPayment::dispatch($order, '', true);
                            } else {
                                Order3DPayment::dispatch($order, '', true);
                            }
                        }
                    }
                    $orderMeta['transfer'] = CommonConstants::PENDING;
                    $payoutSettings = $order->product->user->userDefaultPayout();
                    if ($payoutSettings && $order->final_amount > 0) {
                        $orderMeta['account_id'] = $payoutSettings->account_details['account_id'];
                        if ($payoutSettings->account_details['capability'] == CommonConstants::ACTIVE) {
                            try {
                                // Create a Transfer to a connected account (later):
                                $stripe->transfers->create([
                                    'amount' => $order->final_amount * 100,
                                    'currency' => 'usd',
                                    'destination' => $payoutSettings->account_details['account_id'],
                                    'transfer_group' => $order->token,
                                ]);
                                $orderMeta['transfer'] = CommonConstants::COMPLETED;
                            } catch (Exception $ex) {
                                $orderMeta['transfer'] = CommonConstants::PENDING;
                            }
                        }
                    }
                    $order->order_meta = json_encode($orderMeta);
                    $order->save();
                    $model = new $order->product->resource_type;
                    $resource = $model->findOrFail($order->product->resource_id);
                    Log::channel('payment')->info('stripe event charge.succeeded: order updated.', ['payment_intent' => $data['payment_intent']]);

                    try {

                        $mailData = [];
                        $mailData['name'] = $orderMeta['user_name'];
                        $mailData['url'] = route('site.launch', ['t' => $order->product->resource->slug, 'token' => $order->token, 'd' => 'v2']);
                        $mailData['seller'] = $productOwner->name;
                        $mailData['product'] = $resource->name;
                        $mailData['token'] = $order->token;
                        $mailData['amount'] = '$'.$order->amount;
                        $mailData['stripe_txn_id'] = $order->stripe_txn_id;
                        $mailData['subject'] = 'Your purchase receipt for '.$resource->name;
                        $mailData['user_id'] = $order->buyer_id;
                        if ($order->buyer()->exists()) {
                            $buyer_total_orders = Order::where('buyer_id', $order->buyer_id)->count();
                            if ($buyer_total_orders > 1) {
                                Mail::to($orderMeta['user_email'])->queue((new OrderCompleted($mailData))->onQueue('emails'));
                            }
                        }

                        $mailData['subject'] = 'New '.$resource->name.' sale! ';
                        $mailData['url'] = route('template.sales', ['template' => $resource]);
                        $mailData['id'] = $order->seller_id;
                        Mail::to($productOwner->email)->queue((new OrderCreatedSeller($mailData))->onQueue('emails'));
                    } catch (Exception $e) {
                        Log::channel('payment')->info('-- Order Error In mail --', ['mail_error' => $e->getMessage()]);
                    }
                }
            }
        } catch (Exception $ex) {
            Log::channel('payment')->error('stripe event charge.succeeded:', ['payment_intent' => $data['payment_intent']]);
            report($ex);
        }
    }

    public function invoicePaid($data)
    {
        $order = Order::where('stripe_txn_id', $data['subscription'])->first();

        if ($order) {
            return $this->handleWizardPayment($data, $order);
        } else {
            $instaWpLiveSubscription = HostingConnection::where('live_subscription_meta->id', $data['subscription'])->first();
            if ($instaWpLiveSubscription) {
                return true;
            }
        }
        $customer_id = $data['customer'];
        $user = User::where('stripe_id', $customer_id)->first();
        // if ($user->active_subscription->hasIncompletePayment()) {
        $billing_reason = $data['billing_reason'];
        $paid = $data['paid'];
        $status = $data['status'];
        $coupon_code = null;
        if (!empty($data['discount']) && !empty($data['discount']['coupon'])) {
            $coupon_code = $data['discount']['coupon']['id'];
        }
        Log::channel('payment')->info('invoice.paid', [$paid && (!empty($status) && $status == 'paid')]);

        if ($user?->active_subscription && $paid && (!empty($status) && $status == 'paid')) {
            try {
                $user->active_subscription->syncStripeStatus();
                $metadata = $data['subscription_details']['metadata'] ?? [];
                if ($billing_reason == 'subscription_update') {
                    foreach ($data['lines']['data'] as $s_item) {
                        $metadata = $metadata + $s_item['metadata'];
                        if (count($s_item['metadata']) > 1 && !empty($s_item['metadata']['is_custom'])) {
                            $user->plans()->where([
                                'stripe_addon_plan_id' => null,
                                'stripe_product_id' => null,
                            ])->delete();
                            $activePlan = $user->activePlan();
                            $user->active_subscription
                                ->update([
                                    'plan_price' => $activePlan->price,
                                    'amount_charged' => $activePlan->price,
                                ]);
                            Log::channel('payment')->info('stripe event invoice.paid: Addon activated.', ['customer' => $data['customer']]);
                            break;
                        }
                    }
                    // Remove unnecessary metadata fields before updating subscription metadata
                    // Remove fields: is_custom, activePlan
                    $metadata = Arr::except($metadata, ['is_custom', 'old_price_details', 'old_subscription_item_id']);
                    $stripe = new StripeClient(config('cashier.secret'));
                    $stripe->subscriptions->update(
                        $user->active_subscription->stripe_id,
                        ['metadata' => $metadata]
                    );
                }

                if (in_array($billing_reason, ['subscription_create', 'subscription_update'])) {
                    $plan = $user->activePlan();
                    $stripeService = new StripeService;
                    $stripeService->onPaymentComplete($user, $plan, $coupon_code);
                    Log::channel('payment')->info('stripe event invoice.paid: Subscription updated.', ['customer' => $data['customer']]);
                }
                $subscription = $user->active_subscription;
                if ($subscription && $subscription->stripe_status !== 'past_due' && $subscription->stripe_status !== 'incomplete') {
                    $user->payment_due_warn_count = 0;
                    $user->save();
                    if (isset($metadata['conversion_id'])) {
                        // Log::channel('payment')->info('conversion_user Subscription Meta Data', $metadata);
                        ConversionUser::where('id', $metadata['conversion_id'])->update(['is_converted' => true]);
                        unset($metadata['conversion_id']);
                        $metadata = count($metadata) > 0 ? $metadata : null;
                        $stripe = new StripeClient(config('cashier.secret'));
                        $stripe->subscriptions->update(
                            $subscription->stripe_id,
                            ['metadata' => $metadata]
                        );
                    }
                }
            } catch (Exception $ex) {
                Log::channel('payment')->error('stripe event invoice.paid:', ['customer' => $data['customer']]);
                report($ex);
            }
        }
        // }
    }

    public function handleWizardPayment($data, $order)
    {
        // if ($user->active_subscription->hasIncompletePayment()) {
        $paid = $data['paid'];
        $status = $data['status'];
        if ($paid && (!empty($status) && $status == 'paid')) {
            try {
                $order->status = CommonConstants::COMPLETED;
                $stripe = new StripeClient(config('cashier.secret'));
                $subscription = $stripe->subscriptions->retrieve($order->stripe_txn_id, []);
                $order->stripe_status = $subscription->status;
                $order->trial_ends_at = $subscription->trial_end;
                $order->ends_at = $subscription->ended_at;
            } catch (Exception $ex) {
                Log::channel('payment')->error('stripe event invoice.paid wizard:', ['customer' => $data['customer']]);
                report($ex);
            }
            $order->save();
            if (isset($order->order_meta['group_token']) && Order::whereJsonContains('order_meta', ['group_token' => $order->order_meta['group_token']])->where('status', '!=', CommonConstants::COMPLETED)->count() == 0) {
                GroupOrderConfirmed::dispatch($order);
            } else {
                if ($order->product->payment_type == CommonConstants::RECURRING) {
                    Order3DSubPayment::dispatch($order, '', true);
                } else {
                    Order3DPayment::dispatch($order, '', true);
                }
            }
        }
        // }
    }
}
