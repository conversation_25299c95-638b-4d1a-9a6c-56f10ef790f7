<template>
  <Head>
    <title>Users - {{ setting.brandShortName }}</title>
  </Head>
  <ConnectLayout
    :connect="$props.connect"
    :title="$t('users')"
    :sub-title="$t('list_of_users_in_your_connected_site')"
  >
    <template #right>
      <div class="flex md:flex-wrap items-center gap-3">
        <CommonListbox
          v-model:value="selectedSortOption"
          :options="sortOptions"
          :label-key="'name'"
          :value-key="'id'"
          button-class="!w-52"
          options-class="!w-52"
        />
        <CommonButton
          id="manage_add_user"
          :btn-title="$t('add_user')"
          icon-name="OutlinePlusIcon"
          btn-type="gray-outline-btn"
          @click="openModal"
        />
      </div>
    </template>
    <input
      ref="shareLinkRef"
      readonly
      type="text"
      class="absolute top-0 h-1 cursor-pointer p-0 opacity-0"
      :value="copyLink"
    >
    <EmptyState
      v-if="!isLoading && (users == null || users.length == 0)"
      :title="$t('no_users')"
      :sub-title="$t('no_users_found')"
      class="py-24 card-main"
    >
      <template #mainIcon>
        <svg
          class="mx-auto size-12 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
          />
        </svg>
      </template>
    </EmptyState>
    <CommonTable
      v-else
      :fields="tableHeaderData"
      :items="{ data: users }"
      :busy="isLoading"
      row-bordered
      bordered
      :responsive="true"
      border-rounded
    >
      <template #cell_user="{ item }">
        <div class="flex items-center gap-3">
          <div class="relative">
            <img
              v-lazy="item?.gravatar_image"
              class="size-10 rounded-full"
              alt=""
            >
            <div
              v-if="item.username === defaultLoginUser"
              class="absolute -right-0.5 -top-0.5 size-3 rounded-full bg-green-500 ring-2 ring-white"
            >
              <OutlineCheckCircleIcon
                v-tooltip="$t('default_magic_login_user')"
                class="size-3 text-white"
              />
            </div>
          </div>
          <div>
            <div
              v-tooltip="item?.display_name"
              class="name w-fit"
            >
              <h2
                class="max-w-[150px] truncate text-sm font-medium text-grayCust-980 sm2:max-w-[300px] lg:max-w-[180px]"
              >
                {{ item?.display_name }}
              </h2>
            </div>
            <div
              v-tooltip="item?.email"
              class="email"
            >
              <h2
                class="max-w-[150px] truncate text-xs font-medium text-grayCust-950 sm2:max-w-[300px] lg:max-w-[180px]"
              >
                {{ item?.email }}
              </h2>
            </div>
          </div>
        </div>
      </template>
      <template #cell_created_at="{ item }">
        <div class="flex items-center justify-start gap-2">
          <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
            {{ $t('created_at') }}:
          </p>
          <div v-tooltip="formatDate(item?.created_at)">
            <h2
              class="max-w-[120px] truncate text-sm font-medium text-grayCust-980 sm2:max-w-[250px] lg:max-w-[150px]"
            >
              {{ formatDate(item?.created_at) }}
            </h2>
          </div>
        </div>
      </template>
      <template #cell_role="{ item }">
        <div class="flex items-center justify-start gap-2">
          <p class="block text-sm font-medium uppercase text-grayCust-800 lg:hidden">
            {{ $t('role') }}:
          </p>
          <div
            v-for="(role, key) in item?.roles"
            :key="key"
          >
            <CommonTag
              :text="getUserRoleName(role)"
              type="gray"
            />
          </div>
        </div>
      </template>
      <template #cell_action="{ item }">
        <CommonActionButtonGroup>
          <IconButton
            :id="'users_magic_login_' + item.id"
            v-tooltip="$t('magic_login')"
            icon-name="OutlineDownloadIcon"
            btn-type="gray-outline-btn"
            icon-size="rotate-[270deg] size-4"
            @click="autoLoginConnect(item?.username)"
          />
          <CommonPopover button-class="!p-0 rounded-lg overflow-hidden">
            <template #popoverButton>
              <IconButton
                v-tooltip="$t('more_act')"
                icon-name="OutlineDotsVerticalIcon"
                btn-type="gray-outline-btn"
                extra-class="!rounded-lg"
              />
            </template>
            <template #popoverOptions>
              <CommonMenuItem
                :label="$t('edit_in_admin_panel')"
                icon="OutlineAdjustmentsIcon"
                @click="openUserEditPage(item.id)"
              />
              <CommonMenuItem
                :label="$t('get_temp_login_url')"
                :icon="link_copied ? 'OutlineCheckIcon' : 'OutlineDuplicateIcon'"
                @click="getTemporaryLogin(item.id)"
              />
              <CommonMenuItem
                v-if="item.username !== defaultLoginUser && item.roles?.includes('administrator')"
                :label="$t('set_default_magic_login_user')"
                icon="OutlineUserIcon"
                @click="setDefaultLoginUser(item.id)"
              />
            </template>
          </CommonPopover>
        </CommonActionButtonGroup>
      </template>
    </CommonTable>
    <CommonPagination
      v-if="!isLoading && users.length > 0 && pagination?.lastPage > 1"
      v-model:current-page="pagination.currentPage"
      v-model:per-page="pagination.perPage"
      :total="pagination?.total"
      :resource-name="users.length > 1 ? $t('users') : $t('user')"
      @page-change="fetchUsers"
      @per-page-change="handlePerPageChange"
    />
    <UserModal
      :is-open="isUserModal"
      :connect="$props.connect"
      :option-list="optionList"
      @user-added="onUserAdded"
      @close-modal="closeModal"
    />
  </ConnectLayout>
</template>

<script>
import CommonMenuItem from '@/app/Common/ActionDropdown/CommonMenuItem.vue';
import CommonActionButtonGroup from "@/app/Common/CommonActionButtonGroup.vue";
import CommonButton from "@/app/Common/CommonButton.vue";
import IconButton from '@/app/Common/CommonIconButton.vue';
import CommonListbox from "@/app/Common/CommonListbox.vue";
import CommonPagination from "@/app/Common/CommonPagination.vue";
import CommonPopover from '@/app/Common/CommonPopover.vue';
import CommonTable from "@/app/Common/CommonTable.vue";
import CommonTag from "@/app/Common/CommonTag.vue";
import ConnectLayout from "@/app/Pages/Connects/ConnectLayout.vue";
import EmptyState from '@/app/Pages/Connects/Components/EmptyState.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { trans, wTrans } from 'laravel-vue-i18n';
import moment from 'moment';
import { mapStores } from 'pinia';
import { defineAsyncComponent } from 'vue';

const UserModal = defineAsyncComponent(() => import('@/app/Pages/Connects/UserModal.vue'));

export default {
  name: "ConnectUsers",
  components: {
    ConnectLayout,
    EmptyState,
    UserModal,
    CommonTable,
    CommonTag,
    CommonActionButtonGroup,
    CommonButton,
    CommonPopover,
    CommonMenuItem,
    CommonListbox,
    CommonPagination,
    IconButton
  },
  props: {
    connect: {
      type: Object,
      required: true
    },
    usersPagination: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      copyLink: '',
      isUserModal: false,
      link_copied: false,
      isLoading: true,
      selectedBaseOption: {},
      defaultLoginUser: this.$props.connect.username ?? null,
      setting: new Setting(),
      activeTabName: "UsersTab",
      optionList: [
        { id: 1, name: 'Administrator', value: 'administrator' },
        { id: 2, name: 'Editor', value: 'editor' },
        { id: 3, name: 'Author', value: 'author' },
        { id: 4, name: 'Contributor', value: 'contributor' },
        { id: 5, name: 'Subscriber', value: 'subscriber' },
      ],
      tableHeaderData: [
        {
          key: 'user',
          label: wTrans('user'),
          headerClass: 'w-1/2'
        },
        {
          key: 'created_at',
          label: wTrans('created_at')
        },
        {
          key: 'role',
          label: wTrans('role')
        },
        {
          key: 'action',
          label: wTrans('action')
        }
      ],
      baseList: [
        {
          id: 1,
          name: '5',
        },
        {
          id: 2,
          name: '10',
        },
        {
          id: 3,
          name: '15',
        },
      ],
      users: [],
      pagination: {
        perPage: '10', // String to match with dropdown values
        currentPage: 1,
        lastPage: 0,
        total: 0
      },
      selectedSortOption: {
        id: 'registered',
        name: wTrans('sort_by_created_date')
      },
      sortOptions: [
        {
          id: 'registered',
          name: wTrans('sort_by_created_date')
        },
        {
          id: 'name',
          name: wTrans('sort_by_name')
        }
      ],
    };
  },
  computed: {
    ...mapStores(useAppStore)
  },
  watch: {
    selectedSortOption: {
      handler() {
        this.fetchUsers();
      },
    },
  },
  created() {
    if (this.$props.usersPagination) {
      this.pagination = {
        ...this.$props.usersPagination,
        perPage: this.$props.usersPagination.perPage ? this.$props.usersPagination.perPage.toString() : '10'
      };
    }
    this.fetchUsers();
  },
  methods: {
    formatDate(date) {
      return moment(date).format('MMMM D, YYYY HH:mm:ss');
    },
    autoLoginConnect(username) {
      axios
        .post("/api/v1/connects-autologin", { id: this.$props.connect.id, username: username })
        .then((response) => {
          if (response.data.status) {
            window.open(response.data.data.login_url);
          } else {
            const message = {
              heading: trans('failed'),
              subHeading: response.data.data.message,
              type: "error",
            };
            this.appStore.setNotification(message);
          }
        })
    },
    getTemporaryLogin(user_id) {
      axios
        .post("/api/v2/connects/" + this.$props.connect.id + "/temporary-login", { user_id: user_id })
        .then((response) => {
          this.link_copied = true
          this.copyLink = response.data.data.login_url
          const ele = this.$refs.shareLinkRef;
          if (!ele) {
            return false
          }
          setTimeout(() => {
            let copy = false
            ele.select();
            ele.setSelectionRange(0, 99999);
            copy = document.execCommand('copy');
            if (copy) {
              const message = {
                heading: trans('success'),
                subHeading: trans('temp_link_copy_in_clipboard'),
                type: "success",
              };
              this.appStore.setNotification(message);
              this.link_copied = false
            }
          }, 200);
        })
    },
    openModal() {
      axios
        .get("/api/v2/connects/" + this.$props.connect.id + "/get-user-roles")
        .then((response) => {
          if (response.data.data.success) {
            this.optionList = response.data.data.roles;
            this.isUserModal = true;
          }
        })
        .catch(() => {
          this.isUserModal = true;
        })
    },
    closeModal() {
      this.isUserModal = false;
    },
    fetchUsers(page = 1) {
      if (page && Number.isInteger(page)) {
        this.pagination.currentPage = page
      }
      const params = {
        page: this.pagination?.currentPage,
        per_page: this.pagination?.perPage,
        order_by: this.selectedSortOption?.id,
        order: this.selectedSortOption?.id === 'name' ? 'ASC' : 'DESC',
        timestamp: new Date().getTime()
      };
      axios
        .get("/api/v2/connects/" + this.$props.connect.id + "/get-users", { params })
        .then((response) => {
          this.users = response.data.data;
          if (response.data.status) {
            this.setPagination(response.data.meta)
          } else {
            const message = {
              heading: trans('failed'),
              subHeading: response.data.message,
              type: "error",
            };
            this.appStore.setNotification(message);
          }
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        })
    },
    onUserAdded(userData) {
      this.pagination.total = userData.data.count ?? this.pagination.total + 1;
      this.closeModal();
      this.isLoading = true;
      this.fetchUsers();
    },
    handlePerPageChange(perPage) {
      this.pagination.perPage = perPage;
      this.pagination.currentPage = 1;
      this.isLoading = true;
      this.fetchUsers();
    },
    setPagination(pagination) {
      this.pagination = {
        ...pagination,
        perPage: pagination.perPage ? pagination.perPage.toString() : '10'
      };
    },
    setDefaultLoginUser(userId) {
      axios
        .post("/api/v2/connects/" + this.$props.connect.id + "/set-default-login-user", { user_id: userId })
        .then((response) => {
          if (response.data.status) {
            this.defaultLoginUser = response.data.data.username;
          }
        })
    },
    getUserRoleName(role) {
      return this.optionList.find(option => option.value === role)?.name ?? role;
    },
    openUserEditPage(userId) {
      window.open(this.$props.connect.url + '/wp-admin/user-edit.php?user_id=' + userId);
    }
  },
};
</script>