<?php

namespace App\Interfaces;

interface CommonConstants
{
    public const READY = 'ready';

    public const ACTIVE = 'active';

    public const INACTIVE = 'inactive';

    public const INPROGRESS = 'in progress';

    public const PENDING = 'pending';

    public const ERROR = 'error';

    public const FREE = 'free';

    public const COMPLETED = 'completed';

    public const INIT = 'init';

    public const REFUNDED = 'refunded';

    public const GENERATED = 'generated';

    public const IN_PROGRESS = 'in_progress';

    public const REQUESTED = 'requested';

    public const PUBLISHED = 'published';

    public const REJECTED = 'rejected';

    public const DRAFT = 'draft';

    public const ONE_TIME = 'one_time';

    public const RECURRING = 'recurring';

    public const MONTHLY = 'monthly';

    public const YEARLY = 'yearly';

    public const DEFAULT_SITE_PAGINATION = 5;

    public const DEFAULT_TEMPLATE_PAGINATION = 10;

    public const DEFAULT_SNAPSHOT_PAGINATION = 10;

    public const DEFAULT_SHARED_SITE_PAGINATION = 10;

    public const DEFAULT_SITE_VERSION_PAGINATION = 10;

    public const DEFAULT_REF_HISTORY_PAGINATION = 5;

    public const DEFAULT_CREDIT_HISTORY_PAGINATION = 5;

    public const DEFAULT_STACK_PROD_PAGINATION = 9;

    public const NOTIFICATION_ID = [
        'site_expired' => 1,
        'site_created' => 2,
        'site_deleted' => 3,
    ];

    public const CONNECT_NOTIFICATION = [
        'NEW_CONNECTED_SITE' => 'new-connected-site',
        'CONNECT_UPTIME_NOTIFICATION' => 'connect-uptime-notification',
        'MAGIC_LOGIN' => 'hosting-login',
    ];

    public const HOSTING_NOTIFICATION = [
        'NEW_HOSTED_SITE' => 'new-hosted-site',
    ];

    public const CLOUD_TASKS = [
        'site_install' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_install',
            'success_callback' => 'successICSiteInstall',
            'error_callback' => 'failedICSiteInstall',
            'timeout_min' => 30,
        ],
        'site_delete' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_delete',
            'success_callback' => 'successICSiteDelete',
            'error_callback' => 'failedICSiteDelete',
            'timeout_min' => 20,
        ],
        'site_export_localwp' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_export_localwp',
            'success_callback' => 'successICSiteExportLocalwp',
            'error_callback' => 'failedICSiteExportLocalwp',
            'timeout_min' => 120,
        ],
        'site_export_instawp' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_export_instawp',
            'success_callback' => 'successICSiteExportInstaWP',
            'error_callback' => 'failedICSiteExportInstaWP',
            'timeout_min' => 120,
        ],
        'site_export_studio' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_export_studio',
            'success_callback' => 'successICSiteExportStudio',
            'error_callback' => 'failedICSiteExportStudio',
            'timeout_min' => 120,
        ],
        'site_unsuspend' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_unsuspend',
            'success_callback' => 'successICSiteUnsuspend',
            'error_callback' => 'failedICSiteUnsuspend',
            'timeout_min' => 2,
        ],
        'site_backup_create' => [
            'resource_type' => 'App\Models\Template',
            'task_type' => 'site_backup_create',
            'success_callback' => 'successSiteBackupCreate',
            'error_callback' => 'failedSiteBackupCreate',
            'timeout_min' => 120,
        ],
        'site_clone_backup_create' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_clone_backup_create',
            'success_callback' => 'successSiteCloneBackupCreate',
            'error_callback' => 'failedSiteCloneBackupCreate',
            'timeout_min' => 120,
        ],
        'site_map_domain' => [
            'resource_type' => 'App\Models\WebAppDomain',
            'task_type' => 'site_map_domain',
            'success_callback' => 'successSiteMapDomain',
            'error_callback' => 'failedSiteMapDomain',
            'timeout_min' => 30,
        ],
        'site_deploy_git' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_deploy_git',
            'success_callback' => 'successSiteDeployGit',
            'error_callback' => 'failedSiteDeployGit',
            'timeout_min' => 60,
        ],
        'site_update_wp' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'site_update_wp',
            'success_callback' => 'successSiteUpdateWP',
            'error_callback' => 'failedSiteUpdateWP',
            'timeout_min' => 5,
        ],
        'verify_suffix_domain' => [
            'resource_type' => 'App\Models\ServerGroupDomain',
            'task_type' => 'verify_suffix_domain',
            'success_callback' => 'successVerifySuffixDomain',
            'error_callback' => 'failedVerifySuffixDomain',
            'timeout_min' => 15,
        ],
        'site_version_create' => [
            'resource_type' => 'App\Models\SiteVersion',
            'task_type' => 'site_version_create',
            'success_callback' => 'successSiteVersionCreate',
            'error_callback' => 'failedSiteVersionCreate',
            'timeout_min' => 120,
        ],
        'site_version_restore' => [
            'resource_type' => 'App\Models\SiteVersion',
            'task_type' => 'site_version_restore',
            'success_callback' => 'successSiteVersionRestore',
            'error_callback' => 'failedSiteVersionRestore',
            'timeout_min' => 120,
        ],
        'backup_convert' => [
            'resource_type' => 'App\Models\Template',
            'task_type' => 'backup_convert',
            'success_callback' => 'successTemplateBackupConvert',
            'error_callback' => 'failedTemplateBackupConvert',
            'timeout_min' => 120,
        ],
        'wc_generator' => [
            'resource_type' => 'App\Models\Site',
            'task_type' => 'wc_generator',
            'success_callback' => 'successWcGenerator',
            'error_callback' => 'failedWcGenerator',
            'timeout_min' => 5,
        ],
        'waas_ssh_deployer' => [
            'resource_type' => 'App\Models\HostingSite',
            'task_type' => 'waas_ssh_deployer',
            'success_callback' => 'successWaasSshDeployer',
            'error_callback' => 'failedWaasSshDeployer',
            'timeout_min' => 30,
        ],
        'migrate_v3_pull' => [
            'resource_type' => 'App\Models\MigrateV3',
            'task_type' => 'migrate_v3_pull',
            'success_callback' => 'successMigrateV3Pull',
            'error_callback' => 'failedMigrateV3Pull',
            'timeout_min' => 1440,
        ],
        'migrate_v3_push' => [
            'resource_type' => 'App\Models\MigrateV3',
            'task_type' => 'migrate_v3_push',
            'success_callback' => 'successMigrateV3Push',
            'error_callback' => 'failedMigrateV3Push',
            'timeout_min' => 1440,
        ],
        'ssh_cmd_run' => [
            'resource_type' => 'App\Models\HostingSite',
            'task_type' => 'ssh_cmd_run',
            'success_callback' => 'successSshCmdRun',
            'error_callback' => 'failedSshCmdRun',
            'timeout_min' => 5,
        ],
        'ssh_deployer_wpc' => [
            'resource_type' => 'App\Models\HostingSite',
            'task_type' => 'ssh_deployer_wpc',
            'success_callback' => 'successSshDeployerWpc',
            'error_callback' => 'failedSshDeployerWpc',
            'timeout_min' => 5,
        ],
    ];

    public const SITE_STATUS = [
        'normal_site' => 0,
        'abuse_suspended' => 1,
        'admin_unsuspended' => 2,
        'restore_progress' => 3,
        'installation_progress' => 4,
        'installation_failed' => 5,
        'disk_over_use_suspended' => 6,
    ];

    public const CONNECT_SYNC_STATUS = [
        'pending' => 'Pending',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'error' => 'Error',
    ];

    public const THROTTLEABLE_TOKEN_TYPES = ['api'];  // token type countable for api quota

    public const THROTTLEABLE_SYNC_TOKEN_TYPES = ['InstaWP Connect'];  // token type countable for api quota

    public const DISK_WARN_USER_CHUNK = 10;

    public const PLAN_INTERVAL = [
        'monthly' => 1,
        'yearly' => 2,
    ];

    public const HOSTING_PROVIDERS = [
        'RUNCLOUD' => 'runcloud',
        'CLOUDWAYS' => 'cloudways',
        'WP_ENGINE' => 'wp-engine',
        'WORDPRESS' => 'wordpress',
        'WP_BOLT' => 'wp-bolt',
        'GRID_PANE' => 'grid-pane',
        'WP_CLOUD_DEPLOY' => 'wp-cloud-deploy',
        'WP_CLOUD' => 'wp-cloud',
        'PRESSABLE' => 'pressable',
        'WPCS' => 'wpcs',
        'Server_Avatar' => 'serveravatar',
        'SPINUP_WP' => 'spinup-wp',
        'SERVER_BOLT' => 'server-bolt',
    ];

    public const GALLERY_RESOURCE_TYPE = [
        'template' => [
            'path' => 'gallery/template',
        ],
    ];

    public const HOSTING_TYPES = [
        'INSTAWP' => 'instawp',
        'MY_HOSTING' => 'my_hosting',
    ];

    public const SIGNUP_SOURCES = [
        'onboard' => 'onboard',
        'wca' => 'wca',
        'signup_page' => 'signup_page',
        'shared_site_clone' => 'shared_site_clone',
        'signup_page_social' => 'signup_page_social',
        'wca_social' => 'wca_social',
        'wp_new_email' => 'wp_new_email',
        'atarim' => 'atarim',
        'wceu' => 'wceu',
        'wceu_social' => 'wceu_social',
        'shared_site_purchase' => 'shared_site_purchase',
        'wcblr2023' => 'wcblr2023',
        'wcblr2023_social' => 'wcblr2023_social',
        'wcus2023' => 'wcus2023',
        'wcus2023_social' => 'wcus2023_social',
        'wcahm2023' => 'wcahm2023',
        'wcahm2023_social' => 'wcahm2023_social',
        'update_profile' => 'update_profile',
    ];

    public const PAYOUT_OPTIONS = [
        'Stripe' => 'stripe',
        'Paypal' => 'paypal',
    ];

    public const TEMPLATE = 'template';

    public const SITE = 'site';

    public const CONNECT = 'connect';

    public const CONNECTED_PROVIDER = 'connected_provider';

    public const INTEGRATION = 'integration';

    public const GALLERY_MEDIA_TYPE = [
        'IMAGE' => 'image',
        'VIDEO' => 'video',
    ];

    // deprecated constant
    public const ADD_USER_CREDIT = [
        'wca' => [
            'amount' => 25,
            'currency' => 'usd',
            'description' => 'WCAsia Giveaway by InataWp.',
            'credit_note' => 'wcasia_promo_credit',
        ],
        'signup_conversion' => [
            'amount' => 20,
            'currency' => 'usd',
            'description' => 'SignUp user conversion credit.',
            'credit_note' => 'signup_conversion',
        ],
        'plan_purchase' => [
            'amount' => 20,
            'currency' => 'usd',
            'description' => 'Plan purchase credit.',
            'credit_note' => 'plan_purchase',
        ],
        'wceu' => [
            'amount' => 25,
            'currency' => 'usd',
            'description' => 'WCEurope Giveaway by InataWp.',
            'credit_note' => 'wceu_promo_credit',
        ],
        'wcblr2023' => [
            'amount' => 25,
            'currency' => 'usd',
            'description' => 'WCBengaluru Giveaway by InataWp.',
            'credit_note' => 'wcblr2023_promo_credit',
        ],
        'wcus2023' => [
            'amount' => 25,
            'currency' => 'usd',
            'description' => 'WCUS Giveaway by InataWp.',
            'credit_note' => 'wcus2023_promo_credit',
        ],
        'wcahm2023' => [
            'amount' => 25,
            'currency' => 'usd',
            'description' => 'WCAhm Giveaway by InstaWP.',
            'credit_note' => 'wcahm2023_promo_credit',
        ],
    ];

    public const PARENT = 'parent';

    public const CHILD = 'child';

    // deprecated constant
    public const REFERRAL_PROGRAMS = [
        'signup' => [
            'name' => 'Signup Referral Program',
            'type' => 'signup',
            'referral_code_length' => 10,
            'amount_to_self' => 20,
            'amount_to_user' => 20,
        ],
    ];

    public const MAX_TEMPLATE_FAIL_SITE_COUNT = 3;

    public const WIZARD_RESOURCE = [
        'wizard_logo' => [
            'path' => 'wizards/logo',
        ],
        'wizard_gallery' => [
            'path' => 'wizards/gallery',
        ],
    ];

    public const MIGRATE_STATUS = [
        'DRAFT' => 'draft',
        'INIT' => 'init',
        'SITE_CREATED' => 'site_created',
        'BACKUP' => 'backup',
        'UPLOADING' => 'uploading',
        'RESTORING' => 'restoring',
        'COMPLETED' => 'completed',
    ];

    public const MIGRATE_V3_STATUS = [
        'INITIATED' => 'initiated',
        'IN_PROGRESS' => 'in_progress',
        'COMPLETED' => 'completed',
        'FAILED' => 'failed',
        'TIMEOUT' => 'timeout',
        'ABORTED' => 'aborted',
    ];

    public const MIGRATE_SOURCE_TYPE = [
        'LIVE' => 'live',
        'INSTAWP' => 'instawp',
    ];

    public const SG_STORAGE_DIR = 'sg-icons';

    public const STORE_PRODUCT_STATUS = [
        'APPLIED' => 'applied',
        'PENDING' => 'pending',
        'APPROVED' => 'approved',
        'REJECTED' => 'rejected',
    ];

    public const SITE_SOURCES = [
        'guest' => 'guest',
        'scratch' => 'scratch',
        'template' => 'template',
        'shared_template' => 'shared_template',
        'clone' => 'clone',
        'iwp_connect' => 'iwp_connect',
        'waas' => 'waas',
    ];

    public const SERVER_ERROR = 'server_error';

    public const DATA_NOT_AVAILABLE = 'data_not_available';

    public const STACK_CAT_IMG_STORAGE_DIR = 'stack-category-icons';

    public const STACK_PROD_IMG_STORAGE_DIR = 'stack-product-icons';

    public const MIGRATE_WHITE_LABEL_IMG_STORAGE_DIR = 'white-label-migrate';

    public const INBOX = 'inbox';

    public const CONTACT_LIST = 'contact_list';

    public const WORDCAMP_DETAILS = [
        'wca' => [
            'logo' => '/images/MobileVerification/word-camp-logo.png',
            'name' => 'WordCamp Asia',
            'source' => 'wca',
        ],
        'wceu' => [
            'logo' => '/images/wceu-logo.png',
            'name' => 'WordCamp Europe',
            'source' => 'wceu',
        ],
        'wcblr2023' => [
            'logo' => '/images/wcblr2023-logo.png',
            'name' => 'WordCamp Bengaluru 2023',
            'source' => 'wcblr2023',
        ],
        'wcus2023' => [
            'logo' => '/images/wcus2023-logo.svg',
            'name' => 'WordCamp US 2023',
            'source' => 'wcus2023',
        ],
        'wcahm2023' => [
            'logo' => '/images/wcahm2023-logo.png',
            'name' => 'WordCamp Ahmedabad 2023',
            'source' => 'wcahm2023',
        ],
    ];

    public const TEMPLATE_HOSTING_IMG_STORAGE_DIR = 'template-hosting-logos';

    public const SNAPSHOT_HOSTING_IMG_STORAGE_DIR = 'snapshot-hosting-logos';

    public const SELF_DESCRIBE_TYPES = [
        'bo' => [
            'name' => 'Business Owner',
            'icon' => 'OnboardingSite/business-owner-img.svg',
        ],
        'ag' => [
            'name' => 'Agency',
            'icon' => 'OnboardingSite/agency-img.svg',
        ],
        'hc' => [
            'name' => 'Hosting Company',
            'icon' => 'OnboardingSite/hosting-provider-img.svg',
        ],
        'ptc' => [
            'name' => 'Product Company',
            'icon' => 'OnboardingSite/product-company-img.svg',
        ],
        'frl' => [
            'name' => 'Freelancer',
            'icon' => 'OnboardingSite/freelancer-img.svg',
        ],
    ];

    public const ADS_PROGRAM = 'ads_program';

    public const TRAIL_PROGRAM = 'trial_program';

    public const BRAND_TEMPLATE_SELLER = 'brand_template_seller';

    public const SECURITY_JSON_STORAGE_DIR = 'security-scan-data';

    public const DEFAULT_SECURITY_REPORT_PAGINATION = 10;

    public const SITE_SECURITY_SEVERITY = [
        'closed' => [
            'name' => 'Closed',
            'color' => '#000000',
        ],
        'critical' => [
            'name' => 'Critical',
            'color' => '#b42318',
        ],
        'high' => [
            'name' => 'High',
            'color' => '#b49018',
        ],
        'medium' => [
            'name' => 'Medium',
            'color' => '#4e5ba6',
        ],
        'low' => [
            'name' => 'Low',
            'color' => '#439fbc',
        ],
    ];

    public const SITE_SECURITY_SEVERITY_GRAPH = [
        'closed' => [
            'name' => 'Closed',
            'color' => '#E4E4E7',
        ],
        'critical' => [
            'name' => 'Critical',
            'color' => '#FB7185',
        ],
        'high' => [
            'name' => 'High',
            'color' => '#FACC15',
        ],
        'medium' => [
            'name' => 'Medium',
            'color' => '#FEF08A',
        ],
        'low' => [
            'name' => 'Low',
            'color' => '#84CAFF',
        ],
    ];

    public const SITE_SECURITY_REPORT_STATUS = [
        '0' => [
            'name' => 'Healthy',
            'color' => '#005e54',
        ],
        '1' => [
            'name' => 'Warning',
            'color' => '#92400e',
        ],
        '2' => [
            'name' => 'Critical',
            'color' => '#991b1b',
        ],
    ];

    public const SITE_LANGUAGES = [
        'en' => [
            'name' => 'English',
            'icon' => 'images/lang_icons/lang_en_icon.svg',
        ],
        'es' => [
            'name' => 'Spanish',
            'icon' => 'images/lang_icons/lang_es_icon.svg',
        ],
        'de' => [
            'name' => 'German',
            'icon' => 'images/lang_icons/lang_de_icon.svg',
        ],
        'fr' => [
            'name' => 'French',
            'icon' => 'images/lang_icons/lang_fr_icon.svg',
        ],
        'pt-BR' => [
            'name' => 'Brazilian Portuguese',
            'icon' => 'images/lang_icons/lang_pt_BR_icon.svg',
        ],
        'ja' => [
            'name' => 'Japanese',
            'icon' => 'images/lang_icons/lang_ja_icon.svg',
        ],
        'it' => [
            'name' => 'Italian',
            'icon' => 'images/lang_icons/lang_it_icon.svg',
        ],
        'sv' => [
            'name' => 'Swedish',
            'icon' => 'images/lang_icons/lang_sv_icon.svg',
        ],
        'cs' => [
            'name' => 'Czech',
            'icon' => 'images/lang_icons/lang_cs_icon.svg',
        ],
        'sk' => [
            'name' => 'Slovak',
            'icon' => 'images/lang_icons/lang_sk_icon.svg',
        ],
        'fi' => [
            'name' => 'Finnish',
            'icon' => 'images/lang_icons/lang_fi_icon.svg',
        ],
        'nl' => [
            'name' => 'Dutch',
            'icon' => 'images/lang_icons/lang_nl_icon.svg',
        ],
        'da' => [
            'name' => 'Danish',
            'icon' => 'images/lang_icons/lang_da_icon.svg',
        ],
    ];

    public const THROUGH_PARTNER = 'through-partner';

    public const MIGRATE_LOG_TITLES = [
        'CREATE_MIGRATE' => 'Create Migrate',
        'MIGRATE_FAILED' => 'Migrate Failed',
        'CREATE_MIGRATE_PART' => 'Create Migrate Part',
        'UPDATE_MIGRATE_PART' => 'Update Migrate Part',
        'UPDATE_MIGRATE_TOTAL_PART' => 'Update Migrate Total Part',
        'INSTALL_PLUGIN_STATUS' => 'Install Plugin Status',
        'MIGRATE_HOSTING' => 'Migrate Hosting',
        'CHECK_MIGRATE' => 'Check Migrate',
        'CONFIG_PLUGIN' => 'Config Plugin',
        'PING_HOSTING_MIGRATE' => 'Ping Hosting Migrate',
        'MIGRATE_BACKUP' => 'Migrate Backup',
        'MIGRATE_HOSTING_PROVIDER_SITE_PROGRESS' => 'Migrate Hosting Provider Site Progress',
        'MIGRATE_ERROR' => 'Migrate Error',
        'MIGRATE_TOTAL_PARTS' => 'Migrate Total Parts',
        'FORCE_TIMEOUT' => 'Force Timeout',
        'MIGRATE_BACKUP_PROGRESS' => 'Migrate Backup Progress',
        'CONNECT_MIGRATE_SITE' => 'Connect Migrate Site',
        'CREATE_MIGRATE_DRAFT' => 'Create Migrate Draft',
        'UPDATE_MIGRATE_DRAFT' => 'Update Migrate Draft',
        'MIGRATE_WEBHOOK_DATA' => 'Migrate Webhook Data',
        'MIGRATE_RESTORE' => 'Migrate Restore',
        'MIGRATE_COMPLETED' => 'Migrate Completed',
        'SOURCE' => 'Source Log',
        'DESTINATION' => 'Destination Logs',
        'CATCH_ERROR' => 'Catch Error',
        'MIGRATE_WITH_KEY' => 'Migrate With Key',
        'MIGRATE_STATUS_UPDATE' => 'Migrate Status Update',
        'CALL_PLUGIN_API_START_MIGRATE' => 'Call plugin api start migrate',
        'CALL_CLOUD_API_START_MIGRATE' => 'Call cloud api start migrate',
    ];

    public const STRIPE_SUBSCRIPTION_TYPE = [
        'WAAS' => 'waas',
        'GLOBAL' => 'global',
    ];

    public const MIGRATE_INSTA_SITE_STATUS = [
        'STARTED' => 'started',
        'FAILED' => 'failed',
        'COMPLETED' => 'completed',
    ];

    public const CONNECT_CLI_COMMAND = [
        'CONFIG_KEY' => 'instawp config-set api-key ',
        'CONFIG_API_DOMAIN' => 'instawp config-set api-domain ',
        'CONFIG_RESET' => 'instawp hard-reset',
        'CONFIG_RESET_WAAS_MODE' => 'instawp reset-waas-mode',
        'CONFIG_USER_ROLE' => 'user set-role ',
        'SET_WAAS_MODE' => 'wp instawp set-waas-mode ',
        'PLUGIN_LIST' => 'wp plugin list --fields=name,status --format=csv',
        'INSTALL_PLUGIN' => 'wp plugin install ',
        'MARK_STAGING' => 'instawp staging-set ',
        'DEACTIVATE_PLUGIN' => 'plugin deactivate ',
        'CREATE_USER' => 'wp user create ',
        'STAGING_SET' => 'instawp staging-set ',
        'MULTI_SITE' => 'core multisite-convert',
        'COOKIE_DOMAIN' => 'config set COOKIE_DOMAIN \'$_SERVER["HTTP_HOST"] ?? DOMAIN_NAME\' --raw',
        'WP_TABLE_PREFIX' => 'wp db prefix --skip-themes --skip-plugins 2>/dev/null',
        'WP_UPDATE_SITE_TITLE' => 'option update blogname ',
        'WP_VERSION' => 'wp core version',
    ];

    public const WPCS_IP_ADDRESS = [
        'eu1' => ['************', '***********', '*************'],
        'us1' => ['*************', '***********', '**************'],
    ];

    public const WP_CLOUD_SERVER_LOCATIONS = [
        'bur' => 'Burbank, USA',
        'dca' => 'Washington, USA',
        'dfw' => 'Dallas, USA',
        'ams' => 'Amsterdam, NL',
    ];

    public const WP_CLOUD_PHP_VERSIONS = ['8.0', '8.1', '8.2', '8.3'];

    public const WAAS_DEFAULT_EMAIL = [
        'custom_email_subject' => 'Your purchase receipt for {{template_name}}',
        'custom_email_body' => '<h1>Hey {{creator_name}},</h1><p>Congratulations on your purchase of {{template_name}} from  {{seller_name}}! We\'re excited to share the details of your transaction with you:</p>{{credentials}}<p>Woohoo! You\'re officially the proud owner of {{template_name}}! To keep tabs on your order, you can easily track it using the link below:</p>{{track_url}}<p>Thanks for choosing our platform for your purchase. Happy building!</p>',
    ];

    public const WAAS_INTEGRATE_EMAIL = [
        'integrate_subject' => 'Your {{waas_name}} checkout link',
        'integrate_body' => '<p>Hello {{name}},</p><p><br></p><p>Thank you for the purchase. Your checkout link for {{waas_name}} has been generated, click on the following link to setup a new website for yourself:</p><p><br></p><p>{{waas_unique_link}}</p><p><br></p><p>powered by InstaWP.com</p>',
    ];

    public const CONNECT_APIS = [
        'api/v2/connects/{connect}',
        'api/v1/connects/{connect}/usage',
        'api/v2/connects/{connect}/staging-sites',
        'api/v2/connects/{connect}/get-sync-quota',
        'api/v2/connects/{connect}/connected-sites',
        'api/v2/connects/{connect}/syncs',
        'api/v2/connects/{connect}/syncs/{connectSync}',
        'api/v1/connects/{connect}/heartbeat',
        'api/v2/connects/{connect}/disconnect',
        'api/v2/connects/{connect}/activity-log',
        'api/v2/connects/{connect}/generate-token',
        'api/v2/connects/{connect}/restore',
        'api/v2/connects/{connect}/logs',
        'api/v2/connects/{connect}/delete',
        'api/v2/connects/{connect}/subscribe',
        'api/v2/connects/{connect}/managed',
        'api/v2/connects/{connect}/sites/create-staging',
        'api/v2/connects/{connect}/sites/{site}/restore-raw',
        'api/v2/connects/{connect}/sites/{site}/update-sftp-status',
        'api/v2/connects/{connect}/sites/{site}/sftp-details',
        'api/v2/connects/{connect}/tasks/{task_id}/status',
        'api/v2/connects/{connect}/scheduled-updates',
        'api/v2/sync/{connect}/upload-attachment',
    ];

    public const CONNECT_IGNORE_APIS = [
        'api/v1/check-key',
        'api/v1/connects',
        'api/v2/connects/plans',
        'api/v2/migrates-v3',
        'api/v2/migrates-v3/local-push',
        'api/v2/migrates-v3/finish-local-staging',
        'api/v2/migrates-v3/log',
        'api/v2/migrates-v3/{migrate_id}/update-status',
        'api/v2/migrates-v3/{migrate}',
        'api/v2/validate-token',
    ];

    public const SSH_KEY_TYPE = [
        'GIT_DEPLOYMENT' => 'git_deployment',
        'HOSTING' => 'hosting',
    ];

    public const USERMAVEN_EVENTS = [
        'signed_up',
        'logged_in',
        'logged_out',
        'trial_started',
        'trial_ended',
        'plan_upgraded',
        'plan_downgraded',
        'plan_subscribed',
        'site_created',
        'template_created',
        'paywall_modal',
        'paywall_tooltip',
        'template_created',
        'site_opened',
    ];

    public const CUSTOMERIO_EVENTS = [
        'signed_up',
        'logged_in',
        'logged_out',
        'trial_started',
        'trial_ended',
        'plan_upgraded',
        'plan_downgraded',
        'plan_subscribed',
        'site_created',
        'template_created',
        'paywall_modal',
        'paywall_tooltip',
        'template_created',
        'site_opened',
    ];

    public const RC_SITE_STATUS = [
        'deleted' => 0,
        'suspended' => 1,
        'restore_failed' => 2,
        'live' => 3,
        'not_resolved' => 4,
        'cloned' => 5,
        'clone_failed' => 6,
        'wp_error' => 7,
    ];

    public const INSTAWP_BRANDING = [
        'PRIMARY_COLOR' => '#005e54',
        'SECONDARY_COLOR' => '#15b881',
        'LOGO' => 'images/instawp-migrate-logo.svg',
    ];

    public const CHART_COLOR = [
        'php' => '#7377AD',
        'static' => '#008F8C',
        'domain' => '#F27405',
        'GET' => '#008F8C',
        'POST' => '#F27405',
        '301' => '#FFBC05',
        '302' => '#F27405',
        '200' => '#008F8C',
        '404' => '#7377AD',
        '0' => '#7377AD',
        '1' => '#008F8C',

    ];

    public const IMPORT_BACKUP_DIR = 'import_backup';

    public const MIGRATE_V3_LOG_TYPE = [
        'APP' => 'app',
        'CLOUD' => 'cloud',
        'INSTACP' => 'instacp',
        'PLUGIN' => 'plugin',
    ];

    public const SITE_TYPES = [
        'app_pool' => 'App Pool',
        'sandbox' => 'Sandbox',
        'ordered' => 'Ordered',
        'staging' => 'Staging',
        'live' => 'Live',
    ];

    public const CONNECT_FEATURES_SUPPORTED_VERSION = [
        'user_management' => '********',
        'config_manager' => '********',
        'plugin_theme' => '********',
        'scheduled_updates' => '********',
        'activity_logs' => '********',
    ];

    public const PERFORMANCE_REPORT_GRAPH = [
        'pass' => [
            'name' => 'Pass',
            'color' => '#00cc66',
        ],
        'average' => [
            'name' => 'Average',
            'color' => '#ffaa33',
        ],
        'fail' => [
            'name' => 'Fail',
            'color' => '#ff3333',
        ],
    ];

    public const OtherSubscriptionTypes = ['Hosting Package', 'Wizard Package', 'Waas Seller Package', 'Global Package'];
}
