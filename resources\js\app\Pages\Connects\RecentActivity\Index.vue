<template>
  <div class="w-full rounded-lg border border-grayCust-180 p-4 mt-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <p class="text-base font-medium text-grayCust-1520 mb-1.5">
          Recent Activity
        </p>
        <p class="text-sm text-grayCust-500">
          Events happening within your website
        </p>
      </div>
      <div>
        <CommonListbox
          :options="baseList"
          :value="selectedOption"
          button-class="w-[200px]"
          options-class="w-[200px]"
          width="w-auto"
          @select="onHandleDropdownChange"
        />
      </div>
    </div>
    <div
      v-for="activity in activities"
      :key="activity.id"
    >
      <div class="mt-4 flex items-center justify-between border-t border-grayCust-180 pt-4">
        <p class="mb-0 text-sm font-medium text-grayCust-1800">
          {{ activity.description }}
        </p>
        <CButton
          btn-title="View"
          btn-type="primary-text-btn"
          icon-name="ExternalLinkIcon"
          icon-position="last"
        />
      </div>
    </div>
  </div>
</template>

<script>

import CommonListbox from '@/app/Common/CommonListbox.vue';

export default {
  name: "RecentActivity",
  components: {
    CommonListbox,
  },
  data() {
    return {
      selectedOption: {
        id: 1,
        value: 'Today',
        name: 'Today',
      },
      baseList: [
        {
          id: 1,
          value: 'Today',
          name: 'Today',
        },
        {
          id: 2,
          value: 'Yesterday',
          name: 'Yesterday',
        },
        {
          id: 3,
          value: 'Past 7 Days',
          name: 'Past 7 Days',
        },
      ],
      activities: [
        {
          id: 1,
          description: 'Post 1 Deleted 1 min ago by Abhishek',
        },
        {
          id: 2,
          description: 'User admin was Updated by Vikas',
        },
        {
          id: 3,
          description: 'User admin was Updated by Vikas',
        },
      ],
    }
  },
  methods: {
    onHandleDropdownChange(optionValue) {
      this.selectedOption = optionValue;
    }
  }
}
</script>

<style></style>