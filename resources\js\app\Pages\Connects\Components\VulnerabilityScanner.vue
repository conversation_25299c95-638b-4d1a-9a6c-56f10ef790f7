<template>
  <Head>
    <title>Vulnerability Scanner - {{ setting.brandShortName }}</title>
  </Head>
  <ConnectLayout
    :connect="$props.connect"
    :title="$t('vulnerability_scanner')"
    :sub-title="$t('we_will_check_vulnerabilities_against_core_installed_plugins_and_installed_themes')"
  >
    <template #right>
      <div class="flex md:flex-wrap items-center gap-3">
        <template
          v-if="isSwitchDisabled"
        >
          <CommonSwitch
            v-tooltip="isAutomatedVulnerabilityScanEnabled ? $t('turn_off_automated_vulnerability_scan') : $t('turn_on_automated_vulnerability_scan')"
            :disabled="true"
            @open-disabled-modal="handleSwitchClick"
          />
          <CommonButton
            icon-name="OutlineLockClosedIcon"
            btn-type="gray-outline-btn"
            :btn-title="$t('new_scan')"
            @click="createReport"
          />
        </template>
        <template
          v-else
        >
          <CommonSwitch
            v-model="isAutomatedVulnerabilityScanEnabled"
            v-tooltip="isAutomatedVulnerabilityScanEnabled ? $t('turn_off_automated_vulnerability_scan') : $t('turn_on_automated_vulnerability_scan')"
            type="secondary"
          />
          <CommonButton
            image-path="/images/svgviewer.svg"
            btn-type="gray-outline-btn"
            :btn-title="$t('new_scan')"
            @click="createReport"
          />
        </template>
      </div>
    </template>
    <div
      v-if="!processing && !(Array.isArray(reportList) && reportList.length) && !creatingReport"
      class="card-main p-6"
    >
      <!-- empty-data-section -->
      <EmptyData
        subtitle-class="text-grayCust-500 text-sm font-normal"
        image-name="images/scanner.svg"
        title-class="text-warning-1300 text-sm font-medium"
        main-class="py-16"
        icon="security-empty.svg"
        :title="$t('no_vulnerability_report_title')"
        :subtitle="$t('no_vulnerability_report_subtitle')"
        button-text="Scan"
        img-class="h-8 w-8"
        @on_button_click="createReport"
      />
    </div>
    <!-- inprogress-section -->
    <div
      v-if="creatingReport && Array.isArray(reportList) && !reportList.length"
      class="card-main p-6"
    >
      <InProgress :show-progress="true" />
    </div>

    <div
      v-if="Array.isArray(reportList) && reportList.length"
      class="mb-6 space-y-6"
    >
      <div class="space-y-1 card-main p-4">
        <div>
          <Vue3ChartJs
            :id="barChartData.id"
            ref="vulnerabilityChart"
            :type="barChartData.type"
            :data="barChartData.data"
            :options="barChartData.options"
            style="max-height: 63px"
          />
        </div>

        <div class="flex flex-wrap items-center justify-between">
          <span class="text-xs text-grayCust-910 sm2:text-sm">{{ $t('30_days_ago') }}</span>
          <span class="text-xs text-grayCust-910 sm2:text-sm">{{ $t('today') }}</span>
        </div>
      </div>

      <div class="">
        <CommonTable
          :fields="tableHeaderData"
          :items="{ data: getReportsList }"
          :responsive="true"
          :busy="initialLoading"
          row-bordered
          row-height="lg:h-[40px]"
          header-height="lg:h-10"
          bordered
          border-rounded
        >
          <template #tbody-start>
            <tr
              v-if="creatingReport"
              class="border-y"
            >
              <td
                colspan="2"
                class="whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-grayCust-800"
              >
                <div class="flex flex-wrap items-center lg:flex-nowrap">
                  <div>
                    <img
                      v-lazy="cdn('images/SiteEdit/Security/www.gif')"
                      alt="gif"
                      class="mx-auto lg:w-24"
                    >
                  </div>
                  <div>
                    <div class="mb-1 text-sm font-medium text-grayCust-700">
                      {{ $t('scanning_in_progress') }}
                    </div>
                    <div class="mb-1 text-sm font-normal text-grayCust-500">
                      {{ $t('please_wait') }}
                    </div>
                  </div>
                </div>
              </td>
              <td
                colspan="2"
                class="whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-grayCust-800"
              >
                <InProgress :show-progress="false" />
                <!-- <img v-lazy="cdn('images/SiteEdit/Security/progressbar-image.gif')" class="w-64" alt=""> -->
              </td>
            </tr>
          </template>
          <template #cell_date="{ item }">
            <div class="text-sm font-medium text-grayCust-800">
              {{ dateFormatToLocal(item.created_at) }}
              <!-- <span v-if="item.is_current_scan"> ({{$t('current_scan')}})</span> -->
            </div>
          </template>
          <template #cell_status="{ item }">
            <div
              class="inline-block rounded-xl px-6 py-0.5 text-sm font-medium text-grayCust-800"
              :style="{ backgroundColor: item?.status_details?.color + '1a', color: item?.status_details?.color }"
            >
              {{ item?.status_details?.name }}
            </div>
          </template>
          <template #cell_type="{ item }">
            <div class="rounded-xl text-sm font-medium text-grayCust-800">
              {{ item?.scan_type == 'automated' ? $t('automated') : $t('on_demand') }}
            </div>
          </template>
          <template #cell_summary="{ item }">
            <div class="rounded-xl text-sm font-medium text-grayCust-800">
              {{ item?.summary }}
              <span
                v-if="item?.status_details?.name !== 'Healthy'"
                class="ml-3 inline-flex w-11 cursor-pointer items-center text-primary-900"
                @click="toggleReportDetails(item.id)"
              >
                <span v-if="openReportIds.includes(item.id)">{{ $t('hide') }}</span>
                <span v-else>{{ $t('show') }}</span>
                <img
                  v-lazy="cdn('images/SiteEdit/Security/down-arrow.svg')"
                  :class="openReportIds.includes(item.id) ? 'rotate-180' : ''"
                  alt="gif"
                  class="ml-2"
                ></span>
            </div>
          </template>
          <template #after-row="{ item }">
            <tr
              v-if="openReportIds.includes(item.id)"
              :class="'border-b border-grayCust-900'"
              :style="openReportIds.includes(item.id) ? `background: ${item?.status_details?.color}1a` : ''"
            >
              <td
                colspan="4"
                class="p-6 pt-0"
              >
                <CommonTable
                  :fields="summaryTableHeaderData"
                  :items="{ data: item.inventories }"
                  :responsive="true"
                  row-height="lg:h-[40px]"
                  header-height="lg:h-10"
                  bordered
                >
                  <template #cell_component="{ item: inventory }">
                    <div class="text-sm font-medium text-grayCust-800 inline-flex items-center">
                      <img
                        v-lazy="inventory.icon_url"
                        class="mr-2 size-5"
                      >
                      {{ inventory.name }}
                    </div>
                  </template>
                  <template #cell_severity="{ item: inventory }">
                    <div class="text-left text-sm font-normal text-grayCust-500 flex items-center">
                      <div
                        class="mr-2 size-2 rounded-full"
                        :style="{ backgroundColor: inventory?.severity_details?.color }"
                      />
                      <span :style="{ color: inventory?.severity_details?.color }">
                        {{ inventory?.severity_details?.name }}
                      </span>
                    </div>
                  </template>
                  <template #cell_version="{ item: inventory }">
                    <div class="text-left text-sm font-normal text-grayCust-500">
                      {{ inventory?.other_data?.current_version }}
                    </div>
                  </template>
                  <template #cell_fixed_in="{ item: inventory }">
                    <div class="text-left text-sm font-normal text-grayCust-500">
                      {{ inventory?.other_data?.v_fixed_in || 'Not Fixed' }}
                    </div>
                  </template>
                  <template #cell_reference="{ item: inventory }">
                    <div class="text-left text-sm font-normal text-grayCust-500">
                      <button
                        v-if="inventory.severity != 'closed' || (inventory.severity == 'closed' && inventory.other_data?.closed_reason)"
                        type="button"
                        @click="showReadMoreData = inventory; showReadMore = true;"
                      >
                        {{ $t('read_more') }}
                      </button>
                    </div>
                  </template>
                </CommonTable>
              </td>
            </tr>
          </template>
        </CommonTable>
      </div>
    </div>
    <CommonPagination
      v-if="Array.isArray(reportList) && reportList.length && pagination.lastPage > 1"
      v-model:current-page="pagination.currentPage"
      v-model:per-page="pagination.perPage"
      :total="pagination.total"
      :resource-name="reportList.length > 1 ? $t('reports') : $t('report')"
      @page-change="getReports"
      @per-page-change="handlePerPageChange"
    />
    <advance-connect-site-modal
      :is-open="showAdvanceConnectModal"
      :plan-data="$props.planData"
      :connect="$props.connect"
      @refresh-connects="handleRefreshConnects"
      @close-modal="showAdvanceConnectModal = false"
    />
  </ConnectLayout>
  <!-- Read More modal -->
  <CommonModal
    :model-value="showReadMore"
    size="lg"
    :allow-padding="false"
    @close="showReadMore = false"
  >
    <template #default>
      <div class="p-6">
        <div v-if="showReadMoreData">
          <div class="inline-flex items-center">
            <img
              v-lazy="showReadMoreData.icon_url"
              class="mr-2 size-5"
            >
            <strong>{{ showReadMoreData.name }}</strong>
          </div>
          <div
            v-if="showReadMoreData?.other_data?.v_title"
            class="text-md mt-2 font-medium"
          >
            {{ showReadMoreData?.other_data?.v_title }}
          </div>
          <div
            v-if="showReadMoreData?.other_data?.v_descp"
            class="mt-2 text-sm"
          >
            {{ showReadMoreData?.other_data?.v_descp }}
          </div>
          <div
            v-if="showReadMoreData?.other_data?.closed_reason"
            class="mt-2 text-sm"
          >
            {{ showReadMoreData?.other_data?.closed_reason }}
          </div>
          <div
            v-if="showReadMoreData?.other_data?.v_ref_links !== null"
            class="mt-4 text-sm"
          >
            <a
              v-for="(item, index) in showReadMoreData.other_data.v_ref_links"
              :key="index"
              :href="item"
              target="_blank"
              class="mb-2 flex items-center text-sm font-medium text-primary-900"
            >
              <svg
                class="mr-1 size-5"
                viewBox="0 0 16 8"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                role="none"
              >
                <path
                  d="M11.7499 0.25H8.74988V1.75H11.7499C12.9874 1.75 13.9999 2.7625 13.9999 4C13.9999 5.2375 12.9874 6.25 11.7499 6.25H8.74988V7.75H11.7499C13.8199 7.75 15.4999 6.07 15.4999 4C15.4999 1.93 13.8199 0.25 11.7499 0.25ZM7.24988 6.25H4.24988C3.01238 6.25 1.99988 5.2375 1.99988 4C1.99988 2.7625 3.01238 1.75 4.24988 1.75H7.24988V0.25H4.24988C2.17988 0.25 0.499878 1.93 0.499878 4C0.499878 6.07 2.17988 7.75 4.24988 7.75H7.24988V6.25ZM4.99988 3.25H10.9999V4.75H4.99988V3.25Z"
                  fill="#005E54"
                  role="none"
                />
              </svg> References Link {{ index + 1 }}
            </a>
          </div>
        </div>
        <div class="mt-5 flex justify-end">
          <button
            type="button"
            class="inline-flex w-24 justify-center rounded-md border border-grayCust-900 bg-white px-6 py-3 text-base font-medium text-grayCust-800 shadow-sm focus:ring-2 focus:ring-secondary-800 focus:ring-offset-2 focus-visible:outline-none sm:col-start-1 sm:mt-0 sm:text-sm"
            @click="showReadMore = false;"
          >
            {{ $t('close') }}
          </button>
        </div>
      </div>
    </template>
  </CommonModal>
</template>

<script>
import CommonButton from "@/app/Common/CommonButton.vue";
import CommonModal from "@/app/Common/CommonModal.vue";
import CommonPagination from "@/app/Common/CommonPagination.vue";
import CommonSwitch from "@/app/Common/CommonSwitch.vue";
import CommonTable from "@/app/Common/CommonTable.vue";
import InProgress from "@/app/Pages/Connects/Components/InProgress.vue";
import ConnectLayout from "@/app/Pages/Connects/ConnectLayout.vue";
import EmptyData from "@/Common/EmptyData.vue";
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';

import Vue3ChartJs from '@j-t-mcc/vue3-chartjs';
import axios from 'axios';
import { trans, wTrans } from 'laravel-vue-i18n';
import { mapState, mapStores } from 'pinia';
import { defineAsyncComponent, nextTick } from 'vue';

const AdvanceConnectSiteModal = defineAsyncComponent(() => import('@/components/Connect/AdvanceConnectSiteModal/Index.vue'));

export default {
  name: "ConnectVulnerabilityScanner",
  components: {
    AdvanceConnectSiteModal,
    ConnectLayout,
    EmptyData,
    InProgress,
    Vue3ChartJs,
    CommonModal,
    CommonSwitch,
    CommonButton,
    CommonTable,
    CommonPagination
  },
  props: {
    connect: {
      type: Object,
      required: true
    },
    severityData: {
      type: Array,
      required: true
    },
    planData: {
      type: Object,
      required: true
    },
    scanCount: {
      type: Number,
      required: true
    },
    scanMaxCount: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      activeTabName: "VulnerabilityScannerTab",
      isAdvanced: this.$props.connect.advanced ?? false,
      showAdvanceConnectModal: false,
      isAutomatedVulnerabilityScanEnabled: (this.$props.connect.automated_vulnerability_scan && this.$props.connect.advanced) ?? false,
      automatedVulnerabilityScanToggle: false,
      watchAutomatedVulnerabilityScanEnabled: true,
      processing: false,
      reportList: null,
      creatingReport: false,
      openReportIds: [],
      showReadMore: false,
      showReadMoreData: null,
      setting: new Setting(),
      pagination: {
        perPage: '10', // String to match with dropdown values
        currentPage: 1,
        lastPage: 0,
        total: 0
      },
      tableHeaderData: [
        {
          key: 'date',
          label: wTrans('date'),
          headerClass: 'w-1/2'
        },
        {
          key: 'status',
          label: wTrans('status')
        },
        {
          key: 'type',
          label: wTrans('type')
        },
        {
          key: 'summary',
          label: wTrans('summary'),
        },
      ],
      summaryTableHeaderData: [
        {
          key: 'component',
          label: wTrans('component'),
          headerClass: 'w-1/2'
        },
        {
          key: 'severity',
          label: wTrans('severity')
        },
        {
          key: 'version',
          label: wTrans('version')
        },
        {
          key: 'fixed_in',
          label: wTrans('fixed_in'),
        },
        {
          key: 'reference',
          label: wTrans('reference'),
        },
      ],
      barChartData: {
        type: "bar",
        id: 'requestsPerSecond',
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function (context) {
                  const label = context.dataset.label || '';
                  return label;
                }
              }
            },
          },
          scales: {
            y: {
              display: false,
              stacked: true,
            },
            x: {
              stacked: true,
              display: false,
              grid: {
                display: false
              }
            }
          },
        },
        data: {
          labels: [],
          datasets: [],
        }
      },
    };
  },
  computed: {
    ...mapStores(useAppStore),
    ...mapState(useAppStore, [
      "teamCan",
      "featureAvailableFrom",
    ]),
    isVulnerabilityScanningEnabledForPlan() {
      return this.$props.connect.plan_features.vulnerability_scanning ?? false;
    },
    isSwitchDisabled() {
      return !this.isAdvanced || !this.isVulnerabilityScanningEnabledForPlan;
    },
    isButtonEnabled() {
      return this.isAdvanced && this.isVulnerabilityScanningEnabledForPlan;
    },
    canRunScan() {
      return this.$props.scanCount < this.$props.scanMaxCount;
    },
    getReportsList() {
      return this.reportList.map(report => ({
        ...report,
        trClass: this.openReportIds.includes(report.id) ? report.trClass ? `${report?.trClass} border-b-0 transition-none` : 'border-b-0 transition-none' : report.trClass ?? '',
        trStyle: this.openReportIds.includes(report.id) ? `background: ${report?.status_details?.color}1a` : '',
      }));
    }
  },
  watch: {
    isAutomatedVulnerabilityScanEnabled(newValue, oldValue) {
      if (newValue !== oldValue && this.watchAutomatedVulnerabilityScanEnabled) {
        this.handleSwitchChange(newValue, oldValue);
      }
    }
  },
  mounted() {
    this.getReports();
  },
  methods: {
    showUpgradeWarning(feature) {
      const subHeading = trans('security_scan_not_allow')
      const msg = {
        subHeading,
        planMessage: this.featureAvailableFrom[feature] ? trans('this_feature_is_available_in_plan_and_above', { planName: this.featureAvailableFrom[feature] }) : null,
        feature,
        triggerRef: `connect_site_security_scanner_page_${feature}`
      }
      this.appStore.setUpgradeWarning(msg);
    },
    handlePerPageChange(perPage) {
      this.pagination.perPage = perPage;
      this.pagination.currentPage = 1;
      this.getReports();
    },
    setPagination(data) {
      this.pagination.total = data.total;
      this.pagination.lastPage = data.last_page;
      this.pagination.currentPage = data.current_page;
      if (data.per_page) {
        this.pagination.perPage = data.per_page.toString();
      }
    },
    async getReports(page = 1) {
      this.processing = true;
      if (page && Number.isInteger(page)) {
        this.pagination.currentPage = page
      }
      const params = {
        page: this.pagination.currentPage,
        per_page: this.pagination.perPage,
      }
      await axios
        .get(`/api/v2/connects/${this.$props.connect.id}/security-reports`, { params })
        .then((response) => {
          const responseData = response?.data;
          this.reportList = responseData?.data;
          this.setPagination(responseData?.meta);
          this.calculateVulnerabilitiesGraphData(responseData?.vulnerabilities_data);
        })
        .catch(() => {
        })
        .finally(() => {
          this.processing = false;
        });
    },
    createReport() {
      if (!this.teamCan?.vulnerability_scanner) {
        this.showUpgradeWarning('vulnerability_scanner')
        return
      }
      if (!this.isAdvanced) {
        this.showAdvanceConnectModal = true;
        return;
      }
      if (!this.isVulnerabilityScanningEnabledForPlan) {
        this.appStore.setNotification({
          heading: trans('error'),
          subHeading: trans('not_available_in_your_plan'),
          type: "error",
        });
        return;
      }
      if (!this.canRunScan) {
        this.appStore.setNotification({
          heading: trans('error'),
          subHeading: trans('vulnerability_scan_limit_reached', { hours: this.getHoursUntilMidnight() }),
          type: "error",
        });
        return;
      }

      this.creatingReport = true
      axios.post(`/api/v2/connects/${this.$props.connect.id}/security-reports`)
        .then(response => {
          const message = {
            heading: trans('success'),
            subHeading: response.data.message,
            type: "success",
          };
          this.appStore.setNotification(message);
        })
        .then(() => this.creatingReport = false)
        .then(() => {
          this.getReports()
        })
        .catch(() => {
          // let message = error?.response?.data.message || error.message
          // this.appStore.setNotification({
          //     heading: trans('failed') ,
          //     subHeading: message,
          //     type: "error",
          // });
        })
        .finally(() => {
          this.creatingReport = false
        });
    },
    handleRefreshConnects() {
      this.showAdvanceConnectModal = false;
      this.isAdvanced = true;
      if (this.automatedVulnerabilityScanToggle) {
        this.isAutomatedVulnerabilityScanEnabled = !this.isAutomatedVulnerabilityScanEnabled;
      } else {
        this.$inertia.reload({
          only: ['connect', 'planData', 'scanCount', 'scanMaxCount'],
          onSuccess: () => {
            this.createReport();
          }
        });
      }
    },
    toggleReportDetails(id) {
      if (this.openReportIds.includes(id)) {
        this.openReportIds.splice(this.openReportIds.indexOf(id), 1);
      } else {
        this.openReportIds.push(id)
      }
    },
    dateFormatToLocal(utc_date, time = true) {
      const args = {
        year: "numeric",
        month: "short",
        day: "numeric"
      }
      if (time) {
        args.hour = "2-digit";
        args.minute = "2-digit";
        args.hour12 = true;
      }
      return new Date(utc_date).toLocaleDateString('en-US', args);
    },
    getHoursUntilMidnight() {
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const diffInHours = (tomorrow - now) / (1000 * 60 * 60);
      return Math.ceil(diffInHours);
    },
    calculateVulnerabilitiesGraphData(data) {
      if (!data || data.length == 0) {
        return;
      }

      this.barChartData.data.labels = Object.keys(data).toReversed().map(date => this.dateFormatToLocal(date, false));
      this.barChartData.data.datasets = [
        {
          label: trans('not_scanned'),
          data: new Array(Object.keys(data).length).fill(1),
          backgroundColor: '#EFEFEF',
          borderRadius: 2,
          barThickness: 20,
        },
        {
          label: trans('no_vulnerabilities_found'),
          data: new Array(Object.keys(data).length).fill(null),
          backgroundColor: '#A7F3D0',
          borderRadius: 2,
          barThickness: 20,
        },
      ];

      const reversedData = Object.values(data).toReversed();
      for (const [severityIndex, [key, severity]] of Object.entries(this.$props.severityData).entries()) {
        const graphData = reversedData.map((item, index) => {
          if (item.scanned) {
            if (severityIndex == 0 && this.barChartData.data.datasets[1].data[index] == null) {
              this.barChartData.data.datasets[0].data[index] = null;
              this.barChartData.data.datasets[1].data[index] = 1;
            }
            if (item.total > 0) {
              const value = Number((item[key] / item.total).toFixed(2));
              const newValue = parseFloat((this.barChartData.data.datasets[1].data[index] - value).toFixed(2));
              this.barChartData.data.datasets[1].data[index] = newValue == 0 ? null : newValue;
              return value;
            }
          }
          return null;
        });

        this.barChartData.data.datasets.push({
          label: severity.name,
          data: graphData,
          backgroundColor: severity.color,
          borderRadius: 2,
          barThickness: 20,
        });
      }

      nextTick(() => {
        if (this.$refs.vulnerabilityChart) {
          this.$refs.vulnerabilityChart.update();
        }
      });
    },
    handleSwitchClick() {
      if (!this.isAdvanced) {
        this.automatedVulnerabilityScanToggle = true;
        this.showAdvanceConnectModal = true;
        return;
      }
      if (!this.isVulnerabilityScanningEnabledForPlan) {
        this.appStore.setNotification({
          heading: trans('error'),
          subHeading: trans('not_available_in_your_plan'),
          type: "error",
        });
      }
    },
    handleSwitchChange(value, oldValue) {
      this.watchAutomatedVulnerabilityScanEnabled = false;
      axios.post(`/api/v2/connects/${this.$props.connect.id}/features`, {
        automated_vulnerability_scan: value
      }).then(res => {
        if (res.data.status) {
          this.appStore.setNotification({
            heading: trans('success'),
            subHeading: res.data.message,
            type: "success",
          });
        }
      }).catch(() => {
        this.isAutomatedVulnerabilityScanEnabled = oldValue;
      }).finally(() => {
        this.automatedVulnerabilityScanToggle = false;
        this.watchAutomatedVulnerabilityScanEnabled = true;
      });
    }
  },
};
</script>