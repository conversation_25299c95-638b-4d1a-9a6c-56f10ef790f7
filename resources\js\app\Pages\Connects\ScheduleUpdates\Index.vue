<template>
  <div v-if="isScheduledUpdateEnabled">
    <CButton
      v-if="currentState"
      :btn-title="$t('scheduled')"
      btn-type="gray-outline-btn"
      icon-color="text-secondary-800"
      image-path="images/ConnectSiteUpdate/calendar-card.svg"
      extra-class="!border-primary-50 !text-secondary-800"
      @click="isOpen = true"
    />
    <CButton
      v-else
      :btn-title="$t('scheduled_updates')"
      icon-name="OutlineCalendarIcon"
      btn-type="gray-outline-btn"
      @click="isOpen = true"
    />
  </div>
  <CButton
    v-else
    :btn-title="$t('scheduled_updates')"
    icon-name="OutlineLockClosedIcon"
    btn-type="gray-outline-btn"
    @click="showNotice"
  />
  <advance-connect-site-modal
    :is-open="showAdvanceConnectModal"
    :plan-data="$props.planData"
    :connect="$props.connect"
    @refresh-connects="handleRefreshConnects"
    @close-modal="showAdvanceConnectModal = false"
  />
  <!-- <DialogModal
    :show="isOpen"
    main-class="modal-margin"
    max-width="xl"
    extra-flex="site-model flex justify-center items-center"
    header-class="modal-padding"
    extra-class="md:min-w-[560px] min-w-full !rounded-2xl"
    @close="isOpen = false"
  > -->
  <CommonModal
    v-model="isOpen"
    size="xl"
    :allow-padding="false"
    @close="isOpen = false"
  >
    <template #header>
      <h2 class="text-xl font-semibold text-grayCust-430">
        {{ $t('scheduled_updates') }}
      </h2>
    </template>

    <template #content>
      <div class="p-6 space-y-6">
        <div class="space-y-6">
          <div class="grid gap-3 sm2:grid-cols-2">
            <div class="space-y-2">
              <label class="text-sm font-medium text-grayCust-910">{{ $t('frequency') }}</label>
              <!-- <Dropdown>
                <template #listBox>
                  <ListBox
                    btn-icon-name="SelectorIcon"
                    :option-list="frequencyList"
                    :selected-option="selectedPeriod"
                    @on-handle-change="onHandlePeriodChange"
                  />
                </template>
              </Dropdown> -->
              <CommonListbox
                :options="frequencyList"
                :value="selectedPeriod"
                label-key="name"
                value-key="value"
                return-key="value"
                placeholder="Select frequency"
                button-class="w-full"
                options-class="w-full"
                @select="onHandlePeriodChange"
              />
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-grayCust-910">{{ $t('every') }}</label>
              <div class="flex items-center rounded-lg shadow-sm">
                <!-- <input
                  v-model="interval"
                  type="number"
                  min="1"
                  name="every"
                  class="flex h-10 w-full flex-auto items-center rounded-l-lg border border-warning-1020 bg-white px-3 py-2 text-base text-grayCust-1740 transition-shadow placeholder:text-grayCust-630 focus:border-secondary-800 focus:outline-none focus:ring-4 focus:ring-primary-275 disabled:cursor-not-allowed disabled:bg-grayCust-50"
                  @input="onIntervalChange"
                > -->
                <CommonInput
                  id="interval-input"
                  v-model="interval"
                  type="number"
                  :min="1"
                  extra-class="!rounded-r-none  w-full"
                  @input="onIntervalChange"
                />
                <div
                  class="flex h-[34px] items-center text-sm rounded-r-lg border border-l-0 border-warning-1020 bg-white px-3 py-2 text-grayCust-630"
                >
                  {{ selectedPeriod.value === "daily" ? "day(s)" : selectedPeriod.value === "weekly" ? "week(s)" :
                    selectedPeriod.value === "monthly" ? "month(s)" : "day(s)" }}
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="selectedPeriod.value === 'weekly'"
            class="flex flex-wrap items-center gap-3"
          >
            <span class="text-sm font-medium text-grayCust-910">{{ $t('on') }} :</span>
            <SelectButton
              v-for="day in dayList"
              :id="day.value"
              :key="day.id"
              v-model="selectedDay"
              :label="day.label"
              name="week"
              :value="day.value"
            />
          </div>

          <div
            v-if="selectedPeriod.value === 'monthly'"
            class="space-y-4"
          >
            <div class="space-y-4">
              <div class="flex items-center gap-2">
                <!-- <Radio
                  id="each"
                  v-model="selectedMonthlyOption"
                  :checked="selectedMonthlyOption"
                  value="dates"
                  name="month"
                /> -->
                <CommonRadio
                  id="each"
                  v-model="selectedMonthlyOption"
                  :label="$t('each')"
                  value="dates"
                  name="month"
                />
              </div>
              <div
                v-if="selectedMonthlyOption === 'dates'"
                class="flex flex-wrap items-center justify-center gap-[18px]"
              >
                <SelectButton
                  v-for="date in 31"
                  :id="date"
                  :key="date.id"
                  v-model="selectedDate"
                  :label="date"
                  name="date"
                  :value="date"
                />
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-center gap-2">
                <!-- <Radio
                  id="on-the"
                  v-model="selectedMonthlyOption"
                  :checked="selectedMonthlyOption"
                  value="day"
                  name="month"
                /> -->
                <CommonRadio
                  id="on-the"
                  v-model="selectedMonthlyOption"
                  :label="$t('on_the')"
                  value="day"
                  name="month"
                />
              </div>
              <div
                v-if="selectedMonthlyOption === 'day'"
                class="grid grid-cols-2 gap-3"
              >
                <!-- <Dropdown>
                  <template #listBox>
                    <ListBox
                      :option-list="orderList"
                      :selected-option="selectedOrder"
                      btn-icon-name="SelectorIcon"
                      @on-handle-change="onHandleOrderChange"
                    />
                  </template>
                </Dropdown> -->
                <CommonListbox
                  :options="orderList"
                  :value="selectedOrder"
                  label-key="name"
                  value-key="value"
                  return-key="value"
                  button-class="w-full"
                  options-class="w-full"
                  placeholder="Select order"
                  @select="onHandleOrderChange"
                />
                <!-- <Dropdown>
                  <template #listBox>
                    <ListBox
                      :option-list="orderDayList"
                      :selected-option="selectedOrderDay"
                      btn-icon-name="SelectorIcon"
                      @on-handle-change="onHandleOrderDayChange"
                    />
                  </template>
                </Dropdown> -->
                <CommonListbox
                  :options="orderDayList"
                  :value="selectedOrderDay"
                  label-key="name"
                  value-key="value"
                  button-class="w-full"
                  options-class="w-full"
                  return-key="value"
                  placeholder="Select day"
                  @select="onHandleOrderDayChange"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="space-y-6">
          <div class="space-y-2">
            <label class="text-sm font-medium text-grayCust-910">
              {{ $t('your_local_time', { timezone: current_timezone }) }}
            </label>
            <!-- <Dropdown>
              <template #listBox>
                <ListBox
                  btn-icon-name="SelectorIcon"
                  :option-list="timeSlots"
                  :selected-option="selectedLocalTime"
                  @on-handle-change="onHandleTimeChange"
                />
              </template>
            </Dropdown> -->
            <CommonListbox
              :options="timeSlots"
              :value="selectedLocalTime"
              label-key="name"
              value-key="name"
              return-key="name"
              button-class="w-full"
              options-class="w-full"
              placeholder="Select time"
              @select="onHandleTimeChange"
            />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium text-grayCust-910">
              {{ $t('what_to_update') }}
            </label>
            <div class="flex flex-wrap items-center gap-3">
              <div
                v-for="option in updateList"
                :key="option.id"
              >
                <CheckboxButton
                  :id="option.title"
                  v-model:is-checked="option.isChecked"
                  class="w-fit !gap-2"
                  :title="option.title"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="flex justify-between items-center bg-grayCust-380">
        <CButton
          :btn-title="$t('cancel')"
          btn-type="gray-outline-btn"
          size="btn-sm"
          @click="isOpen = false"
        />
        <div class="flex items-center gap-3">
          <CButton
            :disabled="!currentState"
            :btn-title="$t('stop')"
            btn-type="gray-outline-btn"
            size="btn-sm"
            @click="saveData(false)"
          />
          <CButton
            :btn-title="$t('save')"
            btn-type="secondary"
            size="btn-sm"
            @click="saveData(true)"
          />
        </div>
      </div>
    </template>
    <!-- </DialogModal> -->
  </CommonModal>
</template>

<script>
import SelectButton from '@/app/Pages/Connects/ScheduleUpdates/SelectButton.vue';
import CheckboxButton from '@/app/Common/CheckboxButton.vue';
// import Dropdown from '@/components/New/Blocks/Dropdowns/Dropdown.vue';
// import ListBox from '@/components/New/Blocks/Dropdowns/ListBox/ListBox.vue';
// import Radio from '@/components/New/Blocks/Radios/Radio.vue';
import { versionCompare } from '@/helpers.js';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { wTrans } from 'laravel-vue-i18n';
import moment from 'moment';
import momentDurationFormatSetup from "moment-duration-format";
import { mapStores } from 'pinia';
import { defineAsyncComponent } from 'vue';

const AdvanceConnectSiteModal = defineAsyncComponent(() => import('@/components/Connect/AdvanceConnectSiteModal/Index.vue'));
// const DialogModal = defineAsyncComponent(() => import('@/Jetstream/DialogModal.vue'));
const CommonModal = defineAsyncComponent(() => import('@/app/Common/CommonModal.vue'));
const CommonListbox = defineAsyncComponent(() => import('@/app/Common/CommonListbox.vue'));
const CommonInput = defineAsyncComponent(() => import('@/app/Common/CommonInput.vue'));
const CommonRadio = defineAsyncComponent(() => import('@/app/Common/CommonRadio.vue'));

momentDurationFormatSetup(moment);

export default {
  name: "ScheduleUpdates",
  components: {
    AdvanceConnectSiteModal,
    // Dropdown,
    // ListBox,
    CommonListbox,
    CommonInput,
    CommonRadio,
    CheckboxButton,
    SelectButton,
    // Radio,
    // DialogModal
    CommonModal
  },
  props: {
    connect: {
      type: Object,
      required: true
    },
    planData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      popoverRef: null,
      current_timezone: this.$props.connect.user.timezone ? moment().tz(this.$props.connect.user.timezone).format('Z') : moment().format('Z'),
      isAdvanced: false,
      showAdvanceConnectModal: false,
      setting: new Setting(),
      scheduled: false,
      interval: 1,
      selectedPeriod: {},
      selectedLocalTime: {},
      selectedOrder: {},
      selectedOrderDay: {},
      selectedDay: [],
      isOpen: false,
      selectedDate: [],
      selectedMonthlyOption: 'dates',
      frequencyList: [
        {
          id: 1,
          name: wTrans('daily'),
          value: 'daily',
        },
        {
          id: 2,
          name: wTrans('weekly'),
          value: 'weekly',
        },
        {
          id: 3,
          name: wTrans('monthly'),
          value: 'monthly',
        },
      ],
      updateList: [
        {
          id: 1,
          title: wTrans('core'),
          value: 'core',
          isChecked: true,
        },
        {
          id: 2,
          title: wTrans('plugins'),
          value: 'plugins',
          isChecked: true,
        },
        {
          id: 3,
          title: wTrans('themes'),
          value: 'themes',
          isChecked: true,
        }
      ],
      orderList: [
        {
          id: 1,
          name: wTrans('first'),
          value: 'first',
        },
        {
          id: 2,
          name: wTrans('second'),
          value: 'second',
        },
        {
          id: 3,
          name: wTrans('third'),
          value: 'third',
        },
        {
          id: 4,
          name: wTrans('fourth'),
          value: 'fourth',
        },
        {
          id: 5,
          name: wTrans('fifth'),
          value: 'fifth',
        },
        {
          id: 6,
          name: wTrans('last'),
          value: 'last',
        }
      ],
      orderDayList: [
        {
          id: 1,
          name: wTrans('sunday'),
          value: 'sunday',
        },
        {
          id: 2,
          name: wTrans('monday'),
          value: 'monday',
        },
        {
          id: 3,
          name: wTrans('tuesday'),
          value: 'tuesday',
        },
        {
          id: 4,
          name: wTrans('wednesday'),
          value: 'wednesday',
        },
        {
          id: 5,
          name: wTrans('thursday'),
          value: 'thursday',
        },
        {
          id: 6,
          name: wTrans('friday'),
          value: 'friday',
        },
        {
          id: 7,
          name: wTrans('saturday'),
          value: 'saturday',
        },
        {
          id: 8,
          name: wTrans('day'),
          value: 'day',
        },
        {
          id: 9,
          name: wTrans('weekday'),
          value: 'weekday',
        },
        {
          id: 10,
          name: wTrans('weekend_day'),
          value: 'weekend_day',
        },
      ],
      dayList: [
        {
          id: 1,
          label: 'S',
          value: 'sunday',
          isChecked: false,
        },
        {
          id: 2,
          label: 'M',
          value: 'monday',
          isChecked: false,
        },
        {
          id: 3,
          label: 'T',
          value: 'tuesday',
          isChecked: false,
        },
        {
          id: 4,
          label: 'W',
          value: 'wednesday',
          isChecked: false,
        },
        {
          id: 5,
          label: 'T',
          value: 'thursday',
          isChecked: false,
        },
        {
          id: 6,
          label: 'F',
          value: 'friday',
          isChecked: false,
        },
        {
          id: 7,
          label: 'S',
          value: 'saturday',
          isChecked: false,
        },
      ],
      currentState: false,
    };
  },
  computed: {
    ...mapStores(useAppStore),
    timeSlots() {
      const slots = [];
      let hours = 0;
      let minutes = 0;
      let id = 1;

      while (hours < 24) {
        const time = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        slots.push({
          id: id++,
          name: time
        });

        minutes += 30;
        if (minutes === 60) {
          minutes = 0;
          hours++;
        }
      }
      return slots;
    },
    isScheduledUpdateEnabledForPlan() {
      return this.$props.connect.plan_features.scheduled_updates ?? false;
    },
    isScheduledUpdateEnabled() {
      const versionCompatible = versionCompare(this.$props.connect?.plugin_version, this.$props.connect?.plugin_compatibility.scheduled_updates, '>=')
      return versionCompatible && this.isAdvanced && this.isScheduledUpdateEnabledForPlan;
    }
  },
  created() {
    this.isAdvanced = this.$props.connect?.advanced ?? false;
    const scheduledUpdate = this.$props.connect?.scheduled_update;
    if (scheduledUpdate) {
      this.currentState = !!scheduledUpdate.enabled;
      if (scheduledUpdate.update_items.length > 0) {
        this.updateList.forEach(item => item.isChecked = scheduledUpdate.update_items.includes(item.value));
      }
      this.interval = scheduledUpdate.interval;
      this.selectedMonthlyOption = scheduledUpdate.monthly_frequency ?? 'dates';
      this.selectedDay = scheduledUpdate.weekly_on ?? [];
      this.selectedDate = scheduledUpdate.monthly_on ?? [];
      this.selectedPeriod = this.frequencyList.find(item => item.value === scheduledUpdate.frequency) ?? this.frequencyList[0];
      this.selectedLocalTime = this.timeSlots.find(item => item.name === scheduledUpdate.time.slice(0, -3)) ?? this.timeSlots[0];
      this.selectedOrder = this.orderList.find(item => item.value === scheduledUpdate.monthly_order) ?? this.orderList[0];
      this.selectedOrderDay = this.orderDayList.find(item => item.value === scheduledUpdate.monthly_order_day) ?? this.orderDayList[0];
    } else {
      this.currentState = false;
      this.selectedPeriod = this.frequencyList[0];
      this.selectedLocalTime = this.timeSlots[0];
      this.selectedOrder = this.orderList[0];
      this.selectedOrderDay = this.orderDayList[0];
    }
  },
  methods: {
    onIntervalChange(event) {
      const value = parseInt(event.target.value);
      if (isNaN(value) || value < 1) {
        this.interval = 1;
      } else if (value > 999) {
        this.interval = 999;
      } else {
        this.interval = value;
      }
    },
    saveData(enabled) {
      const localTime = moment(this.selectedLocalTime.name, 'HH:mm');
      const data = {
        frequency: this.selectedPeriod.value,
        interval: this.interval,
        time: this.selectedLocalTime.name,
        time_utc: this.$props.connect.user.timezone ? localTime.tz(this.$props.connect.user.timezone).utc().format('HH:mm') : localTime.utc().format('HH:mm'),
        update_items: this.updateList.filter(item => item.isChecked).map(item => item.value),
        weekly_on: Object.values(this.selectedDay),
        monthly_frequency: this.selectedMonthlyOption,
        monthly_on: Object.values(this.selectedDate),
        monthly_order: this.selectedOrder.value,
        monthly_order_day: this.selectedOrderDay.value,
        enabled: enabled,
      }

      axios.post(`/api/v2/connects/${this.$props.connect.id}/scheduled-updates/save`, data)
        .then(() => {
          const updatedState = data.enabled;
          let messageText;
          
          if (this.currentState === false && updatedState === true) {
            messageText = wTrans('scheduled_updates_enabled', { url: this.$props.connect.url });
          } else if (this.currentState === true && updatedState === false) {
            if (data.update_items.length == 0) {
              this.updateList.forEach(item => item.isChecked = true);
            }
            messageText = wTrans('scheduled_updates_disabled', { url: this.$props.connect.url });
          } else {
            messageText = wTrans('scheduled_updates_updated');
          }

          this.currentState = updatedState;
          
          const message = {
            heading: wTrans('success'),
            subHeading: messageText,
            type: "success",
          };
          this.appStore.setNotification(message);

          this.isOpen = false;
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
        })
    },
    showNotice() {
      if (!this.isAdvanced) {
        this.showAdvanceConnectModal = true;
      } else {
        this.appStore.setNotification({
          heading: wTrans('error'),
          subHeading: this.isScheduledUpdateEnabledForPlan ? wTrans('update_connect_plugin_to_latest_version') : wTrans('not_available_in_your_plan'),
          type: "error",
        });
      }
    },
    onHandlePeriodChange(optionValue) {
      this.selectedPeriod = optionValue;
    },
    onHandleTimeChange(optionValue) {
      this.selectedLocalTime = optionValue
    },
    onHandleOrderChange(optionValue) {
      this.selectedOrder = optionValue
    },
    onHandleOrderDayChange(optionValue) {
      this.selectedOrderDay = optionValue
    },
    handleRefreshConnects() {
      this.$inertia.reload({
        only: ['connect'],
        onSuccess: () => {
          if (this.isScheduledUpdateEnabledForPlan) {
            this.isOpen = true;
          } else {
            this.appStore.setNotification({
              heading: wTrans('error'),
              subHeading: wTrans('not_available_in_your_plan'),
              type: "error",
            });
          }
          this.isAdvanced = true;
          this.showAdvanceConnectModal = false;
        }
      });
    },
  },
};
</script>
<style scoped>
.schedule-panel {
  right: 0;
}

@media (max-width: 520px) {
  .schedule-panel {
    left: 0;
  }
}
</style>
