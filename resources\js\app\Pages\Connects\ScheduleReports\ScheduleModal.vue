<template>
  <CommonModal
    :model-value="isReportModalOpen"
    size="4xl"
    :allow-padding="false"
    @close="closeModal"
  >
    <template #content>
      <div class="">
        <div class="p-6">
          <h2 class="text-xl font-semibold text-grayCust-430 pb-5 border-b border-grayCust-180">
            {{ $t('scheduled_report') }}
          </h2>
        </div>
        <div class="space-y-6 max-h-[550px] overflow-y-auto px-6 pb-6 custom-scrollbar-v2">
          <div class="space-y-1.5">
            <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('reporting_period') }}</label>
            <CommonListbox
              :options="reportingPeriodList"
              :value="selectedReportingPeriod"
              label-key="name"
              value-key="id"
              return-key="value"
              placeholder="Select reporting period"
              button-class="w-full"
              options-class="w-full"
              @select="onHandlePeriodChange"
            />
          </div>
          <div class="space-y-1.5">
            <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('inclusions') }}</label>
            <div class="flex items-center gap-4 flex-wrap">
              <div
                v-for="inclusion in inclusionList"
                :key="inclusion.id"
              >
                <CheckboxButton
                  :id="inclusion.id"
                  v-model:is-checked="inclusion.isChecked"
                  class="w-fit"
                  :title="inclusion.title"
                />
              </div>
            </div>
          </div>
          <div class="space-y-1.5 p-4 rounded-lg bg-grayCust-380 border border-grayCust-160">
            <label class="text-sm font-medium text-grayCust-910">{{ $t('send_copy_of_the_report_to')+': ' }}</label>
            <CommonMultiTagInput
              v-model="customerEmails"
              :placeholder="$t('add_comma_separated_email_addresses')"
            />
          </div>
          <div class="space-y-3 p-4 rounded-lg bg-grayCust-380 border border-grayCust-160">
            <!-- <div class="flex items-center gap-2">
              <Checkbox
                id="schedule"
                v-model="scheduleReport"
              />
              <label
                for="schedule"
                class="text-grayCust-1740 text-sm cursor-pointer font-medium"
              >
                Schedule Report
              </label>
            </div> -->
            <div class="grid gap-3 md2:grid-cols-3">
              <div class="space-y-2">
                <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('frequency') }}</label>
                <CommonListbox
                  :options="frequencyList"
                  :value="selectedPeriod"
                  label-key="name"
                  value-key="id"
                  return-key="value"
                  placeholder="Select frequency"
                  button-class="w-full"
                  options-class="w-full"
                  @select="onHandleFrequencyChange"
                />
              </div>

              <div class="space-y-2">
                <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('every') }}</label>
                <div class="flex items-center rounded-lg">
                  <CommonInput
                    id="interval"
                    v-model="interval"
                    type="number"
                    :min="1"
                    placeholder="1"
                    extra-class="!rounded-l-lg !rounded-r-none w-full"
                    @input="onIntervalChange"
                  />
                  <div
                    class="flex h-[34px] items-center rounded-r-lg border text-sm border-l-0 border-warning-1020 bg-white px-3 py-2 text-grayCust-630"
                  >
                    {{ selectedPeriod.value === "daily" ? "day(s)" : selectedPeriod.value === "weekly" ?
                      "week(s)" :
                      selectedPeriod.value === "monthly" ? "month(s)" : "day(s)" }}
                  </div>
                </div>
              </div>


              <div class="space-y-2">
                <label class="text-grayCust-910 cursor-pointer text-sm font-medium">{{ $t('your_local_time', { timezone: current_timezone }) }}</label>
                <CommonListbox
                  :options="timeSlots"
                  :value="selectedLocalTime"
                  label-key="name"
                  value-key="id"
                  return-key="name"
                  placeholder="Select time"
                  button-class="w-full"
                  options-class="w-full"
                  @select="onHandleTimeChange"
                />
              </div>
            </div>
            <div
              v-if="selectedPeriod.value === 'weekly'"
              class="flex flex-wrap items-center gap-3"
            >
              <span class="text-sm font-medium text-grayCust-910">{{ $t('on') }} :</span>
              <SelectButton
                v-for="day in dayList"
                :id="day.value"
                :key="day.id"
                v-model="selectedDay"
                :label="day.label"
                name="week"
                :value="day.value"
              />
            </div>

            <div
              v-if="selectedPeriod.value === 'monthly'"
              class="space-y-4"
            >
              <div class="space-y-4">
                <div class="flex items-center gap-2">
                  <CommonRadio
                    id="each"
                    v-model="selectedMonthlyOption"
                    :label="$t('each')"
                    value="dates"
                    name="month"
                  />
                </div>
                <div
                  v-if="selectedMonthlyOption === 'dates'"
                  class="flex flex-wrap items-center justify-center gap-[18px]"
                >
                  <SelectButton
                    v-for="date in 31"
                    :id="date"
                    :key="date.id"
                    v-model="selectedDate"
                    :label="date"
                    name="date"
                    :value="date"
                  />
                </div>
              </div>
              <div class="space-y-4">
                <div class="flex items-center gap-2">
                  <CommonRadio
                    id="on-the"
                    v-model="selectedMonthlyOption"
                    :label="$t('on_the')"
                    value="day"
                    name="month"
                  />
                </div>
                <div
                  v-if="selectedMonthlyOption === 'day'"
                  class="grid sm:grid-cols-2 gap-4"
                >
                  <CommonListbox
                    :options="orderList"
                    :value="selectedOrder"
                    label-key="name"
                    value-key="id"
                    return-key="value"
                    placeholder="Select order"
                    button-class="w-full"
                    options-class="w-full"
                    @select="onHandleOrderChange"
                  />
                  <CommonListbox
                    :options="orderDayList"
                    :value="selectedOrderDay"
                    label-key="name"
                    value-key="id"
                    return-key="value"
                    placeholder="Select day"
                    button-class="w-full"
                    options-class="w-full"
                    @select="onHandleOrderDayChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="px-6 py-5 flex flex-wrap flex-row md:items-center justify-between gap-4 bg-grayCust-50 border-t border-grayCust-180"
      >
        <CButton
          :btn-title="$t('cancel')"
          btn-type="gray-outline-btn"
          size="btn-md"
          class="w-fit"
          @click="closeModal"
        />
        <div class="flex gap-4">
          <CButton
            :disabled="!currentState"
            :btn-title="$t('stop')"
            btn-type="gray-outline-btn"
            size="btn-md"
            @click="saveData(false)"
          />
          <CButton
            :btn-title="$t('save')"
            btn-type="secondary"
            size="btn-md"
            @click="saveData(true)"
          />
        </div>
      </div>
    </template>
  </CommonModal>
</template>

<script>
import CheckboxButton from '@/app/Common/CheckboxButton.vue';
import CommonInput from "@/app/Common/CommonInput.vue";
import CommonListbox from "@/app/Common/CommonListbox.vue";
import CommonModal from "@/app/Common/CommonModal.vue";
import CommonMultiTagInput from "@/app/Common/CommonMultiTagInput.vue";
import CommonRadio from "@/app/Common/CommonRadio.vue";
import SelectButton from '@/app/Pages/Connects/ScheduleUpdates/SelectButton.vue';
import Setting from '@/setting.js';
import { useAppStore } from '@/store/index';
import axios from 'axios';
import { wTrans } from 'laravel-vue-i18n';
import moment from 'moment';
import momentDurationFormatSetup from "moment-duration-format";
import { mapStores } from 'pinia';

momentDurationFormatSetup(moment);

export default {
  name: "ReportModal",
  components: {
    CommonModal,
    CommonInput,
    CommonListbox,
    CommonMultiTagInput,
    CommonRadio,
    CheckboxButton,
    SelectButton
  },
  props: {
    connect: {
      type: Object,
      required: true,
    },
    isReportModalOpen: {
      type: Boolean,
      required: false,
    },
    modelValue: {
      type: Array,
      required: true,
    },
  },
  emits: ['closeModal', 'reportSettingsSaved'],
  data() {
    return {
      current_timezone: this.$props.connect.user.timezone ? moment().tz(this.$props.connect.user.timezone).format('Z') : moment().format('Z'),
      setting: new Setting(),
      selectedReportingPeriod: [],
      customerEmails: [],
      interval: 1,
      selectedPeriod: {},
      selectedLocalTime: {},
      selectedOrder: {},
      selectedOrderDay: {},
      selectedDay: [],
      selectedDate: [],
      selectedMonthlyOption: 'dates',
      reportingPeriodList: [
        {
          id: 1,
          value: 'this_week',
          name: wTrans('this_week'),
        },
        {
          id: 2,
          value: 'last_week',
          name: wTrans('last_week'),
        },
        {
          id: 3,
          value: 'this_month',
          name: wTrans('this_month'),
        },
        {
          id: 4,
          value: 'last_month',
          name: wTrans('last_month'),
        },
        {
          id: 5,
          value: 'this_year',
          name: wTrans('this_year'),
        },
        {
          id: 6,
          value: 'last_year',
          name: wTrans('last_year'),
        }
      ],
      frequencyList: [
        {
          id: 1,
          name: wTrans('daily'),
          value: 'daily',
        },
        {
          id: 2,
          name: wTrans('weekly'),
          value: 'weekly',
        },
        {
          id: 3,
          name: wTrans('monthly'),
          value: 'monthly',
        },
      ],
      orderList: [
        {
          id: 1,
          name: wTrans('first'),
          value: 'first',
        },
        {
          id: 2,
          name: wTrans('second'),
          value: 'second',
        },
        {
          id: 3,
          name: wTrans('third'),
          value: 'third',
        },
        {
          id: 4,
          name: wTrans('fourth'),
          value: 'fourth',
        },
        {
          id: 5,
          name: wTrans('fifth'),
          value: 'fifth',
        },
        {
          id: 6,
          name: wTrans('last'),
          value: 'last',
        }
      ],
      orderDayList: [
        {
          id: 1,
          name: wTrans('sunday'),
          value: 'sunday',
        },
        {
          id: 2,
          name: wTrans('monday'),
          value: 'monday',
        },
        {
          id: 3,
          name: wTrans('tuesday'),
          value: 'tuesday',
        },
        {
          id: 4,
          name: wTrans('wednesday'),
          value: 'wednesday',
        },
        {
          id: 5,
          name: wTrans('thursday'),
          value: 'thursday',
        },
        {
          id: 6,
          name: wTrans('friday'),
          value: 'friday',
        },
        {
          id: 7,
          name: wTrans('saturday'),
          value: 'saturday',
        },
        {
          id: 8,
          name: wTrans('day'),
          value: 'day',
        },
        {
          id: 9,
          name: wTrans('weekday'),
          value: 'weekday',
        },
        {
          id: 10,
          name: wTrans('weekend_day'),
          value: 'weekend_day',
        },
      ],
      dayList: [
        {
          id: 1,
          label: 'S',
          value: 'sunday',
          isChecked: false,
        },
        {
          id: 2,
          label: 'M',
          value: 'monday',
          isChecked: false,
        },
        {
          id: 3,
          label: 'T',
          value: 'tuesday',
          isChecked: false,
        },
        {
          id: 4,
          label: 'W',
          value: 'wednesday',
          isChecked: false,
        },
        {
          id: 5,
          label: 'T',
          value: 'thursday',
          isChecked: false,
        },
        {
          id: 6,
          label: 'F',
          value: 'friday',
          isChecked: false,
        },
        {
          id: 7,
          label: 'S',
          value: 'saturday',
          isChecked: false,
        },
      ],
      inclusionList: [
        {
          id: 'summary',
          title: wTrans('summary'),
          isChecked: true,
        },
        {
          id: 'performance',
          title: wTrans('performance'),
          isChecked: true,
        },
        {
          id: 'uptime_history',
          title: wTrans('uptime_history'),
          isChecked: true,
        },
        {
          id: 'security_scan',
          title: wTrans('security_scan'),
          isChecked: true,
        },
        {
          id: 'activity_logs',
          title: wTrans('activity_logs'),
          isChecked: true,
        },
      ],
      currentState: false,
    };
  },
  computed: {
    ...mapStores(useAppStore),
    timeSlots() {
      const slots = [];
      let hours = 0;
      let minutes = 0;
      let id = 1;

      while (hours < 24) {
        const time = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        slots.push({
          id: id++,
          name: time
        });

        minutes += 30;
        if (minutes === 60) {
          minutes = 0;
          hours++;
        }
      }
      return slots;
    },
  },
  created() {
    const scheduledReport = this.$props.connect?.scheduled_report;
    if (scheduledReport) {
      this.currentState = !!scheduledReport.enabled;
      if (scheduledReport.inclusions.length > 0) {
        this.inclusionList.forEach(item => item.isChecked = scheduledReport.inclusions.includes(item.id));
      }
      this.selectedReportingPeriod = this.reportingPeriodList.filter(item => item.value === scheduledReport.reporting_period)[0];
      this.interval = scheduledReport.interval;
      this.selectedMonthlyOption = scheduledReport.monthly_frequency ?? 'dates';
      this.selectedDay = scheduledReport.weekly_on ?? [];
      this.selectedDate = scheduledReport.monthly_on ?? [];
      this.selectedPeriod = this.frequencyList.find(item => item.value === scheduledReport.frequency) ?? this.frequencyList[0];
      this.selectedLocalTime = this.timeSlots.find(item => item.name === scheduledReport.time.slice(0, -3)) ?? this.timeSlots[0];
      this.selectedOrder = this.orderList.find(item => item.value === scheduledReport.monthly_order) ?? this.orderList[0];
      this.selectedOrderDay = this.orderDayList.find(item => item.value === scheduledReport.monthly_order_day) ?? this.orderDayList[0];
      this.customerEmails = scheduledReport.customer_emails ?? [];
    } else {
      this.currentState = false;
      this.selectedReportingPeriod = this.reportingPeriodList[0];
      this.selectedPeriod = this.frequencyList[0];
      this.selectedLocalTime = this.timeSlots[0];
      this.selectedOrder = this.orderList[0];
      this.selectedOrderDay = this.orderDayList[0];
    }
  },
  methods: {
    onIntervalChange(event) {
      const value = parseInt(event.target.value);
      if (isNaN(value) || value < 1) {
        this.interval = 1;
      } else if (value > 999) {
        this.interval = 999;
      } else {
        this.interval = value;
      }
    },
    closeModal() {
      this.$emit('closeModal');
    },
    saveData(enabled) {
      const localTime = moment(this.selectedLocalTime.name, 'HH:mm');
      const data = {
        reporting_period: this.selectedReportingPeriod.value,
        frequency: this.selectedPeriod.value,
        interval: this.interval,
        time: this.selectedLocalTime.name,
        time_utc: this.$props.connect.user.timezone ? localTime.tz(this.$props.connect.user.timezone).utc().format('HH:mm') : localTime.utc().format('HH:mm'),
        inclusions: this.inclusionList.filter(item => item.isChecked).map(item => item.id),
        weekly_on: Object.values(this.selectedDay),
        monthly_frequency: this.selectedMonthlyOption,
        monthly_on: Object.values(this.selectedDate),
        monthly_order: this.selectedOrder.value,
        monthly_order_day: this.selectedOrderDay.value,
        customer_emails: this.customerEmails,
        enabled: enabled,
      }

      axios.post(`/api/v2/connects/${this.$props.connect.id}/scheduled-reports/save`, data)
      .then(() => {
          const updatedState = data.enabled;
          let messageText;

          if (this.currentState === false && updatedState === true) {
            messageText = this.$t('scheduled_report_enabled', { url: this.$props.connect.url });
          } else if (this.currentState === true && updatedState === false) {
            if (data.inclusions.length == 0) {
              this.inclusionList.forEach(item => item.isChecked = true);
            }
            messageText = this.$t('scheduled_report_disabled', { url: this.$props.connect.url });
          } else {
            messageText = this.$t('scheduled_report_updated');
          }

          this.currentState = updatedState;

          const message = {
            heading: this.$t('success'),
            subHeading: messageText,
            type: "success",
          };
          this.appStore.setNotification(message);

          this.$emit('reportSettingsSaved', updatedState);
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
        })
    },
    onHandlePeriodChange(optionValue) {
      this.selectedReportingPeriod = optionValue;
    },
    onHandleFrequencyChange(optionValue) {
      this.selectedPeriod = optionValue;
    },
    onHandleTimeChange(optionValue) {
      this.selectedLocalTime = optionValue
    },
    onHandleOrderChange(optionValue) {
      this.selectedOrder = optionValue;
    },
    onHandleOrderDayChange(optionValue) {
      this.selectedOrderDay = optionValue;
    },
  },
}
</script>
<style>
@media (max-height: 600px) {
  .report-model {
    align-items: start !important;
  }
}

::-webkit-scrollbar {
  width: 3px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: theme('colors.primary.900');
  border-radius: 8px;
}

.test .relative div:nth-child(2) {
  bottom: 100% !important;
  top: auto !important;
}
</style>