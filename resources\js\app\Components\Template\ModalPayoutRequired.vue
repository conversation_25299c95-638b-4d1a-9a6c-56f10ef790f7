<template>
  <div>
    <DialogModal
      :show="isOpenModal"
      main-class="modal-margin"
      extra-flex="flex justify-center items-center"
      header-class="iframe-modal"
      @close="closeModal"
    >
      <template #content>
        <div>
          <div :class="$attrs['main-class']">
            <div class="text-center">
              <slot name="icon">
                <PayoutRequired />
              </slot>
              <div
                class="mb-1 text-lg font-semibold text-emerald-800"
                :class="$attrs['title-class']"
              >
                {{ $t('Payout Account Setup Incomplete') }}
              </div>
              <div
                class="mb-3 text-sm text-zinc-500 "
                :class="$attrs['subtitle-class']"
              >
                {{ $t('To sell with InstaWP you need to set up your payout account') }}.
              </div>
              <div class="mb-14 flex items-center rounded-full border px-4 py-1 text-sm font-normal text-black">
                <div>
                  <Information />
                </div>
                <span class="ml-1">{{ $t('This feature is available When you setup your Payout account') }}.</span>
              </div>
              <div class="flex gap-2">
                <!-- <button @click="handleBtnClick" :class="$attrs.buttonClass" class="py-2 px-7 shadow-sm border  rounded-md bg-white w-full"> cancel
                    <img v-if="isImageShow" class="mr-2 inline-flex items-center" :src="cdn(`images/${imageName}`)" alt="">
                    {{ buttonText }}</button> -->
                <input
                  type="button"
                  value="cancel"
                  class="block w-full cursor-pointer rounded-lg  border py-2  capitalize shadow-sm drop-shadow-md"
                  @click="$emit('cancel')"
                >
                <input
                  type="button"
                  value="Setup Payout"
                  class="medium block w-full cursor-pointer rounded-lg  border border-transparent bg-emerald-500 py-2 capitalize text-white shadow-sm drop-shadow-md"
                  @click="setupPayout"
                >
              </div>
            </div>
          </div>
        </div>
      </template>
    </DialogModal>
  </div>
</template>
<script>
import DialogModal from "@/Jetstream/DialogModal.vue";
import Information from "./Icons/Information.vue";
import PayoutRequired from "./Icons/PayoutRequired.vue";
export default {
   
   name: '3DModal',
   components: {
    DialogModal,
    PayoutRequired,
    Information
},
   props:{
    isOpenModal:{
      Type:Boolean,
      default:false,
    }
   },
   methods: {
    setupPayout(){
      this.$emit('setupPayout');
    }
  }
}
</script>
<style scoped>
.close-btn {
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.11));
}
</style>
