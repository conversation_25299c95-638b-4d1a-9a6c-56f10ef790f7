<template>
  <CommonModal
    :model-value="isOpenModal"
    size="xl"
    @close="$emit('cancel')"
  >
    <template #content>
      <div class="text-center space-y-4">
        <slot name="icon">
          <img
            :src="cdn('/images/payment-required.svg')"
            alt="Payout Required"
            class="mx-auto"
          >
        </slot>
        <div
          class="mb-1 text-lg font-semibold text-emerald-800"
          :class="$attrs['title-class']"
        >
          {{ $t('Payout Account Setup Incomplete') }}
        </div>
        <div
          class="mb-3 text-sm text-zinc-500"
          :class="$attrs['subtitle-class']"
        >
          {{ $t('To sell with InstaWP you need to set up your payout account') }}.
        </div>
        <div class="flex items-center rounded-full border px-4 py-1 text-sm font-normal text-black">
          <div>
            <OutlineInformationCircleIcon class="text-secondary-900 h-5 w-5" />
          </div>
          <span class="ml-1">{{ $t('This feature is available When you setup your Payout account') }}.</span>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex gap-3 justify-end">
        <CButton
          :btn-title="$t('Cancel')"
          btn-type="gray-outline-btn"
          @click="$emit('cancel')"
        />
        <CButton
          :btn-title="$t('Setup Payout')"
          btn-type="secondary"
          @click="setupPayout"
        />
      </div>
    </template>
  </CommonModal>
</template>
<script>
import CommonModal from "@/app/Common/CommonModal.vue";

export default {

  name: 'ModalPayoutRequired',

  components: {
    CommonModal,
  },

  props: {
    isOpenModal: {
      type: Boolean,
      default: false,
    }
  },

  emits: ['cancel', 'setupPayout'],

  methods: {
    setupPayout() {
      this.$emit('setupPayout');
    }
  }
}
</script>